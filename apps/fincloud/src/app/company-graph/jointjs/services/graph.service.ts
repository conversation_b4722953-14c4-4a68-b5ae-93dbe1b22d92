import { ElementRef, Injectable } from '@angular/core';
import { FullScreenService } from '@fincloud/core/fullscreen';
import { setTimeoutUnpatched } from '@fincloud/core/utils';
import { Store } from '@ngrx/store';
import * as joint from 'jointjs-plus';
import { dia } from 'jointjs-plus';
import { ReplaySubject, Subject, distinctUntilKeyChanged, tap } from 'rxjs';
import { CompanyGraphPageActions } from '../../+state/actions';
import { companyGraphFeature } from '../../+state/reducers/company-graph.reducer';
import { CollapseOrExpandNodeType } from '../../enums/collapse-expand-node';
import { GroupLabelTypes } from '../../enums/group-label-types';
import { NetworkElement } from '../../enums/network-node';
import { CompanyGraphLink } from '../../models/company-graph-link';
import { CompanyGraphMember } from '../../models/company-graph-member';
import { CompanyGraphModel } from '../../models/company-graph-model';
import { NodePosition } from '../../models/company-graph-node-position';
import { cellNamespace } from '../shapes';
import { BaseShape } from '../shapes/base/base.shape';
import { Container } from '../shapes/container/container.shape';
import { GroupLabel } from '../shapes/group-label/group-label.shape';
import { createGraphPaper, createScroller } from '../utils';
import { getShapeType } from '../utils/get-sahpe-opposite-type';
import { RadialGraphLayoutService } from './radial-graph-layout.service';

@Injectable()
export class GraphService {
  private readonly zoomScale$$ = new ReplaySubject<number>(1);
  private readonly scaleFactor = { min: 0.7, max: 3 };
  private readonly scaleStep = 0.01;
  private readonly scaleData$$ = new Subject<{
    initialScale: number;
    maxScale: number;
    minScale: number;
  }>();

  private graphContainerElement: ElementRef<HTMLDivElement>;
  private paperScroller: joint.ui.PaperScroller;
  private savedPositions: NodePosition[] = [];
  private graphData: CompanyGraphModel = [];
  private locationTooltip: joint.ui.Tooltip;
  private nameTooltip: joint.ui.Tooltip;
  private edgeTooltip: joint.ui.Tooltip;
  private groupTooltip: joint.ui.Tooltip;
  private prevElementId: string = null;
  private graph: dia.Graph;
  private collapsedGroups: string[];
  private selectedNodeId: string | null;
  private singleExpandedNodeIds: string[] = [];

  readonly scaleData$ = this.scaleData$$.asObservable();
  readonly zoomScale$ = this.zoomScale$$.asObservable();

  graphNestingLevel$ = this.radialGraphLayout.graphNestingLevel$;
  paper: dia.Paper;

  constructor(
    private store: Store,
    private radialGraphLayout: RadialGraphLayoutService,
    private fullscreenService: FullScreenService,
  ) {}

  initGraph(
    graphElement: ElementRef<HTMLDivElement>,
    graphModel: {
      graphData: CompanyGraphModel;
      savedPositions: NodePosition[];
      collapsedGroups: string[];
      selectedNodeId: string | null;
      singleExpandedNodeIds: string[];
    },
  ) {
    // Load data in the graph
    this.createGraphAndPaper();
    this.setupData(
      graphElement,
      graphModel.graphData,
      graphModel.savedPositions,
      graphModel.collapsedGroups,
      graphModel.singleExpandedNodeIds,
    );
    this.selectedNodeId = graphModel.selectedNodeId;
    this.prevElementId = this.selectedNodeId;
  }

  fitAndCenterGraphContent(
    graphElement: HTMLDivElement | undefined,
    zoomToFit = true,
  ): void {
    if (zoomToFit) {
      this.paperScroller?.zoomToFit({
        useModelGeometry: true,
        fittingBBox: {
          x: 0,
          y: 0,
          width: graphElement?.clientWidth || Number(this.paper.options.width),
          height:
            graphElement?.clientHeight || Number(this.paper.options.height),
        },
        preserveAspectRatio: true,
        padding: 20,
      });
    }
    this.paperScroller?.centerContent({ useModelGeometry: true });
  }

  cleanup() {
    this.graph?.clear();
    this.graph?.off();
    this.paper?.remove();
    this.paperScroller = undefined;
  }

  subscribeViewLayoutChange() {
    return this.store
      .select(companyGraphFeature.selectIsCompactViewSelectedNode)
      .pipe(
        distinctUntilKeyChanged('isCompactView'),
        tap(({ selectedNode }) => {
          this.selectedNodeId = selectedNode?.id;
        }),
        tap(({ isCompactView }) => {
          this.radialGraphLayout.isRootCellCompact = isCompactView;
          if (this.graph.getCells().length) {
            // The graph is loaded and compact view button is clicked
            this.resetMarkupTypes();
            setTimeoutUnpatched(() => {
              this.radialGraphLayout.dispatchNodePositions();
            }, 10);
          } else {
            // On page load
            if (isCompactView) {
              this.resetShapeTypes();
            }

            this.initRadialGraphLayout(this.graphData);
            this.setupPaperScroller();
            this.setInitialPositions();
          }
        }),
      );
  }

  initRadialGraphLayout(
    graphData: CompanyGraphModel,
    isInitial?: boolean,
  ): void {
    this.radialGraphLayout.loadRadialGraphLayout(
      this.graph,
      this.paper,
      graphData,
      this.savedPositions,
      this.collapsedGroups,
      this.singleExpandedNodeIds,
      isInitial,
    );
  }

  setInitialPositions(): void {
    // We need timeout here for graphs with nodes without saved positions
    setTimeoutUnpatched(() => {
      this.updatePaper(
        this.graphContainerElement.nativeElement,
        this.fullscreenService.isFullScreen,
      );
      this.mouseWheelZoomFunctionality();
      //set node positions graphConfiguration
      this.radialGraphLayout.dispatchNodePositions();
      this.nodePositionChangeListener();
    }, 0);
  }

  expandSelectedNode(selectedNodeId: string | null): void {
    this.radialGraphLayout.expandSelectedNode(selectedNodeId);
  }

  collapseSelectedNode(id?: string): void {
    this.onClickAway();
    this.radialGraphLayout.collapseSelectedNode(id);
  }

  updateGroupsPositionsAndExpandedNodeIds(
    collapsedGroups: string[],
    nodePositions: NodePosition[],
    singleExpandedNodeIds: string[],
  ): void {
    this.collapsedGroups = collapsedGroups;
    this.savedPositions = nodePositions;
    this.singleExpandedNodeIds = singleExpandedNodeIds;
  }

  getGraphJSONData() {
    return this.graph.getCells();
  }

  applyFilteredGraphJSONData(graphData: dia.Cell[]) {
    this.radialGraphLayout.applyFilteredGraphJSONData(graphData, this.paper);
  }

  private createGraphAndPaper(): void {
    this.graph = new joint.dia.Graph({}, { cellNamespace });
    this.paper = createGraphPaper(this.graph);
  }

  /**
   * Saves passed data to a local variables
   */
  private setupData(
    graphElement: ElementRef<HTMLDivElement>,
    graphModel: CompanyGraphModel,
    savedPositions: NodePosition[],
    collapsedGroups: string[],
    singleExpandedNodeIds: string[],
  ) {
    this.graphContainerElement = graphElement;
    this.graphData = graphModel;
    this.savedPositions = savedPositions;
    this.collapsedGroups = collapsedGroups;
    this.singleExpandedNodeIds = singleExpandedNodeIds;
  }

  private resetShapeTypes(): void {
    this.graphData.forEach((element: CompanyGraphLink | CompanyGraphMember) => {
      element.type = getShapeType(element.type);
    });
  }

  private resetMarkupTypes(): void {
    this.paper.freeze();
    this.graph.getElements().forEach((cell: any) => {
      if (typeof cell.changeMarkupType === 'function') {
        const type = getShapeType(cell.attributes.type);

        cell.attributes.type = type;
        cell.changeMarkupType(cell.attributes);
      }
    });
    this.graph.resetCells(this.graph.getCells());

    this.paper.unfreeze();
  }

  private setupPaperScroller(): void {
    if (!this.paperScroller) {
      this.paperScroller = createScroller(this.paper);

      this.graphContainerElement.nativeElement.appendChild(
        this.paperScroller.render().el,
      );
    }
  }

  private mouseWheelZoomFunctionality(): void {
    const initialScale = this.paper.scale().sx;
    const minScale = initialScale * this.scaleFactor.min;
    const maxScale = Math.max(initialScale * this.scaleFactor.max, 1);
    this.zoomScale$$.next(initialScale);
    this.scaleData$$.next({
      initialScale,
      maxScale,
      minScale,
    });
    this.paper.off('blank:mousewheel');
    this.paper.on('blank:mousewheel', (evt, x, y, delta) => {
      evt.preventDefault();
      this.scale(delta, minScale, maxScale);
    });
    this.paper.off('cell:mousewheel');
    this.paper.on('cell:mousewheel', (_, evt, x, y, delta) => {
      evt.preventDefault();
      this.scale(delta, minScale, maxScale);
    });
  }

  private scale(delta: number, minScale: number, maxScale: number): void {
    const oldscale = this.paper.scale().sx;
    const newscale = oldscale + this.scaleStep * delta * oldscale;

    if (newscale >= minScale && newscale <= maxScale) {
      this.paper.scale(newscale, newscale, 0);
      this.zoomScale$$.next(newscale);
    }
  }

  private updatePaper(
    graphElement: HTMLDivElement,
    isFullScreen?: boolean,
  ): void {
    this.fitAndCenterGraphContent(graphElement);
    this.paper.off('element:pointerclick');
    this.paper.on(
      'element:pointerclick',
      this.onElementPointerClick.bind(this),
    );

    this.paper.off('blank:pointerdown');
    this.paper.on('blank:pointerdown', (evt: dia.Event) =>
      this.paperScroller?.startPanning(evt),
    );

    this.paper.off('blank:pointerclick');
    this.paper.on('blank:pointerclick', this.onClickAway.bind(this));

    this.createTooltip(isFullScreen);

    this.paper.showTools();
  }

  private findAndUnhighlightPrev() {
    const element = this.graph
      .getElements()
      .find((el) => el.id === this.prevElementId) as BaseShape;
    element?.unhighlight();
  }

  private findAndUnhighlightAll() {
    this.graph.getElements().forEach((e) => {
      (e as BaseShape).unhighlight();
    });
  }

  private onClickAway() {
    if (this.prevElementId) {
      this.findAndUnhighlightAll();

      this.prevElementId = null;

      this.store.dispatch(
        CompanyGraphPageActions.selectNode({
          id: this.prevElementId,
          nodeOriginId: null,
          availabilityType: CollapseOrExpandNodeType.NOT_AVAILABLE,
        }),
      );
    }
  }

  private handleGroupClicked(evt: dia.ElementView) {
    const groupId = evt.model.id.toString();
    const element = this.graph
      .getElements()
      .filter((el) => el instanceof Container)
      .find((container) => container.attributes.groupLabelId === groupId);

    if (!element) {
      return;
    }

    if (evt.model.attributes.type.includes('EXPANDED')) {
      (evt.model as any).changeMarkupType({
        ...evt.model.attributes,
        type: GroupLabelTypes.COLLAPSED,
      });
    } else {
      (evt.model as any).changeMarkupType({
        ...evt.model.attributes,
        type: GroupLabelTypes.EXPANDED,
      });
    }
    this.radialGraphLayout.toggleGroup(
      groupId,
      this.graph,
      this.paper,
      element as Container,
    );
  }

  private onElementPointerClick(evt: dia.ElementView) {
    const isGroupLabel = evt.model instanceof GroupLabel;
    if (isGroupLabel) {
      this.handleGroupClicked(evt);
      return;
    }

    if (evt.model.get('type') === NetworkElement.NODE_CONTAINER) {
      return;
    }

    let id = null;
    let originalElementId = null;
    const elementIsSelected = evt.model.attributes.selected;

    if (!elementIsSelected) {
      (evt.model as BaseShape).highlight();
      id = evt.model.attributes.id;
      originalElementId = evt.model.attributes.originID || id;
    } else {
      (evt.model as BaseShape).unhighlight();
    }

    if (this.prevElementId && id !== this.prevElementId) {
      this.findAndUnhighlightPrev();
    }

    this.prevElementId = id;

    this.store.dispatch(
      CompanyGraphPageActions.selectNode({
        id: id,
        nodeOriginId: originalElementId,
        availabilityType: this.radialGraphLayout.checkForShadowGraph(
          originalElementId,
          id,
        ),
      }),
    );
  }

  createTooltip(isFullScreen = false): void {
    // Having the exact template here and setting max-width here is important. Otherwise, the tooltip position for big graphs is not correct
    this.nameTooltip?.remove();
    this.locationTooltip?.remove();
    this.edgeTooltip?.remove();
    this.groupTooltip?.remove();

    this.nameTooltip = new joint.ui.Tooltip({
      rootTarget: this.paper.svg,
      container: isFullScreen ? '#canvas' : '',
      target: '[shape-name-data-tooltip]',
      position: joint.ui.Tooltip.TooltipPosition.Bottom,
      viewport: { selector: this.graphContainerElement.nativeElement },
      padding: 5,
      template: `<div class="tw-max-w-[32rem] tooltip-content"></div>`,
      content: (portNode: HTMLElement) => {
        const text = portNode.getAttribute('shape-name-data-tooltip');
        return text;
      },
    });

    this.locationTooltip = new joint.ui.Tooltip({
      rootTarget: this.paper.svg,
      container: isFullScreen ? '#canvas' : '',
      target: '[shape-location-data-tooltip]',
      position: joint.ui.Tooltip.TooltipPosition.Top,
      viewport: { selector: this.graphContainerElement.nativeElement },
      padding: 5,
      template: `<div class="tw-max-w-[32rem] tooltip-content"></div>`,
      content: (portNode: HTMLElement) => {
        const text = portNode.getAttribute('shape-location-data-tooltip');
        return text;
      },
    });

    this.edgeTooltip = new joint.ui.Tooltip({
      rootTarget: this.paper.svg,
      container: isFullScreen ? '#canvas' : '',
      target: '[edge-label-data-tooltip]',
      position: joint.ui.Tooltip.TooltipPosition.Bottom,
      viewport: { selector: this.graphContainerElement.nativeElement },
      padding: 5,
      template: `<div class="tw-max-w-[32rem] tooltip-content"></div>`,
      content: (portNode: HTMLElement) => {
        const text = portNode.getAttribute('edge-label-data-tooltip');
        return text;
      },
    });

    this.groupTooltip = new joint.ui.Tooltip({
      rootTarget: this.paper.svg,
      container: isFullScreen ? '#canvas' : '',
      target: '[group-data-tooltip]',
      position: joint.ui.Tooltip.TooltipPosition.Bottom,
      viewport: { selector: this.graphContainerElement.nativeElement },
      padding: 5,
      template: `<div class="tw-max-w-[32rem] tooltip-content"></div>`,
      content: (portNode: HTMLElement) => {
        const text = portNode.getAttribute('group-data-tooltip');
        return text;
      },
    });
  }

  private nodePositionChangeListener() {
    this.graph.off('element:pointerup');
    this.paper.on('element:pointerup', (elementView: dia.ElementView) => {
      if (elementView.model.changed.position) {
        this.radialGraphLayout.dispatchNodePositions();
      }
    });
  }
}
