import { PaginatorIntlService } from '@fincloud/core/services';

import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class CompanyGraphPaginatorIntlService extends PaginatorIntlService {
  override getCompactRangeLabel: (
    page: number,
    pageSize: number,
    length: number,
  ) => string = (page: number, pageSize: number, length: number) => {
    const startItem = (page - 1) * pageSize + 1;

    return $localize`:@@companyGraph.paginator.compactRangeLabel:${startItem} von ${length} Änderungen`;
  };

  constructor() {
    super();
  }
}
