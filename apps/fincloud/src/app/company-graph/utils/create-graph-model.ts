import { NetworkElement } from '../enums/network-node';
import { CompanyGraphEdge } from '../models/company-graph-edge';
import { CompanyGraphMemberAttrs } from '../models/company-graph-member-attrs';
import { CompanyGraphModel } from '../models/company-graph-model';
import { CompanyGraphNode } from '../models/company-graph-node';
import { NodePosition } from '../models/company-graph-node-position';

export const createGraphModel = (
  edges: CompanyGraphEdge[],
  nodes: CompanyGraphNode[],
  positions: NodePosition[] = [],
  selectedNodeId: string | null = null,
): { graphData: CompanyGraphModel; savedPositions: NodePosition[] } => {
  const links = edges
    .map((edge) => {
      if (edge.fromId && edge.toId) {
        let type = NetworkElement.COMPANY_LINK;

        if (edge.fromType === NetworkElement.PERSON) {
          type = NetworkElement.PERSON_LINK;
        }

        if (edge.fromType === NetworkElement.UBO) {
          type = NetworkElement.PERSON_LINK;
          // TODO ubo link
        }

        return {
          type,
          id: edge.id,
          source: { id: edge.toId },
          target: { id: edge.fromId },
          sharesPercent: edge.sharesPercent,
          description: edge.description,
        };
      }
      return null;
    })
    .filter((item) => item !== null);

  const members = nodes.map((node) => {
    const { id, type, ...filteredNode } = node;

    const nodeType = type;

    const nodeAttrs = {} as CompanyGraphMemberAttrs;

    Object.entries(filteredNode).forEach(([key, value]) => {
      nodeAttrs[key as keyof typeof filteredNode] = { text: value } as never;
    });

    return {
      id,
      type: nodeType,
      attrs: { ...nodeAttrs },
      selected: selectedNodeId === node.id,
      position: { x: 0, y: 0 },
    };
  });

  return { graphData: [...members, ...links], savedPositions: positions };
};
