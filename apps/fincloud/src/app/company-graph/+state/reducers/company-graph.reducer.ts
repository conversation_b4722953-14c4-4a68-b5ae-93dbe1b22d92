import {
  CompanyNetworkDto,
  CompanyUbosDto,
} from '@fincloud/swagger-generator/company';
import { createFeature, createReducer, on } from '@ngrx/store';
import { CollapseOrExpandNodeType } from '../../enums/collapse-expand-node';
import { CompanyGraphChangeDto } from '../../models/company-graph-change-dto';
import { GraphConfiguration } from '../../models/company-graph-configuration';
import { CompanyGraphState } from '../../models/company-graph-state';
import { CompanyNetwork } from '../../models/company-network-dto';
import { COMPANY_GRAPH_DEFAULT_FILTERS } from '../../utils/company-graph-default-filters';
import { mergeUBOandCurrentCompany } from '../../utils/merge-ubo-graph';
import { updateNodePositions } from '../../utils/update-node-positions';
import { CompanyGraphApiActions, CompanyGraphPageActions } from '../actions';
import { companyGraphSelectors } from '../selectors/company-graph.selectors';

const initialState: CompanyGraphState = {
  companyNetwork: {} as CompanyNetwork,
  companyNetworkDto: {} as CompanyNetworkDto,
  companyCalculatedUboDto: [],
  companyUboDto: {} as CompanyUbosDto,
  graphConfiguration: {
    companyGraphState: {
      isCompactView: false,
      selectedNodeId: '',
      nodePositions: [],
      collapsedGroups: [],
      singleExpandedNodeIds: [],
      selectedNodeOriginId: null,
      hasLoadedSettings: false,
    },
  } as GraphConfiguration,
  companyChanges: {} as CompanyGraphChangeDto,
  currentChangeIndex: 0,
  error: {} as unknown,
  levelsCount: 0,
  isInitial: true,
  isLoading: true,
  filters: COMPANY_GRAPH_DEFAULT_FILTERS,
  hasNoFilterResults: false,
  availableRoles: [],
  availableEntityTypes: [],
  isCompactView: false,
  selectedNodeNestingAvailability: CollapseOrExpandNodeType.NOT_AVAILABLE,
  companyId: '',
  hasNoGraphData: false,
};

export const companyGraphFeature = createFeature({
  name: 'companyGraph',
  reducer: createReducer<CompanyGraphState>(
    initialState,
    on(
      CompanyGraphPageActions.getCompanyNetwork,
      (state, { companyId }): CompanyGraphState => {
        return {
          ...state,
          ...initialState,
          companyId,
          isLoading: true,
        };
      },
    ),
    on(
      CompanyGraphApiActions.getCompanyNetworkFailure,
      (state, { error }): CompanyGraphState => ({
        ...state,
        ...initialState,
        error,
        companyId: state.companyId,
        hasNoGraphData: true,
      }),
    ),
    on(
      CompanyGraphPageActions.toggleCompactView,
      (state, { isCompactView }): CompanyGraphState => ({
        ...state,
        graphConfiguration: {
          companyGraphState: {
            ...state.graphConfiguration.companyGraphState,
            isCompactView,
          },
        },
      }),
    ),
    on(
      CompanyGraphPageActions.selectNode,
      (state, { id, nodeOriginId, availabilityType }): CompanyGraphState => ({
        ...state,
        graphConfiguration: {
          companyGraphState: {
            ...state.graphConfiguration.companyGraphState,
            selectedNodeId: id,
            selectedNodeOriginId: nodeOriginId,
          },
        },
        selectedNodeNestingAvailability: availabilityType,
      }),
    ),
    on(
      CompanyGraphPageActions.filterGraph,
      (state, { filters }): CompanyGraphState => ({
        ...state,
        isLoading: true,
        graphConfiguration: {
          companyGraphState: {
            ...state.graphConfiguration.companyGraphState,
            selectedNodeId:
              initialState.graphConfiguration.companyGraphState.selectedNodeId,
            selectedNodeOriginId:
              initialState.graphConfiguration.companyGraphState
                .selectedNodeOriginId,
          },
        },
        selectedNodeNestingAvailability: CollapseOrExpandNodeType.NOT_AVAILABLE,
        filters: {
          ...filters,
          roles: filters.roles || [],
          entities: filters.entities || [],
        },
      }),
    ),
    on(
      CompanyGraphPageActions.filterSelectOptions,
      (state, { availableRoles, availableEntityTypes }): CompanyGraphState => {
        return {
          ...state,
          availableRoles,
          availableEntityTypes,
        };
      },
    ),
    on(
      CompanyGraphApiActions.getUserGraphSettingsSuccess,
      (state, { graphConfiguration }): CompanyGraphState => {
        return {
          ...state,
          graphConfiguration: {
            ...state.graphConfiguration,
            ...graphConfiguration,
            companyGraphState: {
              ...state.graphConfiguration?.companyGraphState,
              ...graphConfiguration?.companyGraphState,
              hasLoadedSettings: true,
            },
          },
          isInitial: false,
        };
      },
    ),
    on(
      CompanyGraphApiActions.getUserGraphSettingsFailure,
      (state): CompanyGraphState => ({
        ...state,
        graphConfiguration: {
          companyGraphState: {
            ...state.graphConfiguration.companyGraphState,
            hasLoadedSettings: true,
          },
        },
        isLoading: false,
      }),
    ),
    on(
      CompanyGraphPageActions.updateNodePosition,
      (state, { nodePositions }): CompanyGraphState => ({
        ...state,
        graphConfiguration: {
          ...state.graphConfiguration,
          companyGraphState: {
            ...state.graphConfiguration.companyGraphState,
            nodePositions: updateNodePositions(
              state.graphConfiguration.companyGraphState.nodePositions,
              nodePositions,
            ),
          },
        },
      }),
    ),
    on(
      CompanyGraphPageActions.setGraphLevel,
      (state, { levelsCount }): CompanyGraphState => ({
        ...state,
        levelsCount,
      }),
    ),
    on(
      CompanyGraphPageActions.toggleNestingLevel,
      (state, { nestingLevel }): CompanyGraphState => ({
        ...state,
        graphConfiguration: {
          companyGraphState: {
            ...state.graphConfiguration.companyGraphState,
            nestingLevel,
          },
        },
      }),
    ),
    on(
      CompanyGraphPageActions.expandGroup,
      (state, { groupId }): CompanyGraphState => {
        return {
          ...state,
          graphConfiguration: {
            companyGraphState: {
              ...state.graphConfiguration.companyGraphState,
              collapsedGroups: [
                ...state.graphConfiguration.companyGraphState.collapsedGroups,
                groupId,
              ],
            },
          },
        };
      },
    ),
    on(
      CompanyGraphPageActions.collapseGroup,
      (state, { groupId }): CompanyGraphState => {
        return {
          ...state,
          graphConfiguration: {
            companyGraphState: {
              ...state.graphConfiguration.companyGraphState,
              collapsedGroups:
                state.graphConfiguration.companyGraphState.collapsedGroups.filter(
                  (id) => id !== groupId,
                ),
            },
          },
        };
      },
    ),
    on(
      CompanyGraphPageActions.updateSingleExpandedNodeIds,
      (state, { nodeIds }): CompanyGraphState => {
        return {
          ...state,
          graphConfiguration: {
            companyGraphState: {
              ...state.graphConfiguration.companyGraphState,
              singleExpandedNodeIds: [...nodeIds],
            },
          },
        };
      },
    ),
    on(
      CompanyGraphPageActions.expandNodeConfirm,
      (state): CompanyGraphState => {
        return {
          ...state,
          selectedNodeNestingAvailability: CollapseOrExpandNodeType.COLLAPSE,
        };
      },
    ),
    on(
      CompanyGraphPageActions.updateGraphLoadingState,
      (state, { isLoading }): CompanyGraphState => {
        return {
          ...state,
          isLoading,
        };
      },
    ),
    on(
      CompanyGraphApiActions.getCompanyChangesFailure,
      (state, { error }): CompanyGraphState => {
        return { ...state, error };
      },
    ),
    on(
      CompanyGraphApiActions.getCompanyChangesSuccess,
      (state, { companyChanges }): CompanyGraphState => {
        return {
          ...state,
          companyChanges,
        };
      },
    ),
    on(
      CompanyGraphPageActions.updateChangeCurrentIndex,
      (state, { currentChangeIndex }): CompanyGraphState => {
        return {
          ...state,
          currentChangeIndex,
        };
      },
    ),
    on(
      CompanyGraphPageActions.rejectReviewChanges,
      CompanyGraphPageActions.acceptAllReviewChanges,
      CompanyGraphPageActions.closeReviewChanges,
      (state): CompanyGraphState => {
        return {
          ...state,
          currentChangeIndex: initialState.currentChangeIndex,
        };
      },
    ),
    on(
      CompanyGraphApiActions.acceptReviewChangesSuccess,
      (state, { id }): CompanyGraphState => {
        const existingChange = state?.companyChanges?.changes?.find(
          (change) => change?.id === id,
        );

        if (!existingChange) {
          // Handle the case where no matching change is found
          return { ...state };
        }

        // Clone and update the found change
        const updatedChange = structuredClone(existingChange);
        updatedChange.updated = true;

        return {
          ...state,
          companyChanges: {
            changes: state.companyChanges.changes.map((change) =>
              change.id === id ? updatedChange : change,
            ),
          },
        };
      },
    ),
    on(
      CompanyGraphApiActions.acceptAllReviewChangesSuccess,
      (state): CompanyGraphState => {
        return {
          ...state,
          companyChanges: {
            ...initialState.companyChanges,
          },
        };
      },
    ),
    on(CompanyGraphPageActions.nextChangeIndex, (state): CompanyGraphState => {
      const length = state.companyChanges.changes.length;
      const currentChangeIndex = state.currentChangeIndex + 1;
      if (currentChangeIndex >= length) {
        return state;
      }

      return {
        ...state,
        currentChangeIndex,
      };
    }),
    on(
      CompanyGraphPageActions.previousChangeIndex,
      (state): CompanyGraphState => {
        const currentChangeIndex = state.currentChangeIndex - 1;
        if (state.currentChangeIndex < 1) {
          return state;
        }

        return {
          ...state,
          currentChangeIndex,
        };
      },
    ),
    on(
      CompanyGraphApiActions.getCompanyNetworkDtoSuccess,
      (state, { companyNetworkDto }): CompanyGraphState => {
        return {
          ...state,
          companyNetworkDto,
        };
      },
    ),
    on(
      CompanyGraphApiActions.getCompanyUboDtoSuccess,
      (state, { companyUboDto }): CompanyGraphState => {
        const companyNetwork = mergeUBOandCurrentCompany(
          state.companyNetworkDto as CompanyNetwork,
          state.companyCalculatedUboDto,
          companyUboDto as CompanyNetwork,
          state.companyNetworkDto.generatedCompanyId,
        );
        return {
          ...state,
          companyUboDto,
          companyNetwork,
          hasNoGraphData: !companyNetwork.nodes.length,
        };
      },
    ),
    on(
      CompanyGraphApiActions.getCompanyUboDtoFailure,
      (state): CompanyGraphState => {
        const companyNetwork = mergeUBOandCurrentCompany(
          state.companyNetworkDto as CompanyNetwork,
          state.companyCalculatedUboDto,
          initialState.companyUboDto as CompanyNetwork,
          state.companyNetworkDto.generatedCompanyId,
        );
        return {
          ...state,
          companyUboDto: initialState.companyUboDto,
          companyNetwork,
          hasNoGraphData: !companyNetwork.nodes.length,
        };
      },
    ),
    on(
      CompanyGraphApiActions.getCompanyCalculatedUboDtoSuccess,
      (state, { companyCalculatedUboDto }): CompanyGraphState => {
        const companyNetwork = mergeUBOandCurrentCompany(
          state.companyNetworkDto as CompanyNetwork,
          companyCalculatedUboDto,
          state.companyUboDto as CompanyNetwork,
          state.companyNetworkDto.generatedCompanyId,
        );
        return {
          ...state,
          companyCalculatedUboDto,
          companyNetwork,
          hasNoGraphData: !companyNetwork.nodes.length,
        };
      },
    ),
    on(
      CompanyGraphApiActions.getCompanyCalculatedUboDtoFailure,
      (state): CompanyGraphState => {
        const companyNetwork = mergeUBOandCurrentCompany(
          state.companyNetworkDto as CompanyNetwork,
          initialState.companyCalculatedUboDto,
          state.companyUboDto as CompanyNetwork,
          state.companyNetworkDto.generatedCompanyId,
        );
        return {
          ...state,
          companyCalculatedUboDto: initialState.companyCalculatedUboDto,
          companyNetwork,
          hasNoGraphData: !companyNetwork.nodes.length,
        };
      },
    ),
    on(
      CompanyGraphPageActions.updateGraphDataAvailability,
      (state, { hasNoGraphData }): CompanyGraphState => {
        return {
          ...state,
          hasNoGraphData,
        };
      },
    ),
    on(
      CompanyGraphPageActions.updateLoadingStateAndNoFilterResults,
      (state, { isLoading, hasNoFilterResults }): CompanyGraphState => {
        return { ...state, isLoading, hasNoFilterResults };
      },
    ),
  ),
  extraSelectors: companyGraphSelectors,
});
