import { createAction, props } from '@ngrx/store';
import { CollapseOrExpandNodeType } from '../../enums/collapse-expand-node';
import { CompanyGraphFilters } from '../../models/company-graph-filters';
import { NodePosition } from '../../models/company-graph-node-position';

export const getCompanyNetwork = createAction(
  '[Network Page] Get company graph data',
  props<{ companyId: string }>(),
);

export const getCompanyUBO = createAction(
  '[Network Page] Get company UBO',
  props<{ companyId: string }>(),
);

export const getCompanyCalculatedUBO = createAction(
  '[Network Page] Get company calculated UBO',
  props<{ companyId: string }>(),
);

export const selectNode = createAction(
  '[Network Page] Select node from graph',
  props<{
    id: string;
    nodeOriginId: string;
    availabilityType: CollapseOrExpandNodeType;
  }>(),
);

export const toggleCompactView = createAction(
  '[Network Page] Toggle compact view',
  props<{ isCompactView: boolean }>(),
);

export const filterGraph = createAction(
  '[Network Page] Filter graph',
  props<{
    filters: CompanyGraphFilters;
  }>(),
);

export const filterSelectOptions = createAction(
  '[Network Page] Filter select options',
  props<{
    availableRoles: string[];
    availableEntityTypes: string[];
  }>(),
);

export const updateNodePosition = createAction(
  '[Network Page] Update node position',
  props<{ nodePositions: NodePosition[] }>(),
);

export const setGraphLevel = createAction(
  '[Network Page] Set graph level of nesting',
  props<{ levelsCount: number }>(),
);

export const toggleNestingLevel = createAction(
  '[Network Page] Toggle graph nesting level',
  props<{ nestingLevel: number }>(),
);

export const expandNode = createAction('[Network Page] Expand node');

export const expandNodeConfirm = createAction(
  '[Network Page] Expand node confirm',
);
export const expandNodeCancel = createAction(
  '[Network Page] Expand node cancel',
);

export const expandReviewChanges = createAction(
  '[Network Page] Expand review changes',
);

export const rejectReviewChanges = createAction(
  '[Network Page] Reject review changes',
);

export const closeReviewChanges = createAction(
  '[Network Page] Close changes review',
);

export const rejectAllReviewChanges = createAction(
  '[Network Page] Reject all review changes',
);

export const acceptReviewChanges = createAction(
  '[Network Page] Accept review changes',
);

export const acceptAllReviewChanges = createAction(
  '[Network Page] Accept all review changes',
);

export const updateChangeCurrentIndex = createAction(
  '[Network Page] Update change current index',
  props<{
    currentChangeIndex: number;
  }>(),
);

export const previousChangeIndex = createAction(
  '[Network Page] Previous change index',
);

export const nextChangeIndex = createAction('[Network Page] next change index');

export const collapseNode = createAction('[Network Page] Collapse node');

export const collapseNodeConfirm = createAction(
  '[Network Page] Collapse node confirm',
);
export const collapseNodeCancel = createAction(
  '[Network Page] Collapse node cancel',
);

export const expandGroup = createAction(
  '[Network Page] Expand group',
  props<{
    groupId: string;
  }>(),
);

export const collapseGroup = createAction(
  '[Network Page] Collapse group',
  props<{
    groupId: string;
  }>(),
);

export const updateSingleExpandedNodeIds = createAction(
  '[Network Page] Update single expanded node Ids',
  props<{ nodeIds: string[] }>(),
);

export const updateGraphLoadingState = createAction(
  '[Network Page] Update graph loading state',
  props<{ isLoading: boolean }>(),
);

export const updateGraphDataAvailability = createAction(
  '[Network Page] Update graph data availability',
  props<{ hasNoGraphData: boolean }>(),
);

export const updateLoadingStateAndNoFilterResults = createAction(
  '[Network Page] Update loading state and filter results',
  props<{ isLoading: boolean; hasNoFilterResults: boolean }>(),
);
