import { Component, DestroyRef, OnInit, ViewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import { TableColumn, TableComponent } from '@fincloud/components/lists';
import { ScrollCommunicationService } from '@fincloud/core/scroll';
import { AddressHelperService } from '@fincloud/core/services';
import { selectUserCustomerKey } from '@fincloud/state/user';
import {
  Company,
  CompanyControllerService,
  ContactPerson,
} from '@fincloud/swagger-generator/company';
import { Dictionary, TableRow } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { Observable, of } from 'rxjs';
import { finalize, map, tap } from 'rxjs/operators';
import { COMPANY_TABLE_COLUMNS_CONFIG } from '../../utils/company-table-columns';

@Component({
  selector: 'app-company',
  templateUrl: './company.component.html',
  styleUrls: ['./company.component.scss'],
})
export class CompanyComponent implements OnInit {
  @ViewChild('table', { static: true }) table: TableComponent;

  columns: TableColumn[] = COMPANY_TABLE_COLUMNS_CONFIG;
  companyToEdit: Company;
  expandedRow: TableRow | null;
  searchTerm: string;
  isCreateCompanyOpened = false;
  rows$: Observable<TableRow[]>;
  loaded = false;
  companiesContactPeopleByCompanyId: Dictionary<ContactPerson[]>;
  companies: Company[];
  searchFilterFocused$ = of(true);
  initiallyLoadedCompanyId: string;
  tableRows: TableRow[] = [];

  customerKey$ = this.store.select(selectUserCustomerKey);

  constructor(
    private companyService: CompanyControllerService,
    private addressHelperService: AddressHelperService,
    private store: Store,
    private activatedRoute: ActivatedRoute,
    private scrollCommunicationService: ScrollCommunicationService,
    private destroyRef: DestroyRef,
  ) {}

  ngOnInit() {
    this.initiallyLoadedCompanyId =
      this.activatedRoute.snapshot.queryParams['companyId'];

    this.getFreshData();
  }

  getFreshData() {
    this.companyService
      .getCompaniesForCustomer({
        showInactiveCompanies: true,
      })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((companies) => {
        this.getTableData(companies);
      });
  }

  getTableData(companies: Company[]) {
    this.rows$ = of(companies).pipe(
      map((companies) =>
        companies.map((cmp: Company) => {
          this.companies = companies;
          return this.mapToTableRow(cmp);
        }),
      ),
      tap((tableRows) => {
        this.tableRows = tableRows;
      }),
      finalize(() => {
        this.loaded = true;
        if (this.initiallyLoadedCompanyId?.length) {
          const foundInitialCompany = this.tableRows.find(
            (company: TableRow) => company.id === this.initiallyLoadedCompanyId,
          );

          this.initiallyLoadedCompanyId = null;

          if (foundInitialCompany) {
            this.openEditCompany(foundInitialCompany);

            // TODO: overhaul check
            this.scrollCommunicationService.scrollToElementById(
              this.initiallyLoadedCompanyId,
            );
          }
        }
      }),
    );
  }

  updateRow(row: TableRow) {
    this.companyService
      .getCompanyById({ id: row.id as string })
      .subscribe((updatedCompany) => {
        Object.assign(row, this.mapToTableRow(updatedCompany));
        const updatedCompanyIndex = this.companies.findIndex(
          (company) => company.id == updatedCompany.id,
        );
        this.companies[updatedCompanyIndex] = updatedCompany;
      });
  }

  updateRowStatus(enabled: boolean, row: TableRow) {
    row.enabled = enabled;

    const updatedCompanyIndex = this.companies.findIndex(
      (company) => company.id == row.id,
    );
    this.companies[updatedCompanyIndex].companyState = enabled
      ? 'ACTIVE'
      : 'DELETED';
  }

  getCompany(companyId: string) {
    return this.companies.find((c: Company) => c.id === companyId);
  }

  openEditCompany(row: TableRow) {
    if (this.expandedRow) {
      this.collapseRow(this.expandedRow);
    }
    this.expandedRow = row;
    this.companyToEdit = this.getCompany(row.id as string);
    this.table.toggleRowDetail(row);
  }

  collapseRow(row: TableRow) {
    this.companyToEdit = null;
    this.table.toggleRowDetail(row as TableRow);
    this.expandedRow = null;
  }

  updateSearchTerm(searchTerm: string) {
    this.searchTerm = searchTerm;
  }

  private mapToTableRow(company: Company): TableRow {
    return {
      id: company.id,
      firmName: company.companyInfo?.legalName,
      address: this.addressHelperService.asCompanyAddressString(
        company?.address,
      ),
      enabled: company.companyState === 'ACTIVE',
    };
  }
}
