import { Injectable } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ConfirmationDialogComponent } from '@fincloud/components/modals';
import { ModalService } from '@fincloud/core/modal';
import { Snapshot } from '@fincloud/swagger-generator/demo';
import { SnapshotVersionState } from '@fincloud/types/enums';
import { AppState } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { Observable, map, race, take } from 'rxjs';
import { DemoSnapshotCopyPageActions } from '../+state/actions';
import { SelectVersionForCopyModalComponent } from '../components/select-version-for-copy-modal/select-version-for-copy-modal.component';
import { SnapshotStatus } from '../enums/snapshot-status';
import { SnapshotsTableRow } from '../models/snapshots-rows';

@Injectable()
export class DemoSnapshotHelperService {
  constructor(
    private modalService: ModalService,
    private store: Store<AppState>,
    private router: Router,
    private activatedRoute: ActivatedRoute,
  ) {}

  openSnapshotDeploymentManagementModal(
    options: Pick<
      ConfirmationDialogComponent,
      'templateRef' | 'confirmLabel' | 'confirmButtonLoading$'
    >,
  ): Observable<{ success?: boolean }> {
    const modal =
      this.modalService.openComponentWithStreams$<ConfirmationDialogComponent>(
        ConfirmationDialogComponent,
        {
          ...options,
          confirmButtonColor: 'primary',
          isCustomConfirm: true,
          hideCancelButtonOnConfirmLoading: true,
        },
      );

    return race(
      modal.componentInstance.confirmClicked.pipe(
        map(() => ({
          success: true,
        })),
      ),
      modal.closed,
    ).pipe(take(1));
  }

  openChoseVersionForCopyModal(snapshot: Snapshot | SnapshotsTableRow) {
    // Non-deployed snapshots do not have "latest" state as they don't have a live environment.
    // Therefore, the user can only choose to copy the snapshot based on its initial version state
    // For snapshots without "lastUsedAt", it indicates that the snapshot was never actively used.
    if (snapshot.status !== SnapshotStatus.DEPLOYED || !snapshot.lastUsedAt) {
      this.store.dispatch(
        DemoSnapshotCopyPageActions.navigateToSnapshotCopyPage({
          snapshotId: snapshot.uniqueIdentifier,
          version: snapshot.version,
          snapshotVersionState: SnapshotVersionState.INITIAL,
        }),
      );

      return;
    }
    this.modalService.openComponent<
      SelectVersionForCopyModalComponent,
      { snapshotVersionState: SnapshotVersionState }
    >(
      SelectVersionForCopyModalComponent,
      {
        snapshotUniqueId: snapshot.uniqueIdentifier,
        snapshotVersion: snapshot.version,
      },
      {
        windowClass: 'select-version-for-copy-modal',
      },
      (res) => {
        if (res.success) {
          const snapshotVersionState = res.data.snapshotVersionState;
          this.navigateToSnapshotCopyPage(
            snapshot.uniqueIdentifier,
            snapshot.version,
            snapshotVersionState,
          );
        }
      },
    );
  }

  navigateToSnapshotCopyPage(
    snapshotId: string,
    version: number,
    snapshotVersionState: SnapshotVersionState,
  ) {
    this.store.dispatch(
      DemoSnapshotCopyPageActions.navigateToSnapshotCopyPage({
        snapshotId,
        version,
        snapshotVersionState,
      }),
    );
  }
}
