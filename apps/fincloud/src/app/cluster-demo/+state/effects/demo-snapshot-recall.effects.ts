import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { ModalService } from '@fincloud/core/modal';
import { selectCustomerKey } from '@fincloud/state/customer';
import { SnapshotControllerService } from '@fincloud/swagger-generator/demo';
import { AppState } from '@fincloud/types/models';
import { FinToastService, FinToastType } from '@fincloud/ui/toast';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { catchError, map, of, switchMap, tap } from 'rxjs';
import {
  DemoSnapshotDeploymentManagementApiActions,
  DemoSnapshotDeploymentManagementPageActions,
} from '../actions';
import { selectShouldRedirectAfterSuccessfulAction } from '../selectors/demo-snapshot.selectors';

@Injectable()
export class DemoSnapshotRecallEffects {
  constructor(
    private actions$: Actions,
    private store: Store<AppState>,
    private snapshotControllerService: SnapshotControllerService,
    private finToastService: FinToastService,
    private modalService: ModalService,
    private router: Router,
  ) {}

  recallSnapshot$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DemoSnapshotDeploymentManagementPageActions.recallSnapshot),
      switchMap(({ snapshotId }) =>
        this.snapshotControllerService
          .recallSnapshot({
            snapshotId,
          })
          .pipe(
            map(() =>
              DemoSnapshotDeploymentManagementApiActions.recallSnapshotSuccess(),
            ),
            catchError(() =>
              of(
                DemoSnapshotDeploymentManagementApiActions.recallSnapshotFailure(),
              ),
            ),
          ),
      ),
    ),
  );

  recallSnapshotSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          DemoSnapshotDeploymentManagementApiActions.recallSnapshotSuccess,
        ),
        concatLatestFrom(() => [
          this.store.select(selectCustomerKey),
          this.store.select(selectShouldRedirectAfterSuccessfulAction),
        ]),
        tap(([, customerKey, redirectAfterRecall]) => {
          this.modalService.closeActiveModals();
          this.finToastService.show({
            type: FinToastType.INFO,
            message: $localize`:@@snapshot.actions.recall.successMessage:Snapshot wird zurückgenommen`,
          });

          if (redirectAfterRecall) {
            void this.router.navigate([customerKey, 'demo-snapshot']);
          }
        }),
      ),
    { dispatch: false },
  );

  recallSnapshotFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          DemoSnapshotDeploymentManagementApiActions.recallSnapshotFailure,
        ),
        tap(() => {
          this.modalService.closeActiveModals();
          this.finToastService.show({
            type: FinToastType.ERROR,
            message: $localize`:@@snapshot.actions.recall.errorMessage:Fehler beim Zurücknehmen des Snapshots. \r\nBitte versuchen Sie es erneut.`,
          });
        }),
      ),
    {
      dispatch: false,
    },
  );
}
