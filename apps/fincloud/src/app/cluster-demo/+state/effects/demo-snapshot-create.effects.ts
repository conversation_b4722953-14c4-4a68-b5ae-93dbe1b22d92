import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Toast } from '@fincloud/core/toast';
import { selectCustomerKey } from '@fincloud/state/customer';
import { selectQueryParam, selectRouteParam } from '@fincloud/state/router';
import { SingleClusterDemoControllerService as SingleClusterDemoAuthorizationControllerService } from '@fincloud/swagger-generator/authorization-server';
import { SingleClusterDemoControllerService as SingleClusterDemoBusinessCaseControllerService } from '@fincloud/swagger-generator/business-case-manager';
import { SingleClusterDemoControllerService as SingleClusterDemoCompanyControllerService } from '@fincloud/swagger-generator/company';
import { SingleClusterDemoControllerService as SingleClusterDemoFinancingDetailsControllerService } from '@fincloud/swagger-generator/financing-details';

import {
  SnapshotControllerService,
  User,
} from '@fincloud/swagger-generator/demo';
import { OrganizationLogoControllerService } from '@fincloud/swagger-generator/document';
import { ExchangeService } from '@fincloud/swagger-generator/exchange';
import { AppState } from '@fincloud/types/models';
import { FinToastService } from '@fincloud/ui/toast';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { difference } from 'lodash-es';
import {
  catchError,
  concatMap,
  filter,
  forkJoin,
  from,
  map,
  mergeAll,
  mergeMap,
  of,
  switchMap,
  tap,
  toArray,
} from 'rxjs';
import { SnapshotHttpErrorCode } from '../../enums/snapshot-http-error-code';
import {
  DemoSnapshotCRUDApiActions,
  DemoSnapshotCRUDPageActions,
} from '../actions';
import { snapshotCreateFeature } from '../reducers/demo-snapshot-create.reducer';

@Injectable()
export class DemoSnapshotCreateEffects {
  constructor(
    private actions$: Actions,
    private store: Store<AppState>,
    private router: Router,
    private snapshotControllerService: SnapshotControllerService,
    private singleClusterDemoBusinessCaseControllerService: SingleClusterDemoBusinessCaseControllerService,
    private singleClusterDemoAuthorizationControllerService: SingleClusterDemoAuthorizationControllerService,
    private singleClusterDemoCompanyControllerService: SingleClusterDemoCompanyControllerService,
    private singleClusterDemoFinancingDetailsControllerService: SingleClusterDemoFinancingDetailsControllerService,
    private organizationLogoService: OrganizationLogoControllerService,
    private exchangeService: ExchangeService,
    private finToastService: FinToastService,
  ) {}

  fetchCaseIdsForCustomer$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DemoSnapshotCRUDPageActions.customerSelected),
      mergeMap(({ customerKey }) =>
        this.exchangeService
          .exchangeControllerGetIdsByCustomerKey({
            customerKey,
          })
          .pipe(
            map((businessCaseIds) =>
              DemoSnapshotCRUDApiActions.getCustomerBusinessCasesIdsSuccess({
                businessCaseIds,
                customerKey,
              }),
            ),
            catchError(() =>
              of(
                DemoSnapshotCRUDApiActions.getCustomerBusinessCasesIdsFailure(),
              ),
            ),
          ),
      ),
    ),
  );

  fetchCasesForCustomer$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DemoSnapshotCRUDApiActions.getCustomerBusinessCasesIdsSuccess),
      mergeMap(({ businessCaseIds, customerKey }) => {
        let requests;

        if (!businessCaseIds.length) {
          requests = [
            this.singleClusterDemoBusinessCaseControllerService
              .getBusinessCaseData({ customerKey })
              .pipe(catchError(() => of(null))),
          ];
        } else {
          requests = businessCaseIds.map((businessCaseId) =>
            this.singleClusterDemoBusinessCaseControllerService
              .getBusinessCaseData({ businessCaseId })
              .pipe(
                tap((caseData) =>
                  caseData.companyData?.companyContactPersons?.forEach(
                    (contactPerson) =>
                      ((contactPerson as User).customerKey =
                        caseData.companyData?.company?.customerKey),
                  ),
                ),
                catchError(() => of(null)),
              ),
          );
        }

        return forkJoin([...requests]).pipe(
          map((businessCasesData) => {
            const filteredCaseData = businessCasesData.filter(Boolean);
            return DemoSnapshotCRUDApiActions.getCustomerBusinessCasesSuccess({
              businessCases: filteredCaseData,
              customerKey,
            });
          }),
          catchError(() =>
            of(DemoSnapshotCRUDApiActions.getCustomerBusinessCasesFailure()),
          ),
        );
      }),
    ),
  );

  fetchKpisForCustomer$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DemoSnapshotCRUDPageActions.customerSelected),
      mergeMap(({ customerKey }) =>
        this.singleClusterDemoFinancingDetailsControllerService
          .getCustomerKpis({
            customerKey,
          })
          .pipe(
            map((kpis) =>
              DemoSnapshotCRUDApiActions.getCustomerKpisSuccess({
                kpis,
                customerKey,
              }),
            ),
            catchError(() =>
              of(DemoSnapshotCRUDApiActions.getCustomerKpisFailure()),
            ),
          ),
      ),
    ),
  );

  fetchUsersForCustomer$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        DemoSnapshotCRUDPageActions.customerSelected,
        DemoSnapshotCRUDPageActions.fetchUsersForCustomer,
      ),
      mergeMap(({ customerKey }) =>
        this.singleClusterDemoAuthorizationControllerService
          .getAllUsersForCustomerDemo({
            customerKey,
          })
          .pipe(
            map((users) =>
              DemoSnapshotCRUDApiActions.getCustomerUsersSuccess({
                users,
                customerKey,
              }),
            ),
            catchError(() =>
              of(DemoSnapshotCRUDApiActions.getCustomerUsersFailure()),
            ),
          ),
      ),
    ),
  );

  fetchCompaniesForCustomer$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        DemoSnapshotCRUDPageActions.customerSelected,
        DemoSnapshotCRUDPageActions.fetchCompaniesForCustomer,
      ),
      mergeMap(({ customerKey }) =>
        this.singleClusterDemoCompanyControllerService
          .getCompaniesForCustomerDemo({
            customerKey,
          })
          .pipe(
            map((companies) =>
              DemoSnapshotCRUDApiActions.getCustomerCompaniesSuccess({
                companies,
                customerKey,
              }),
            ),
            catchError(() =>
              of(DemoSnapshotCRUDApiActions.getCustomerCompaniesFailure()),
            ),
          ),
      ),
    ),
  );

  getCadrTemplateForCustomer$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        DemoSnapshotCRUDPageActions.customerSelected,
        DemoSnapshotCRUDPageActions.fetchCompaniesForCustomer,
      ),
      mergeMap(({ customerKey }) =>
        this.singleClusterDemoCompanyControllerService
          .getCadrTemplateByCustomerKeyDemo({
            customerKey,
          })
          .pipe(
            map((cadrTemplate) =>
              DemoSnapshotCRUDApiActions.getCustomerCadrTemplateSuccess({
                customerKey,
                cadrTemplate,
              }),
            ),
            catchError((error) =>
              of(
                DemoSnapshotCRUDApiActions.getCustomerCadrTemplateFailure({
                  error,
                }),
              ),
            ),
          ),
      ),
    ),
  );

  getInformationsForCustomerCompanies$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DemoSnapshotCRUDApiActions.getCustomerCompaniesSuccess),
      mergeMap(({ companies, customerKey }) => {
        const requests = companies.map((company) =>
          this.singleClusterDemoCompanyControllerService
            .getCompanyInformationDemo({
              companyId: company.id,
            })
            .pipe(
              map((informations) => ({
                companyId: company.id,
                informations,
              })),
              catchError(() => of(null)),
            ),
        );

        return from(requests).pipe(
          mergeAll(),
          filter(Boolean),
          toArray(),
          map((informationsPerCompany) =>
            DemoSnapshotCRUDApiActions.getInformationsForCustomerCompaniesSuccess(
              { customerKey, informationsPerCompany },
            ),
          ),
        );
      }),
    ),
  );

  getCompaniesWithoutCases$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DemoSnapshotCRUDPageActions.getCompaniesWithoutCases),
      concatLatestFrom(() =>
        this.store.select(
          snapshotCreateFeature.selectAllCompanyIdsWithNoBusinessCases,
        ),
      ),
      mergeMap(([, companyIds]) =>
        of(
          DemoSnapshotCRUDPageActions.fetchContactPersonsForCompanies({
            companyIds,
          }),
        ),
      ),
    ),
  );

  fetchContactPersonsForCompanies$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DemoSnapshotCRUDPageActions.fetchContactPersonsForCompanies),
      mergeMap(({ companyIds }) => {
        const contactPersonRequests = companyIds.map((companyId) =>
          this.singleClusterDemoCompanyControllerService
            .getContactPersonsForCompanyDemo({
              companyId,
            })
            .pipe(
              map((contactPersons) =>
                contactPersons.map((contactPerson) => ({
                  ...contactPerson,
                  companyId: contactPerson.companyId,
                  attributes: {
                    mobileNumber: contactPerson.phoneNumber,
                    position: contactPerson.role,
                  },
                })),
              ),
              catchError(() => of(null)),
            ),
        );
        return forkJoin([...contactPersonRequests]).pipe(
          map((users) => {
            return DemoSnapshotCRUDApiActions.getCompanyContactPersonsSuccess({
              contactPersons: users,
            });
          }),
          catchError(() =>
            of(DemoSnapshotCRUDApiActions.getCompanyContactPersonsFailure()),
          ),
        );
      }),
    ),
  );

  getParticipantsForCustomer$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DemoSnapshotCRUDApiActions.getCustomerBusinessCasesSuccess),
      mergeMap(({ customerKey }) =>
        of(
          DemoSnapshotCRUDPageActions.setCustomerBusinessCasesParticipants({
            customerKey,
          }),
        ),
      ),
    ),
  );

  setAutoAddedCustomerKeys$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DemoSnapshotCRUDPageActions.setCustomerBusinessCasesParticipants),
      concatLatestFrom(() => [
        this.store.select(snapshotCreateFeature.selectParticipantsPerCustomers),
        this.store.select(snapshotCreateFeature.selectSelectedCustomers),
      ]),
      mergeMap(([{ customerKey }, participantCustomers, customers]) => {
        const participants = participantCustomers?.[customerKey] || [];

        if (!participants.length) {
          return of(DemoSnapshotCRUDPageActions.autoAddedCustomerKeysNotSet());
        }

        const selectedCustomerKeys = customers.map((customer) => customer.key);
        const participantCustomerKeys = participants.map(
          (customer) => customer.key,
        );
        const missingCustomerKeys = difference(
          participantCustomerKeys,
          selectedCustomerKeys,
        );
        return of(
          DemoSnapshotCRUDPageActions.autoAddedCustomerKeysSet({
            missingCustomerKeys,
          }),
        );
      }),
    ),
  );

  loadCustomerLogo$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        DemoSnapshotCRUDPageActions.autoAddedCustomerKeysSet,
        DemoSnapshotCRUDPageActions.customerSelected,
      ),
      concatLatestFrom(() => [
        this.store.select(
          snapshotCreateFeature.selectAutoAddedAndSelectedCustomers,
        ),
      ]),
      switchMap(([, customers]) =>
        from(customers).pipe(
          filter((customer) => !customer.logoContentBase64 && !!customer.logo),
          mergeMap((customer) =>
            this.organizationLogoService
              .getOrganizationLogo({
                customerKey: customer.key,
              })
              .pipe(
                map((logoData) =>
                  DemoSnapshotCRUDApiActions.loadCustomerLogoSuccess({
                    customerKey: customer.key,
                    logoData,
                  }),
                ),
                catchError((error) =>
                  of(
                    DemoSnapshotCRUDApiActions.loadCustomerLogoFailure({
                      error,
                    }),
                  ),
                ),
              ),
          ),
        ),
      ),
    ),
  );

  setAutoAddedCustomerKeysOnCustomerUnselected$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DemoSnapshotCRUDPageActions.customerUnselected),
      concatLatestFrom(() =>
        this.store.select(
          snapshotCreateFeature.selectAllParticipantsForAllCustomers,
        ),
      ),
      mergeMap(([{ customerKey }, participants]) => {
        const isRemovedCustomerParticipant = participants.filter(
          (participant) => participant.key === customerKey,
        ).length;
        if (!isRemovedCustomerParticipant) {
          return of(DemoSnapshotCRUDPageActions.autoAddedCustomerKeysNotSet());
        }
        return of(
          DemoSnapshotCRUDPageActions.autoAddedCustomerKeysSet({
            missingCustomerKeys: [customerKey],
          }),
        );
      }),
    ),
  );

  fetchCompaniesForAutoAddedCustomers$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DemoSnapshotCRUDPageActions.autoAddedCustomerKeysSet),
      concatMap(({ missingCustomerKeys }) => {
        return missingCustomerKeys.map((customerKey) =>
          DemoSnapshotCRUDPageActions.fetchCompaniesForCustomer({
            customerKey,
          }),
        );
      }),
    ),
  );

  fetchUsersForAutoAddedCustomers$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DemoSnapshotCRUDPageActions.autoAddedCustomerKeysSet),
      concatMap(({ missingCustomerKeys }) => {
        return missingCustomerKeys.map((customerKey) =>
          DemoSnapshotCRUDPageActions.fetchUsersForCustomer({ customerKey }),
        );
      }),
    ),
  );

  sendCreateSnapshotRequest$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DemoSnapshotCRUDPageActions.sendCreateSnapshotRequest),
      concatLatestFrom(() => [
        this.store.select(snapshotCreateFeature.selectCreateSnapshotRequest),
      ]),
      mergeMap(([, payload]) =>
        this.snapshotControllerService
          .createSnapshot({
            body: payload,
          })
          .pipe(
            map(() =>
              DemoSnapshotCRUDApiActions.sendCreateSnapshotRequestSuccess(),
            ),
            catchError((error) =>
              of(
                DemoSnapshotCRUDApiActions.sendCreateSnapshotRequestFailure({
                  error,
                }),
              ),
            ),
          ),
      ),
    ),
  );

  sendCreateSnapshotRequestSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(DemoSnapshotCRUDApiActions.sendCreateSnapshotRequestSuccess),
        concatLatestFrom(() => this.store.select(selectCustomerKey)),
        tap(([, customerKey]) => {
          this.finToastService.show(
            Toast.info(
              $localize`:@@snapshot.create.successToast:Erstellter Snapshot wird bereitgestellt`,
            ),
          );

          this.router.navigate([customerKey, 'demo-snapshot']);
        }),
      ),
    { dispatch: false },
  );

  createSnapshotFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(DemoSnapshotCRUDApiActions.sendCreateSnapshotRequestFailure),
        filter(
          ({ error }) =>
            error?.error?.code !== SnapshotHttpErrorCode.DEPLOY_FAILED,
        ),
        tap(() =>
          this.finToastService.show(
            Toast.error(
              $localize`:@@snapshot.create.errorToast:Fehler bei der Snapshot-Erstellung`,
            ),
          ),
        ),
      ),
    { dispatch: false },
  );

  deployAfterCreateSnapshotFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(DemoSnapshotCRUDApiActions.sendCreateSnapshotRequestFailure),
        filter(
          ({ error }) =>
            error?.error?.code === SnapshotHttpErrorCode.DEPLOY_FAILED,
        ),
        concatLatestFrom(() => [this.store.select(selectCustomerKey)]),
        tap(([, customerKey]) => {
          this.finToastService.show(
            Toast.error(
              $localize`:@@snapshot.create.deployErrorToast:Fehler beim Bereitstellen des Snapshots`,
            ),
          );
          this.router.navigate([customerKey, 'demo-snapshot']);
        }),
      ),
    { dispatch: false },
  );

  closeCreateSnapshotPage$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(DemoSnapshotCRUDPageActions.closeCreateSnapshotPage),
        concatLatestFrom(() => [
          this.store.select(selectCustomerKey),
          this.store.select(snapshotCreateFeature.selectIsInCopyMode),
          this.store.select(selectRouteParam('uniqueIdentifier')),
          this.store.select(selectQueryParam('version')),
        ]),
        tap(([, customerKey, isInCopyMode, uniqueIdentifier, version]) => {
          if (isInCopyMode) {
            this.router.navigate(
              [customerKey, 'demo-snapshot', uniqueIdentifier],
              { queryParams: { version } },
            );
          } else {
            this.router.navigate([customerKey, 'demo-snapshot']);
          }
        }),
      ),
    { dispatch: false },
  );
}
