import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { ModalService } from '@fincloud/core/modal';
import { selectCustomerKey } from '@fincloud/state/customer';
import { SnapshotControllerService } from '@fincloud/swagger-generator/demo';
import { AppState } from '@fincloud/types/models';
import { FinToastService, FinToastType } from '@fincloud/ui/toast';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { catchError, map, of, switchMap, tap } from 'rxjs';
import {
  DemoSnapshotDeploymentManagementApiActions,
  DemoSnapshotDeploymentManagementPageActions,
} from '../actions';
import { selectShouldRedirectAfterSuccessfulAction } from '../selectors/demo-snapshot.selectors';

@Injectable()
export class DemoSnapshotResetEffects {
  constructor(
    private actions$: Actions,
    private store: Store<AppState>,
    private snapshotControllerService: SnapshotControllerService,
    private finToastService: FinToastService,
    private modalService: ModalService,
    private router: Router,
  ) {}

  resetSnapshot$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DemoSnapshotDeploymentManagementPageActions.resetSnapshot),
      switchMap(({ snapshotId }) =>
        this.snapshotControllerService.resetSnapshot({ snapshotId }).pipe(
          map(() =>
            DemoSnapshotDeploymentManagementApiActions.resetSnapshotSuccess(),
          ),
          catchError(() =>
            of(
              DemoSnapshotDeploymentManagementApiActions.resetSnapshotFailure(),
            ),
          ),
        ),
      ),
    ),
  );

  resetSnapshotSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(DemoSnapshotDeploymentManagementApiActions.resetSnapshotSuccess),
        concatLatestFrom(() => [
          this.store.select(selectCustomerKey),
          this.store.select(selectShouldRedirectAfterSuccessfulAction),
        ]),
        tap(([, customerKey, redirectAfterReset]) => {
          this.modalService.closeActiveModals();
          this.finToastService.show({
            type: FinToastType.INFO,
            message: $localize`:@@snapshot.actions.reset.successMessage:Snapshot wird zurückgesetzt`,
          });

          if (redirectAfterReset) {
            void this.router.navigate([customerKey, 'demo-snapshot']);
          }
        }),
      ),
    { dispatch: false },
  );

  resetSnapshotFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(DemoSnapshotDeploymentManagementApiActions.resetSnapshotFailure),
        tap(() => {
          this.modalService.closeActiveModals();
          this.finToastService.show({
            type: FinToastType.ERROR,
            message: $localize`:@@snapshot.actions.reset.errorMessage:Fehler beim Zurücksetzen des Snapshots. \r\nBitte versuchen Sie es erneut.`,
          });
        }),
      ),
    {
      dispatch: false,
    },
  );
}
