import { HttpErrorResponse } from '@angular/common/http';
import { User } from '@fincloud/swagger-generator/authorization-server';
import {
  CadrTemplate,
  ContactPersonCompanyDto,
  Information,
} from '@fincloud/swagger-generator/company';
import {
  BusinessCaseData,
  Company,
  Snapshot,
} from '@fincloud/swagger-generator/demo';
import { DocumentEntity } from '@fincloud/swagger-generator/document';
import { CustomerKpi } from '@fincloud/swagger-generator/financing-details';
import { createAction, props } from '@ngrx/store';

// CREATE

export const getCustomerBusinessCasesIdsSuccess = createAction(
  '[Demo Snapshot CRUD API] Get customer business cases IDs Success',
  props<{ businessCaseIds: string[]; customerKey: string }>(),
);

export const getCustomerBusinessCasesIdsFailure = createAction(
  '[Demo Snapshot CRUD API] Get customer business cases IDs Failure',
);

export const getCustomerBusinessCasesSuccess = createAction(
  '[Demo Snapshot CRUD API] Get customer business cases Success',
  props<{ businessCases: BusinessCaseData[]; customerKey: string }>(),
);

export const getCustomerBusinessCasesFailure = createAction(
  '[Demo Snapshot CRUD API] Get customer business cases Failure',
);

export const getCustomerKpisSuccess = createAction(
  '[Demo Snapshot CRUD API] Get customer kpis Success',
  props<{ kpis: CustomerKpi[]; customerKey: string }>(),
);

export const getCustomerKpisFailure = createAction(
  '[Demo Snapshot CRUD API] Get customer kpis Failure',
);

export const getCustomerCompaniesSuccess = createAction(
  '[Demo Snapshot CRUD API] Get customer companies Success',
  props<{ companies: Company[]; customerKey: string }>(),
);

export const getCustomerCompaniesFailure = createAction(
  '[Demo Snapshot CRUD API] Get customer companies Failure',
);

export const getCustomerUsersSuccess = createAction(
  '[Demo Snapshot CRUD API] Fetch customer users Success',
  props<{ users: User[]; customerKey: string }>(),
);

export const getCustomerUsersFailure = createAction(
  '[Demo Snapshot CRUD API] Fetch customer users Failure',
);

export const getCustomerCadrTemplateSuccess = createAction(
  '[Demo Snapshot CRUD API] Fetch customer CADR Template Success',
  props<{ customerKey: string; cadrTemplate: CadrTemplate }>(),
);

export const getCustomerCadrTemplateFailure = createAction(
  '[Demo Snapshot CRUD API] Fetch customer CADR Template Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const getCompanyContactPersonsSuccess = createAction(
  '[Demo Snapshot CRUD API] Get company contact persons Success',
  props<{
    contactPersons: (User & { companyId: string })[][];
  }>(),
);

export const getCompanyContactPersonsFailure = createAction(
  '[Demo Snapshot CRUD API] Get company contact persons Failure',
);

export const getInformationsForCustomerCompaniesSuccess = createAction(
  '[Demo Snapshot CRUD API] Get informations for customer companies Success',
  props<{
    customerKey: string;
    informationsPerCompany: {
      companyId: string;
      informations: Information[];
    }[];
  }>(),
);

export const loadCustomerLogoSuccess = createAction(
  '[Demo Snapshot CRUD API] Load customer logo Success',
  props<{ customerKey: string; logoData: DocumentEntity }>(),
);

export const loadCustomerLogoFailure = createAction(
  '[Demo Snapshot CRUD API] Load customer logo Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const sendCreateSnapshotRequestSuccess = createAction(
  '[Demo Snapshot CRUD API] Send create snapshot request Success',
);

export const sendCreateSnapshotRequestFailure = createAction(
  '[Demo Snapshot CRUD API] Send create snapshot request Failure',
  props<{ error: HttpErrorResponse }>(),
);

// READ

export const getAllSnapshotsSuccess = createAction(
  '[Demo Snapshot CRUD API] Get all snapshots Success',
  props<{ allSnapshots: Snapshot[] }>(),
);

export const getAllSnapshotsFailure = createAction(
  '[Demo Snapshot CRUD API] Get all snapshots Failure',
);

// UPDATE (Snapshot General Information)

export const getSnapshotGeneralInformationForEditSuccess = createAction(
  '[Demo Snapshot CRUD API] Get snapshot general information for edit Success',
  props<{ snapshot: Snapshot }>(),
);

export const getSnapshotGeneralInformationForEditFailure = createAction(
  '[Demo Snapshot CRUD API] Get snapshot general information for edit Failure',
  props<{ error: unknown }>(),
);

export const editSnapshotGeneralInformationSuccess = createAction(
  '[Demo Snapshot CRUD API] Edit snapshot general information Success',
  props<{ snapshot: Snapshot }>(),
);

export const editSnapshotGeneralInformationFailure = createAction(
  '[Demo Snapshot CRUD API] Edit snapshot general information Failure',
);

// UPDATE (Snapshot Users)

export const fetchContactPersonByEmailSuccess = createAction(
  '[Demo Snapshot CRUD API] Fetch Contact Person Success',
  props<{
    user: User;
    contactPersonCompanyDto: ContactPersonCompanyDto;
  }>(),
);

export const fetchContactPersonByEmailFailure = createAction(
  '[Demo Snapshot CRUD API] Fetch Contact Person Failure',
  props<{ error: HttpErrorResponse }>(),
);

// DELETE

export const deleteSnapshotSuccess = createAction(
  '[Demo Snapshot CRUD API] Delete snapshot Success',
);

export const deleteSnapshotFailure = createAction(
  '[Demo Snapshot CRUD API] Delete snapshot Failure',
);
export const setShowNoSelectedCustomersErrorFailure = createAction(
  '[Demo Snapshot API] Set show no selected customers Failure',
);
