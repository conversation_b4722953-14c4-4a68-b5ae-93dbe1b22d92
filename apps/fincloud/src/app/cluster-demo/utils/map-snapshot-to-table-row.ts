import { generateSnapshotDemoTypeCssClass } from '@fincloud/core/demo-snapshot';
import { Snapshot } from '@fincloud/swagger-generator/demo';
import {
  SNAPSHOT_DEMO_TYPE_LABELS,
  capitalizeFirstLetter,
} from '@fincloud/utils';
import { SnapshotsTableRow } from '../models/snapshots-rows';
import { SNAPSHOT_AUTO_DELETE_HOURS_THRESHOLD } from './snapshot-auto-delete-hours-threshold';
import { SNAPSHOT_STATUS_LABELS } from './snapshot-status-labels';

export const mapSnapshotToTableRow = (
  snapshot: Snapshot,
  createdByUsername: string,
): SnapshotsTableRow => {
  const rowData = {
    id: snapshot.id,
    uniqueIdentifier: snapshot.uniqueIdentifier,
    name: snapshot.name,
    type: SNAPSHOT_DEMO_TYPE_LABELS[snapshot.demoType],
    potentialCustomerName: snapshot.potentialCustomerName,
    dateDeployed: snapshot.deployedAt,
    createdBy: createdByUsername,
    autoDeletionDate: snapshot.autoDeletionDate,
    showAutoDeleteWarning: false,
    status: snapshot.status,
    statusLabel:
      SNAPSHOT_STATUS_LABELS[snapshot.status] ??
      capitalizeFirstLetter(snapshot.status),
    demoTypeCellClass: generateSnapshotDemoTypeCssClass(snapshot.demoType),
    version: snapshot.version,
    lastUsedAt: snapshot.lastUsedAt,
  };

  if (snapshot.autoDeletionDate) {
    const now = new Date().getTime();
    const givenDate = new Date(snapshot.autoDeletionDate).getTime();
    const diffInMilliseconds = givenDate - now;
    const diffInHours = diffInMilliseconds / (1000 * 60 * 60);

    rowData.showAutoDeleteWarning =
      diffInHours < SNAPSHOT_AUTO_DELETE_HOURS_THRESHOLD;
  }

  return rowData;
};
