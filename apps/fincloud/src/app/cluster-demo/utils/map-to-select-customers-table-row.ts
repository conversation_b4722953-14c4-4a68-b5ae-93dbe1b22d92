import { generateSnapshotDemoTypeCssClass } from '@fincloud/core/demo-snapshot';
import { Customer } from '@fincloud/swagger-generator/authorization-server';
import {
  SelectCustomersTableRow,
  SnapshotCustomerAdditionalInfo,
} from '@fincloud/types/models';
import { CUSTOMER_SOURCE_TYPE_LABELS } from '@fincloud/utils';

export const mapToSelectCustomersTableRow = (
  customer: Customer,
  createdBy: string,
  customerAdditionalInfo: SnapshotCustomerAdditionalInfo,
): SelectCustomersTableRow => ({
  customerKey: customer.key,
  name: customer.name,
  createdBy,
  createdOn: customer.creationDate,
  source: customerAdditionalInfo?.source ?? CUSTOMER_SOURCE_TYPE_LABELS.MAIN,
  sourceType: customerAdditionalInfo?.sourceType ?? 'MAIN',
  sourceTypeCellClass: generateSnapshotDemoTypeCssClass(
    customerAdditionalInfo?.sourceType,
  ),
});
