import { BusinessCaseData } from '@fincloud/swagger-generator/demo';
import { CASE_TYPES } from '@fincloud/utils';
import { getDemoBusinessCaseVolume } from './get-demo-business-case-financing-volume';

export const mapCaseToTableRow = (businessCaseData: BusinessCaseData) => {
  const businessCase = businessCaseData.businessCase;
  return {
    autoGeneratedBusinessCaseName: businessCase.autoGeneratedBusinessCaseName,
    financingVolume: getDemoBusinessCaseVolume(businessCaseData),
    caseType: CASE_TYPES[businessCase.businessCaseType],
    companyId: businessCase.companyId,
  };
};
