import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ModalService } from '@fincloud/core/modal';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import {
  CreateSnapshotRequest,
  Snapshot,
} from '@fincloud/swagger-generator/demo';
import { SNAPSHOT_DEMO_TYPE_LABELS } from '@fincloud/utils';
import { SnapshotStatus } from '../../enums/snapshot-status';
import { DemoSnapshotHelperService } from '../../services/demo-snapshot-helper.service';
import { ZERO_OFFSET_TIMEZONE } from '../../utils/zero-offset-timezone';
import { DeleteSnapshotModalComponent } from '../delete-snapshot-modal/delete-snapshot-modal.component';
import { DeploySnapshotModalComponent } from '../deploy-snapshot-modal/deploy-snapshot-modal.component';
import { RecallSnapshotModalComponent } from '../recall-snapshot-modal/recall-snapshot-modal.component';
import { ResetSnapshotModalComponent } from '../reset-snapshot-modal/reset-snapshot-modal.component';

@Component({
  selector: 'app-snapshot-summary-general-info',
  templateUrl: './snapshot-summary-general-info.component.html',
  styleUrls: ['./snapshot-summary-general-info.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SnapshotSummaryGeneralInfoComponent {
  @Input() snapshot: Snapshot | CreateSnapshotRequest;
  @Input() limitPreview = false;

  zeroOffsetTimezone = ZERO_OFFSET_TIMEZONE;

  protected readonly Snapshot: Snapshot;
  protected readonly SnapshotStatus = SnapshotStatus;
  dateFormat: string;

  constructor(
    public demoSnapshotHelperService: DemoSnapshotHelperService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private modalService: ModalService,
    private regionalSettings: RegionalSettingsService,
  ) {
    this.dateFormat = this.regionalSettings.dateFormat;
  }

  get demoTypeLabel(): string {
    return SNAPSHOT_DEMO_TYPE_LABELS[this.snapshot.demoType] ?? 'N/A';
  }

  openDeploySnapshotModal(): void {
    this.modalService.openComponentWithStreams$<DeploySnapshotModalComponent>(
      DeploySnapshotModalComponent,
      {
        snapshotId: (this.snapshot as Snapshot).id,
        redirectAfterDeploy: true,
      },
    );
  }

  openRecallSnapshotModal(): void {
    this.modalService.openComponentWithStreams$<RecallSnapshotModalComponent>(
      RecallSnapshotModalComponent,
      {
        snapshotId: (this.snapshot as Snapshot).id,
        redirectAfterRecall: true,
      },
    );
  }

  openResetSnapshotModal(): void {
    this.modalService.openComponentWithStreams$<ResetSnapshotModalComponent>(
      ResetSnapshotModalComponent,
      {
        snapshotId: (this.snapshot as Snapshot).id,
        redirectAfterReset: true,
      },
    );
  }

  openDeleteSnapshotModal(): void {
    this.modalService.openComponentWithStreams$<DeleteSnapshotModalComponent>(
      DeleteSnapshotModalComponent,
      {
        snapshot: this.snapshot,
        redirectAfterDelete: true,
      },
    );
  }

  editSnapshot(): void {
    void this.router.navigate(['edit'], {
      relativeTo: this.activatedRoute,
      queryParams: {
        version: (this.snapshot as Snapshot).version,
      },
    });
  }

  openChooseVersionForCopyModal(): void {
    this.demoSnapshotHelperService.openChoseVersionForCopyModal(this.snapshot);
  }
}
