import { Component, Inject, LOCALE_ID, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ModalService } from '@fincloud/core/modal';
import { Locale } from '@fincloud/types/enums';
import { FluidTableSorting } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import {
  DemoSnapshotCRUDPageActions,
  DemoSnapshotCommonPageActions,
} from '../../+state/actions';
import { selectSnapshotsListState } from '../../+state/selectors/demo-snapshot.selectors';
import { SnapshotStatus } from '../../enums/snapshot-status';
import { SnapshotsTableRow } from '../../models/snapshots-rows';
import { DemoSnapshotHelperService } from '../../services/demo-snapshot-helper.service';
import { getSnapshotsColumnsConfig } from '../../utils/get-snapshot-columns-config';
import { DeleteSnapshotModalComponent } from '../delete-snapshot-modal/delete-snapshot-modal.component';
import { DeploySnapshotModalComponent } from '../deploy-snapshot-modal/deploy-snapshot-modal.component';
import { RecallSnapshotModalComponent } from '../recall-snapshot-modal/recall-snapshot-modal.component';
import { ResetSnapshotModalComponent } from '../reset-snapshot-modal/reset-snapshot-modal.component';

@Component({
  selector: 'app-demo-snapshot',
  templateUrl: './demo-snapshot.component.html',
  styleUrls: ['./demo-snapshot.component.scss'],
})
export class DemoSnapshotComponent implements OnInit, OnDestroy {
  getSnapshots$ = this.store.select(selectSnapshotsListState);

  readonly columns = getSnapshotsColumnsConfig(this.locale);
  readonly defaultSort: FluidTableSorting[] = [
    { dir: 'desc', prop: 'dateDeployed' },
  ];
  readonly SnapshotStatus = SnapshotStatus;

  constructor(
    private store: Store,
    private modalService: ModalService,
    private demoSnapshotHelperService: DemoSnapshotHelperService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    @Inject(LOCALE_ID) private locale: Locale,
  ) {}

  ngOnInit(): void {
    this.store.dispatch(DemoSnapshotCRUDPageActions.getAllSnapshots());
  }

  searchSnapshots(term: string): void {
    this.store.dispatch(DemoSnapshotCommonPageActions.searchSnapshot({ term }));
  }

  openDeploySnapshotModal(snapshot: SnapshotsTableRow): void {
    this.modalService.openComponentWithStreams$<DeploySnapshotModalComponent>(
      DeploySnapshotModalComponent,
      { snapshotId: snapshot.id },
    );
  }

  openRecallSnapshotModal(snapshot: SnapshotsTableRow): void {
    this.modalService.openComponentWithStreams$<RecallSnapshotModalComponent>(
      RecallSnapshotModalComponent,
      { snapshotId: snapshot.id },
    );
  }

  openResetSnapshotModal(snapshot: SnapshotsTableRow): void {
    this.modalService.openComponentWithStreams$<ResetSnapshotModalComponent>(
      ResetSnapshotModalComponent,
      { snapshotId: snapshot.id },
    );
  }

  openDeleteSnapshotModal(snapshot: SnapshotsTableRow): void {
    this.modalService.openComponentWithStreams$<DeleteSnapshotModalComponent>(
      DeleteSnapshotModalComponent,
      { snapshot },
    );
  }

  openChoseVersionForCopyModal(snapshot: SnapshotsTableRow): void {
    this.demoSnapshotHelperService.openChoseVersionForCopyModal(snapshot);
  }

  navigateToSnapshotEditPage(snapshot: SnapshotsTableRow): void {
    void this.router.navigate([snapshot.uniqueIdentifier, 'edit'], {
      relativeTo: this.activatedRoute,
      queryParams: { version: snapshot.version },
    });
  }

  ngOnDestroy(): void {
    this.store.dispatch(
      DemoSnapshotCommonPageActions.clearSnapshotSearchTerm(),
    );
  }
}
