<div class="tw-flex tw-justify-between tw-items-center">
  <div class="heading2">Snapshots</div>
  <ui-button
    icon="add"
    i18n-label="@@snapshot.snapshot.createButton"
    label="Snapshot erstellen"
    corners="rounded"
    routerLink="create"
  ></ui-button>
</div>

<div class="tw-flex-col tw-flex tw-pt-[1.6rem]">
  <div class="search-container tw-bg-color-surface-primary tw-py-5 tw-px-7">
    <ui-search-filter
      color="gray"
      size="large"
      class="search"
      placeholder="Suche..."
      i18n-placeholder="@@cases.search.placeholder"
      [hasBorder]="false"
      (search)="searchSnapshots($event)"
    ></ui-search-filter>
  </div>

  @if (getSnapshots$ | async; as snapshots) {
    @if (!snapshots.loading && snapshots.snapshotsList.length === 0) {
      <div
        class="no-demos-container tw-flex tw-flex-col tw-items-center tw-py-3.5"
      >
        @if (!snapshots.searchTerm) {
          <div
            class="no-results tw-bg-color-surface-primary tw-text-center tw-py-16 tw-w-full"
          >
            <ui-icon name="svgNoDemosIcon" size="large" color="gray"></ui-icon>
            <div class="heading4" i18n="@@snapshot.snapshot.noResult.title">
              Keine Snapshots erstellt
            </div>
            <div
              class="heading8 description tw-text-color-background-dark-moderate tw-pt-3"
              i18n="@@snapshot.snapshot.noResult.description"
            >
              Die erstellten Snapshots werden hier angezeigt
            </div>
          </div>
        }
        @if (snapshots.searchTerm) {
          <div
            class="no-results tw-bg-color-surface-primary tw-text-center tw-py-16 tw-w-full"
          >
            <ui-icon name="svgNoDemosIcon" size="large" color="gray"></ui-icon>
            <div class="heading4" i18n="@@snapshot.snapshot.notFound.title">
              Keine Snapshots gefunden
            </div>
          </div>
        }
      </div>
    }
    <div class="snapshots-table-container tw-py-2.5">
      @if (
        snapshots.loading ||
        (!snapshots.loading && snapshots.snapshotsList.length > 0)
      ) {
        <div class="snapshots-table">
          <ui-fluid-table
            [columns]="columns"
            [templates]="{
              type: typeTemplate,
              autoDeletionDate: autoDeletionDateTemplate,
              actions: actionsTemplate,
            }"
            noDataAvailableMessage=""
            [rows]="snapshots.snapshotsList"
            [selectionType]="null"
            [defaultSort]="defaultSort"
            [showSpinner]="snapshots.loading"
          >
          </ui-fluid-table>
        </div>
      }
    </div>
  }
</div>

<ng-template #typeTemplate let-row="row">
  <ui-truncated-text [ngClass]="row.demoTypeCellClass">
    {{ row.type }}
  </ui-truncated-text>
</ng-template>

<ng-template #autoDeletionDateTemplate let-row="row">
  <div class="tw-flex tw-items-center">
    @if (row.showAutoDeleteWarning) {
      <ui-icon
        class="tw-mr-2"
        name="schedule"
        size="medium"
        color="error"
      ></ui-icon>
    }
    <ui-truncated-text
      [class.tw-text-color-text-error]="row.showAutoDeleteWarning"
    >
      {{ row.autoDeletionDate | date }}
    </ui-truncated-text>
  </div>
</ng-template>

<ng-template #actionsTemplate let-row="row" let-value="value">
  @if (
    row.status !== SnapshotStatus.DEPLOYING &&
    row.status !== SnapshotStatus.RECALLING &&
    row.status !== SnapshotStatus.RESETTING
  ) {
    <ui-actions-menu
      [optionsTemplate]="options"
      [hasInteractionState]="true"
      [hideArrow]="true"
      [showMenuBottomAndLeft]="true"
      iconColor="subtle"
      iconSize="medium-large"
      class="group-actions !tw-block"
    ></ui-actions-menu>
  }
  <ng-template #options>
    <ui-actions-menu-item
      class="action-group-menu-item"
      iconName="svgViewIcon"
      iconSize="medium"
      label="Ansehen"
      i18n-label="@@snapshot.actions.view"
      [routerLink]="[row.uniqueIdentifier]"
      [queryParams]="{ version: row.version }"
    ></ui-actions-menu-item>
    <ui-actions-menu-item
      class="action-group-menu-item"
      iconName="edit"
      iconSize="medium"
      label="Bearbeiten"
      i18n-label="@@companyAnalysis.companyBranches.label.rework"
      [disabled]="row.status === SnapshotStatus.FAILED"
      (clicked)="navigateToSnapshotEditPage(row)"
    ></ui-actions-menu-item>
    <ui-actions-menu-item
      class="action-group-menu-item"
      iconName="svgCloneIcon"
      iconSize="medium"
      label="Kopieren"
      i18n-label="@@snapshot.actions.copy"
      [disabled]="row.status !== SnapshotStatus.DEPLOYED"
      (clicked)="openChoseVersionForCopyModal(row)"
    ></ui-actions-menu-item>
    <ui-tooltip
      placement="start"
      [disabled]="
        row.status !== SnapshotStatus.DEPLOYED ||
        row.status === SnapshotStatus.FAILED
      "
      [closeDelay]="0"
      i18n-text="@@snapshot.actions.reset.tooltip"
      text="Das Zurücksetzen stellt den Snapshot in seinen ursprünglichen Zustand zurück und macht ihn sofort einsatzbereit."
    >
      <ui-actions-menu-item
        [disabled]="row.status !== SnapshotStatus.DEPLOYED"
        class="action-group-menu-item"
        iconName="svgResetIcon"
        iconSize="medium"
        label="Zurücksetzen"
        i18n-label="@@snapshot.actions.reset"
        (clicked)="openResetSnapshotModal(row)"
      ></ui-actions-menu-item>
    </ui-tooltip>
    <ui-tooltip
      placement="start"
      [disabled]="row.status !== SnapshotStatus.DEPLOYED"
      [closeDelay]="0"
      i18n-text="@@snapshot.actions.recall.tooltip"
      text="Entfernt den Snapshot aus der Umgebung und setzt ihn in seinen ursprünglichen Zustand zurück."
    >
      <ui-actions-menu-item
        [disabled]="row.status !== SnapshotStatus.DEPLOYED"
        class="action-group-menu-item"
        iconName="restart"
        iconSize="medium"
        label="Abrufen"
        i18n-label="@@snapshot.actions.recall"
        (clicked)="openRecallSnapshotModal(row)"
      ></ui-actions-menu-item>
    </ui-tooltip>
    <ui-tooltip
      placement="start"
      [disabled]="
        row.status === SnapshotStatus.DEPLOYED ||
        row.status === SnapshotStatus.FAILED
      "
      [closeDelay]="0"
      i18n-text="@@snapshot.actions.deploy.tooltip"
      text="Die Bereitstellung ermöglicht Kundenanmeldungen."
    >
      <ui-actions-menu-item
        [disabled]="
          row.status === SnapshotStatus.DEPLOYED ||
          row.status === SnapshotStatus.FAILED
        "
        class="action-group-menu-item"
        iconName="merge"
        iconSize="medium"
        label="Bereitstellen"
        i18n-label="@@snapshot.actions.deploy"
        (clicked)="openDeploySnapshotModal(row)"
      ></ui-actions-menu-item>
    </ui-tooltip>
    <ui-actions-menu-item
      class="action-group-menu-item delete-action"
      iconName="svgDeleteIcon"
      iconSize="medium"
      label="Löschen"
      i18n-label="@@snapshot.actions.delete"
      color="red"
      (clicked)="openDeleteSnapshotModal(row)"
    ></ui-actions-menu-item>
  </ng-template>
</ng-template>
