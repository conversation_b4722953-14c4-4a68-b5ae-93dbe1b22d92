import {
  ChangeDetectionStrategy,
  Component,
  Inject,
  Input,
  LOCALE_ID,
  OnInit,
} from '@angular/core';
import { TableColumn } from '@fincloud/components/lists';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { AddressHelperService } from '@fincloud/core/services';
import { CUSTOMER_FEATURE_TEMPLATE_OPTIONS } from '@fincloud/neoshare/account-management';
import { Address } from '@fincloud/swagger-generator/authorization-server';
import { CustomerData } from '@fincloud/swagger-generator/demo';
import { CustomerStatus, CustomerType, Locale } from '@fincloud/types/enums';
import { TableRow } from '@fincloud/types/models';
import { CUSTOMER_TYPE_LABELS } from '@fincloud/utils';
import { CompanyWithBusinessCases } from '../../models/company-with-business-cases';
import { businessCasesInformationColumnConfig } from '../../utils/business-case-information-column-config';

@Component({
  selector: 'app-snapshot-summary-customer-preview-section',
  templateUrl: './snapshot-summary-customer-preview-section.component.html',
  styleUrls: ['./snapshot-summary-customer-preview-section.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SnapshotSummaryCustomerPreviewSectionComponent implements OnInit {
  @Input() customerData: CustomerData;
  @Input() customerPartners: CustomerData[];
  @Input() customerCompanies: CompanyWithBusinessCases[];
  @Input() users: TableRow[];

  columns: TableColumn[];

  readonly customerStatus = CustomerStatus;
  readonly customerType = CustomerType;
  readonly customerTypeLabels = CUSTOMER_TYPE_LABELS;
  readonly customerFeatureTemplateOptions = CUSTOMER_FEATURE_TEMPLATE_OPTIONS;

  constructor(
    @Inject(LOCALE_ID) private locale: Locale,
    private regionalSettings: RegionalSettingsService,
    private addressHelperService: AddressHelperService,
  ) {}

  get customerLogoUrl(): string {
    if (this.customerData.logoContentBase64) {
      return this.customerData.logoContentBase64.startsWith('data:image')
        ? this.customerData.logoContentBase64
        : `data:image/png;base64,${this.customerData.logoContentBase64}`;
    }

    return (
      this.customerData.customer?.logo || './assets/svg/svgAvatarNoImage.svg'
    );
  }

  get fullAddress(): string {
    return this.addressHelperService.asCustomerAddressString(
      this.customerData?.customer?.address,
    );
  }

  get featureTemplateLabel(): string {
    return this.customerFeatureTemplateOptions.find(
      (opt) => opt.theme === this.customerData.customer.themeName,
    ).label;
  }

  ngOnInit() {
    this.columns = businessCasesInformationColumnConfig(
      this.locale,
      this.regionalSettings,
    );
  }

  getCityName(address: Address): string {
    return address?.cityName;
  }
}
