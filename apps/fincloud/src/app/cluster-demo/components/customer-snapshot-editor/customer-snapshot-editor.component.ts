import { Component, DestroyRef, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl, UntypedFormBuilder, Validators } from '@angular/forms';
import { UploadCustomerLogoModalComponent } from '@fincloud/components/modals';
import { DEFAULT_ASPECT_RATIO } from '@fincloud/core/aspect-ratio';
import { ModalService } from '@fincloud/core/modal';
import { AddressHelperService } from '@fincloud/core/services';
import { CUSTOMER_FEATURE_TEMPLATE_OPTIONS } from '@fincloud/neoshare/account-management';
import { User } from '@fincloud/swagger-generator/authorization-server';
import {
  CustomerStatus,
  CustomerType,
  FeatureTemplate,
} from '@fincloud/types/enums';
import { CUSTOMER_TYPE_LABELS } from '@fincloud/utils';
import { Store } from '@ngrx/store';
import { isEqual } from 'lodash-es';
import { distinctUntilChanged, tap } from 'rxjs';
import { DemoSnapshotCRUDPageActions } from '../../+state/actions';
import { snapshotCreateFeature } from '../../+state/reducers/demo-snapshot-create.reducer';

@Component({
  selector: 'app-customer-snapshot-editor',
  templateUrl: './customer-snapshot-editor.component.html',
  styleUrls: ['./customer-snapshot-editor.component.scss'],
})
export class CustomerSnapshotEditorComponent implements OnInit {
  users$ = this.store.select(
    snapshotCreateFeature.selectUsersTableDataForCustomerInView,
  );
  companies$ = this.store.select(
    snapshotCreateFeature.selectCompaniesForCustomerInView,
  );
  customer$ = this.store.select(snapshotCreateFeature.selectCustomerInView);

  logoPhotoTooltipMessage = {
    uploadLogoMessage: $localize`:@@customerDetails.uploadLogoPhoto.tooltip:Logo hochladen`,
    deleteLogoMessage: $localize`:@@customerDetails.deleteLogoPhoto.tooltip:Logo löschen`,
  };

  aspectRatio = DEFAULT_ASPECT_RATIO;

  manageCustomerForm = this.formBuilder.group({
    name: ['', [Validators.required]],
    customerType: ['', [Validators.required]],
    address: [null],
    website: [''],
    featureTemplate: [null],
  });

  customerKey: string;

  readonly customerFeatureTemplateOptions = CUSTOMER_FEATURE_TEMPLATE_OPTIONS;
  readonly customerTypeLabels = CUSTOMER_TYPE_LABELS;
  readonly customerType = CustomerType;
  readonly customerStatus = CustomerStatus;

  get addressControl(): FormControl {
    return this.manageCustomerForm.get('address') as FormControl;
  }

  constructor(
    private modalService: ModalService,
    private formBuilder: UntypedFormBuilder,
    private store: Store,
    private destroyRef: DestroyRef,
    private addressHelperService: AddressHelperService,
  ) {}

  ngOnInit() {
    this.subscribeToCustomerChanges();
    this.subscribeToFormChanges();
  }

  openEditUserModal(user: User) {
    this.store.dispatch(
      DemoSnapshotCRUDPageActions.openSnapshotUserEditModal({
        userId: user.id,
      }),
    );
  }

  openUploadLogoModal() {
    this.modalService.openComponent<UploadCustomerLogoModalComponent>(
      UploadCustomerLogoModalComponent,
      {
        isExternalExecution: true,
      },
      {
        windowClass: 'upload-logo',
      },
      (res) => {
        if (res.success) {
          this.store.dispatch(
            DemoSnapshotCRUDPageActions.snapshotCustomerLogoUploaded({
              ...(res.data as {
                logoContentBase64: string;
                logoDocumentName: string;
              }),
            }),
          );
        }
      },
    );
  }

  onDeleteLogoImage() {
    this.store.dispatch(
      DemoSnapshotCRUDPageActions.snapshotCustomerLogoDeleted({
        customerKey: this.customerKey,
      }),
    );
  }

  private subscribeToCustomerChanges(): void {
    this.customer$
      .pipe(
        distinctUntilChanged((prev, curr) => prev?.key === curr?.key),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((customer) => {
        if (customer) {
          this.customerKey = customer.key;
          const featureTemplateOption =
            this.customerFeatureTemplateOptions.find(
              (t) => t.theme === customer.themeName,
            );

          this.manageCustomerForm.patchValue({
            name: customer.newCustomerName ?? customer.name,
            customerType: customer.customerType,
            address: customer.address?.streetNameAndNumber
              ? {
                  ...customer.address,
                  addressAsString:
                    this.addressHelperService.asCustomerAddressString(
                      customer.address,
                    ),
                }
              : null,
            website: customer.website,
            featureTemplate:
              featureTemplateOption?.theme || FeatureTemplate.NEOSHARE, // default to 'neoshare' if not present
          });
        }
      });
  }

  private subscribeToFormChanges(): void {
    this.manageCustomerForm.valueChanges
      .pipe(
        distinctUntilChanged(isEqual),
        tap((value) =>
          this.store.dispatch(
            DemoSnapshotCRUDPageActions.snapshotCustomerDetailsUpdated({
              customerKey: this.customerKey,
              ...value,
            }),
          ),
        ),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
