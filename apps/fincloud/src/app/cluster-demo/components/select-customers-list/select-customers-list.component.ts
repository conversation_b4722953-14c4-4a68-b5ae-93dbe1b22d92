import {
  Component,
  DestroyRef,
  Inject,
  LOCALE_ID,
  OnD<PERSON>roy,
  OnInit,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FluidTableColumn } from '@fincloud/components/lists';
import { Locale } from '@fincloud/types/enums';
import { CUSTOMER_SOURCE_TYPE_LABELS } from '@fincloud/utils';
import { Store } from '@ngrx/store';
import { Subject, debounceTime, filter, take, tap } from 'rxjs';
import { shareReplay } from 'rxjs/operators';
import {
  DemoSnapshotCRUDPageActions,
  DemoSnapshotCopyPageActions,
} from '../../+state/actions';
import { snapshotCreateFeature } from '../../+state/reducers/demo-snapshot-create.reducer';
import { getSelectCustomersTableColumnsConfig } from '../../utils/get-select-customers-table-columns-config';
import { SELECT_CUSTOMERS_TABLE_DEFAULT_SORT } from '../../utils/select-customers-table-default-sort';
import { SNAPSHOT_TYPE_OPTIONS_EXTENDED } from '../../utils/snapshot-type-options-extended';

@Component({
  selector: 'app-select-customers-list',
  templateUrl: './select-customers-list.component.html',
  styleUrls: ['./select-customers-list.component.scss'],
})
export class SelectCustomersListComponent implements OnInit, OnDestroy {
  searchSubject = new Subject<string>();
  columns: FluidTableColumn[];
  defaultSort = SELECT_CUSTOMERS_TABLE_DEFAULT_SORT;
  tableData$ = this.store.select(
    snapshotCreateFeature.selectCustomersTableData,
  );
  selectedCustomerSourceTypeFilter$ = this.store.select(
    snapshotCreateFeature.selectCustomersListSourceTypeFilter,
  );

  selectedCustomers$ = this.store
    .select(snapshotCreateFeature.selectSelectedCustomers)
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  showNoSelectedCustomersError$ = this.store.select(
    snapshotCreateFeature.selectShowNoSelectedCustomersError,
  );

  selectedCustomersCount = 0;
  selectedCustomersRecords: Record<string, boolean> = {};

  readonly snapshotTypeOptionsExtended = SNAPSHOT_TYPE_OPTIONS_EXTENDED;
  readonly customerSourceTypeLabels = CUSTOMER_SOURCE_TYPE_LABELS;
  readonly maxAmountOfSelectedCustomers = 5;

  constructor(
    private store: Store,
    private destroyRef: DestroyRef,
    @Inject(LOCALE_ID) private locale: Locale,
  ) {}

  ngOnInit() {
    this.columns = getSelectCustomersTableColumnsConfig(this.locale);

    this.searchSubject.pipe(debounceTime(300)).subscribe((searchTerm) => {
      this.store.dispatch(
        DemoSnapshotCRUDPageActions.setCustomersListSearchTerm({
          searchTerm,
        }),
      );
    });

    this.store
      .select(snapshotCreateFeature.selectSecondStepInfoFromCopyInitialVersion)
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        filter(
          ({
            areSelectedCustomersFromCopySet,
            createBasedOnInitialVersion,
            isDataFromInitialSnapshotSet,
          }) =>
            !areSelectedCustomersFromCopySet &&
            createBasedOnInitialVersion &&
            isDataFromInitialSnapshotSet,
        ),
        tap(({ selectedCustomers }) => {
          selectedCustomers?.forEach((customer) => {
            this.selectedCustomersRecords[customer.key] = true;
          });
          this.store.dispatch(
            DemoSnapshotCopyPageActions.selectedCustomersFromCopySet(),
          );
        }),
      )
      .subscribe();

    this.store
      .select(snapshotCreateFeature.selectSecondStepInfoFromCopyLatestVersion)
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        filter(
          ({
            areSelectedCustomersFromCopySet,
            createBasedOnLatestVersion,
            isCustomersLoading,
            areCustomersFromLatestVersionSet,
          }) =>
            createBasedOnLatestVersion &&
            !isCustomersLoading &&
            areCustomersFromLatestVersionSet &&
            !areSelectedCustomersFromCopySet,
        ),
        tap(({ selectedCustomers }) => {
          selectedCustomers?.forEach((customer) => {
            this.toggleRowSelection(true, customer.key);
          });
          this.store.dispatch(
            DemoSnapshotCopyPageActions.selectedCustomersFromCopySet(),
          );
        }),
      )
      .subscribe();

    this.selectedCustomers$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (selectedCustomers) =>
          (this.selectedCustomersCount = selectedCustomers?.length || 0),
      });
  }

  ngOnDestroy() {
    this.searchSubject.complete();
  }

  toggleRowSelection(isSelected: boolean, customerKey: string): void {
    this.selectedCustomers$
      .pipe(
        take(1),
        tap((selectedCustomers) => {
          const customerAlreadySelected = !!selectedCustomers.find(
            ({ key }) => key === customerKey,
          );

          if (
            isSelected &&
            (customerAlreadySelected ||
              selectedCustomers.length >= this.maxAmountOfSelectedCustomers)
          ) {
            return;
          }

          if (isSelected) {
            this.selectedCustomersRecords[customerKey] = true;
            this.store.dispatch(
              DemoSnapshotCRUDPageActions.customerSelected({ customerKey }),
            );
          } else {
            this.unselectCustomer(customerKey);
          }
        }),
      )
      .subscribe();
  }

  searchCustomers(searchTerm: string) {
    this.searchSubject.next(searchTerm);
  }

  filterSourceTypes(sourceType: string) {
    this.store.dispatch(
      DemoSnapshotCRUDPageActions.setCustomersListSourceTypeFilter({
        sourceType,
      }),
    );
  }

  unselectCustomer(customerKey: string): void {
    this.selectedCustomersRecords[customerKey] = false;
    this.store.dispatch(
      DemoSnapshotCRUDPageActions.customerUnselected({ customerKey }),
    );
  }
}
