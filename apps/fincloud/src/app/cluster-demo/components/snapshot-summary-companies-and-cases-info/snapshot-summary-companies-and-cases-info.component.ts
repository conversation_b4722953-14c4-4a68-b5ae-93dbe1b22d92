import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import {
  CreateSnapshotRequest,
  Snapshot,
} from '@fincloud/swagger-generator/demo';
import { SnapshotVersionState } from '@fincloud/types/enums';
import { PillItem } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { DemoSnapshotDetailsPageActions } from '../../+state/actions';
import { snapshotDetailsFeature } from '../../+state/reducers/demo-snapshot-details.reducer';
import { SnapshotStatus } from '../../enums/snapshot-status';
import { DEFAULT_SNAPSHOT_VERSION_STATE_VIEW } from '../../utils/default-snapshot-version-state-view';
import { SNAPSHOT_STATE_VIEW_PILLS } from '../../utils/snapshot-state-view-pills';

@Component({
  selector: 'app-snapshot-summary-companies-and-cases-info',
  templateUrl: './snapshot-summary-companies-and-cases-info.component.html',
  styleUrls: ['./snapshot-summary-companies-and-cases-info.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SnapshotSummaryCompaniesAndCasesInfoComponent {
  @Input() snapshot: Snapshot | CreateSnapshotRequest;
  @Input() limitPreview: boolean;

  companiesPerCustomer$ = this.store.select(
    snapshotDetailsFeature.selectCompaniesWithBusinessCasesPerCustomerFromSnapshotData,
  );

  manuallyAddedCustomers$ = this.store.select(
    snapshotDetailsFeature.selectManuallyAddedCustomers,
  );
  autoAddedCustomers$ = this.store.select(
    snapshotDetailsFeature.selectAutoAddedCustomers,
  );

  partnersPerCustomer$ = this.store.select(
    snapshotDetailsFeature.selectPartnersPerCustomerFromSnapshotData,
  );

  usersPerCustomer$ = this.store.select(
    snapshotDetailsFeature.selectUsersPerCustomerFromSnapshotData,
  );

  linkedSnapshots$ = this.store.select(
    snapshotDetailsFeature.selectLinkedSnapshots,
  );

  readonly pills: PillItem[] = SNAPSHOT_STATE_VIEW_PILLS;
  activePill: PillItem = SNAPSHOT_STATE_VIEW_PILLS.find(
    (pill) => pill.key === DEFAULT_SNAPSHOT_VERSION_STATE_VIEW,
  );

  readonly SnapshotStatus = SnapshotStatus;
  readonly Snapshot: Snapshot;

  constructor(private store: Store) {}

  switchStateView(item: PillItem): void {
    this.store.dispatch(
      DemoSnapshotDetailsPageActions.switchViewDetailsStateOption({
        versionState: item.key as SnapshotVersionState,
      }),
    );
  }
}
