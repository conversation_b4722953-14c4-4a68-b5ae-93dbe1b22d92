import { Component, Input } from '@angular/core';
import { Customer } from '@fincloud/swagger-generator/authorization-server';
import { AppState } from '@fincloud/types/models';
import { CUSTOMER_TYPE_LABELS } from '@fincloud/utils';
import { Store } from '@ngrx/store';
import { DemoSnapshotCRUDPageActions } from '../../+state/actions';
import { snapshotCreateFeature } from '../../+state/reducers/demo-snapshot-create.reducer';

@Component({
  selector: 'app-customer-card-snapshot',
  templateUrl: './customer-card-snapshot.component.html',
  styleUrls: ['./customer-card-snapshot.component.scss'],
})
export class CustomerCardSnapshotComponent {
  @Input() participantsView: boolean;

  customerList$ = this.store.select(
    snapshotCreateFeature.selectSelectedCustomers,
  );
  participantsList$ = this.store.select(
    snapshotCreateFeature.selectAutoAddedCustomers,
  );
  participantCustomers$ = this.store.select(
    snapshotCreateFeature.selectParticipantsPerCustomers,
  );
  customerInViewKey$ = this.store.select(
    snapshotCreateFeature.selectCustomerInViewKey,
  );
  customerAdditionalInfo$ = this.store.select(
    snapshotCreateFeature.selectCustomerAdditionalInfo,
  );

  readonly customerTypeLabels = CUSTOMER_TYPE_LABELS;

  constructor(private store: Store<AppState>) {}

  trackById(index: number, customer: Customer) {
    return customer.id;
  }

  selectCustomer(customerKey: string | undefined) {
    this.store.dispatch(
      DemoSnapshotCRUDPageActions.setCustomerInView({ customerKey }),
    );
  }
}
