export enum KpiRangesFormControl {
  CHECK_EXCLUSION_CRITERIA_RANGE_MIN = 'checkExclusionCriteriaRangeMin',
  CHECK_EXCLUSION_CRITERIA_RANGE_MAX = 'checkExclusionCriteriaRangeMax',
  CHECK_PERMISSIBLE_DEVIATION_RANGE_MIN = 'checkPermissibleDeviationRangeMin',
  CHECK_PERMISSIBLE_DEVIATION_RANGE_MAX = 'checkPermissibleDeviationRangeMax',
  COMPLIANT_WITH_SPECIFICATIONS_RANGE_MIN = 'compliantWithSpecificationsRangeMin',
  COMPLIANT_WITH_SPECIFICATIONS_RANGE_MAX = 'compliantWithSpecificationsRangeMax',
}
