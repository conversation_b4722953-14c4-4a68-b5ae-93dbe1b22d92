import { Component, DestroyRef, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  AbstractControl,
  FormControl,
  NonNullableFormBuilder,
  Validators,
} from '@angular/forms';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import {
  DECIMAL_MASK_NEGATIVE_ALLOWED_CONFIG,
  PERCENTAGE_MASK_NEGATIVE_ALLOWED_CONFIG,
} from '@fincloud/core/utils';
import {
  KPI_OPTIONS_FOR_BANK,
  KPI_OPTIONS_FOR_REAL_ESTATE,
} from '@fincloud/neoshare/kpis';
import { selectCustomerType } from '@fincloud/state/customer';
import { CustomerKpi } from '@fincloud/swagger-generator/financing-details';
import { CustomerType } from '@fincloud/types/enums';
import { Store } from '@ngrx/store';
import { isEqual } from 'lodash-es';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  pairwise,
  startWith,
  tap,
} from 'rxjs';
import {
  StateLibKpiRangesPageActions,
  StateLibKpiTogglePageActions,
} from '../../+state/actions';
import {
  selectCurrentlySelectedKpi,
  selectKpiToggleState,
} from '../../+state/selectors/kpi.selectors';
import { KpiRangesFormControl } from '../../enums/kpi-ranges-form-control';
import { KpiRanges } from '../../models/kpi-ranges';
import { kpiRangeMaxValidator } from '../../utils/kpi-ranges-max-validator';
import { mapValidationErrorToLocalizedMessage } from '../../utils/map-validation-error-to-localized-message';

@Component({
  selector: 'app-customer-kpi-settings',
  templateUrl: './customer-kpi-settings.component.html',
  styleUrls: ['./customer-kpi-settings.component.scss'],
})
export class CustomerKpiSettingsComponent implements OnInit {
  percentMaskConfig =
    PERCENTAGE_MASK_NEGATIVE_ALLOWED_CONFIG[this.regionalSettings.locale];
  decimalMaskConfig =
    DECIMAL_MASK_NEGATIVE_ALLOWED_CONFIG[this.regionalSettings.locale];
  KPI_OPTIONS_FOR_BANK = KPI_OPTIONS_FOR_BANK;
  KPI_OPTIONS_FOR_REAL_ESTATE = KPI_OPTIONS_FOR_REAL_ESTATE;
  progressBarStart: number | null = null;
  progressBarEnd: number | null = null;
  checkExclusionCriteriaRangeWidth = 0;
  checkPermissibleDeviationRangeWidth = 0;
  compliantWithSpecificationsRangeWidth = 0;
  kpiRangesForm = this.initializeForm();
  customerType = CustomerType;

  customerType$ = this.store.select(selectCustomerType);
  selectedKpi$ = this.store.select(selectCurrentlySelectedKpi).pipe(
    filter(Boolean),
    distinctUntilChanged(isEqual),
    tap((kpi) => {
      if (!kpi.rangeDirection) {
        return;
      }
      this.isKpiRangeAscending = kpi.rangeDirection === 'ASC';
      this.isPercentageKpi = kpi.type === 'PERCENTAGE';
      this.applyValidators();
      if (!kpi.checkExclusionCriteriaRange) {
        this.resetKpiRangesState();
        return;
      }

      this.setKpiRangesState(kpi);
    }),
  );

  kpiToggleState$ = this.store.select(selectKpiToggleState);

  private isKpiRangeAscending: boolean;
  private isPercentageKpi: boolean;

  constructor(
    private destroyRef: DestroyRef,
    private fb: NonNullableFormBuilder,
    private store: Store,
    private regionalSettings: RegionalSettingsService,
  ) {
    this.destroyRef.onDestroy(() => {
      this.store.dispatch(
        StateLibKpiTogglePageActions.removeKpiListSearchTerm(),
      );
    });
  }

  get checkExclusionCriteriaRangeMinControl() {
    return this.kpiRangesForm.get(
      KpiRangesFormControl.CHECK_EXCLUSION_CRITERIA_RANGE_MIN,
    ) as FormControl<number>;
  }

  get checkExclusionCriteriaRangeMaxControl() {
    return this.kpiRangesForm.get(
      KpiRangesFormControl.CHECK_EXCLUSION_CRITERIA_RANGE_MAX,
    ) as FormControl<number>;
  }

  get checkPermissibleDeviationRangeMinControl() {
    return this.kpiRangesForm.get(
      KpiRangesFormControl.CHECK_PERMISSIBLE_DEVIATION_RANGE_MIN,
    ) as FormControl<number>;
  }

  get checkPermissibleDeviationRangeMaxControl() {
    return this.kpiRangesForm.get(
      KpiRangesFormControl.CHECK_PERMISSIBLE_DEVIATION_RANGE_MAX,
    ) as FormControl<number>;
  }

  get compliantWithSpecificationsRangeMinControl() {
    return this.kpiRangesForm.get(
      KpiRangesFormControl.COMPLIANT_WITH_SPECIFICATIONS_RANGE_MIN,
    ) as FormControl<number>;
  }

  get compliantWithSpecificationsRangeMaxControl() {
    return this.kpiRangesForm.get(
      KpiRangesFormControl.COMPLIANT_WITH_SPECIFICATIONS_RANGE_MAX,
    ) as FormControl<number>;
  }

  get firstInvalidControlError() {
    if (this.kpiRangesForm.touched && this.kpiRangesForm.invalid) {
      for (const key of Object.keys(this.kpiRangesForm.controls)) {
        const formControl = this.kpiRangesForm.get(key);
        if (formControl.touched && formControl.invalid) {
          return mapValidationErrorToLocalizedMessage(
            formControl.errors,
            this.isKpiRangeAscending,
            this.isPercentageKpi,
          );
        }
      }
    }
    return null;
  }

  ngOnInit(): void {
    this.subscribeToValueChanges().subscribe();
  }

  searchKpis(kpiListSearchTerm: string) {
    this.store.dispatch(
      StateLibKpiTogglePageActions.setKpiListSearchTerm({
        kpiListSearchTerm,
      }),
    );
  }

  toggleKpi(enabled: boolean): void {
    this.store.dispatch(
      enabled
        ? StateLibKpiTogglePageActions.disableKpi()
        : StateLibKpiTogglePageActions.enableKpi(),
    );
  }

  onSave() {
    if (this.kpiRangesForm.invalid) {
      for (const key of Object.keys(this.kpiRangesForm.controls)) {
        const formControl = this.kpiRangesForm.get(key);
        if (formControl.invalid) {
          this.kpiRangesForm.markAsTouched();
          formControl.markAsTouched();
          return;
        }
      }
      return;
    }

    const kpiRanges = {
      checkExclusionCriteriaRange: {
        min: this.checkExclusionCriteriaRangeMinControl.value,
        max: this.checkExclusionCriteriaRangeMaxControl.value,
      },
      checkPermissibleDeviationRange: {
        min: this.checkPermissibleDeviationRangeMinControl.value,
        max: this.checkPermissibleDeviationRangeMaxControl.value,
      },
      compliantWithSpecificationsRange: {
        min: this.compliantWithSpecificationsRangeMinControl.value,
        max: this.compliantWithSpecificationsRangeMaxControl.value,
      },
    };
    this.store.dispatch(
      StateLibKpiRangesPageActions.saveKpiRanges({ kpiRanges }),
    );
  }

  private initializeForm() {
    return this.fb.group({
      checkExclusionCriteriaRangeMin: [null as null | number],
      checkExclusionCriteriaRangeMax: [
        {
          value: null as null | number,
          disabled: true,
        },
      ],
      checkPermissibleDeviationRangeMin: [
        { value: null as null | number, disabled: true },
      ],
      checkPermissibleDeviationRangeMax: [
        {
          value: null as null | number,
          disabled: true,
        },
      ],
      compliantWithSpecificationsRangeMin: [
        { value: null as null | number, disabled: true },
      ],
      compliantWithSpecificationsRangeMax: [
        {
          value: null as null | number,
          disabled: true,
        },
      ],
    });
  }

  private subscribeToValueChanges() {
    return this.kpiRangesForm.valueChanges.pipe(
      takeUntilDestroyed(this.destroyRef),
      debounceTime(300),
      distinctUntilChanged((prev, next) => isEqual(prev, next)),
      startWith({
        checkExclusionCriteriaRangeMin: null,
        checkExclusionCriteriaRangeMax: null,
        checkPermissibleDeviationRangeMin: null,
        checkPermissibleDeviationRangeMax: null,
        compliantWithSpecificationsRangeMin: null,
        compliantWithSpecificationsRangeMax: null,
      }),
      pairwise(),
      tap(([currentValue, prevValue]) => {
        const { changedControlName, changedControl } = this.getChangedControl([
          prevValue,
          currentValue,
        ]);
        if (
          changedControlName ===
            KpiRangesFormControl.CHECK_EXCLUSION_CRITERIA_RANGE_MIN &&
          changedControl.valid
        ) {
          this.progressBarStart = changedControl.value;
          const correspondingMaxControl =
            this.checkExclusionCriteriaRangeMaxControl;

          correspondingMaxControl.disabled
            ? correspondingMaxControl.enable()
            : correspondingMaxControl.updateValueAndValidity();
          this.reflectUserInputOnProgressBar();
        }

        if (changedControlName?.endsWith('Max') && changedControl.valid) {
          this.handleNextRange(changedControlName, changedControl);
          this.reflectUserInputOnProgressBar();
        }
      }),
    );
  }

  private getChangedControl([prevValue, currentValue]: Partial<KpiRanges>[]) {
    const currentFormValues = currentValue;
    const permanentlyDisabledControls = [
      KpiRangesFormControl.CHECK_PERMISSIBLE_DEVIATION_RANGE_MIN,
      KpiRangesFormControl.COMPLIANT_WITH_SPECIFICATIONS_RANGE_MIN,
    ];
    const changedControlName = Object.keys(currentFormValues)
      .filter(
        (key) =>
          !permanentlyDisabledControls.includes(key as KpiRangesFormControl),
      )
      .find(
        (key) =>
          currentFormValues[key as keyof KpiRanges] !==
          prevValue[key as keyof KpiRanges],
      ) as keyof KpiRanges;

    const changedControl = this.kpiRangesForm.get(changedControlName);

    return { changedControlName, changedControl };
  }

  private handleNextRange(
    changedMaxControlName: keyof KpiRanges,
    changedMaxControl: AbstractControl,
  ) {
    const isTheLastRange =
      changedMaxControlName ===
      KpiRangesFormControl.COMPLIANT_WITH_SPECIFICATIONS_RANGE_MAX;
    if (isTheLastRange) {
      return;
    }

    const nextRangeMinControlName =
      changedMaxControlName ===
      KpiRangesFormControl.CHECK_EXCLUSION_CRITERIA_RANGE_MAX
        ? KpiRangesFormControl.CHECK_PERMISSIBLE_DEVIATION_RANGE_MIN
        : KpiRangesFormControl.COMPLIANT_WITH_SPECIFICATIONS_RANGE_MIN;

    const nextRangeMaxControlName =
      changedMaxControlName ===
      KpiRangesFormControl.CHECK_EXCLUSION_CRITERIA_RANGE_MAX
        ? KpiRangesFormControl.CHECK_PERMISSIBLE_DEVIATION_RANGE_MAX
        : KpiRangesFormControl.COMPLIANT_WITH_SPECIFICATIONS_RANGE_MAX;

    const nextRangeMinControl = this.kpiRangesForm.get(nextRangeMinControlName);
    const nextRangeMaxControl = this.kpiRangesForm.get(nextRangeMaxControlName);

    const rangeStep = this.isPercentageKpi ? 0.001 : 0.01;
    const factor = this.isPercentageKpi ? 1000 : 100;
    const nextRangeMinControlValue =
      Math.round(
        (this.isKpiRangeAscending
          ? changedMaxControl.value + rangeStep
          : changedMaxControl.value - rangeStep) * factor,
      ) / factor;

    nextRangeMinControl.setValue(nextRangeMinControlValue);
    nextRangeMaxControl.disabled
      ? nextRangeMaxControl.enable()
      : nextRangeMaxControl.updateValueAndValidity();
  }

  private reflectUserInputOnProgressBar() {
    const populatedInputs = Object.values(
      this.kpiRangesForm.getRawValue(),
    ).filter((value) => value !== null);

    const firstNonAscendingIndex = populatedInputs.findIndex(
      (value, index, array) =>
        this.isKpiRangeAscending
          ? index > 0 && value <= array[index - 1]
          : index > 0 && value >= array[index - 1],
    );

    const validRanges =
      firstNonAscendingIndex === -1
        ? populatedInputs
        : populatedInputs.slice(0, firstNonAscendingIndex);

    if (validRanges.length > 1) {
      this.progressBarEnd = this.isKpiRangeAscending
        ? Math.max(...validRanges.filter((_, index) => index % 2 !== 0))
        : Math.min(...validRanges.filter((_, index) => index % 2 !== 0));
    }

    const progressBarTotalRange = this.progressBarEnd - this.progressBarStart;
    const scalingFactor = 100 / progressBarTotalRange;

    if (validRanges.length === 1 || validRanges.length === 3) {
      this.checkExclusionCriteriaRangeWidth = 100;
      this.checkPermissibleDeviationRangeWidth = 0;
      this.compliantWithSpecificationsRangeWidth = 0;
    } else if (validRanges.length === 5) {
      this.checkExclusionCriteriaRangeWidth =
        (validRanges[1] - validRanges[0]) * scalingFactor;
      this.checkPermissibleDeviationRangeWidth =
        (validRanges[3] - validRanges[2]) * scalingFactor;
      this.compliantWithSpecificationsRangeWidth = 0;
      this.adjustRangeWidthsToFillProgressBar();
    } else if (validRanges.length === 6) {
      this.checkExclusionCriteriaRangeWidth =
        (validRanges[1] - validRanges[0]) * scalingFactor;
      this.checkPermissibleDeviationRangeWidth =
        (validRanges[3] - validRanges[2]) * scalingFactor;
      this.compliantWithSpecificationsRangeWidth =
        (validRanges[5] - validRanges[4]) * scalingFactor;
      this.adjustRangeWidthsToFillProgressBar();
    }
  }

  private adjustRangeWidthsToFillProgressBar() {
    const totalRangesWidth =
      this.checkExclusionCriteriaRangeWidth +
      this.checkPermissibleDeviationRangeWidth +
      (this.compliantWithSpecificationsRangeWidth ?? 0);
    if (totalRangesWidth === 100) {
      return;
    }

    const leftOverWidthOnProgressBar = 100 - totalRangesWidth;
    const widthToAddPerRange = leftOverWidthOnProgressBar / totalRangesWidth;

    this.checkExclusionCriteriaRangeWidth +=
      this.checkExclusionCriteriaRangeWidth * widthToAddPerRange;
    this.checkPermissibleDeviationRangeWidth +=
      this.checkPermissibleDeviationRangeWidth * widthToAddPerRange;
    if (this.compliantWithSpecificationsRangeWidth) {
      this.compliantWithSpecificationsRangeWidth +=
        this.compliantWithSpecificationsRangeWidth * widthToAddPerRange;
    }
  }

  private resetKpiRangesState() {
    if (
      !this.checkExclusionCriteriaRangeMinControl.value &&
      this.checkExclusionCriteriaRangeMinControl.untouched
    ) {
      return;
    }

    this.kpiRangesForm.reset();
    this.checkExclusionCriteriaRangeMaxControl.disable();
    this.checkPermissibleDeviationRangeMaxControl.disable();
    this.compliantWithSpecificationsRangeMaxControl.disable();
    this.progressBarStart = null;
    this.progressBarEnd = null;
    this.checkExclusionCriteriaRangeWidth = 0;
    this.checkPermissibleDeviationRangeWidth = 0;
    this.compliantWithSpecificationsRangeWidth = 0;
  }

  private setKpiRangesState(kpi: CustomerKpi) {
    this.kpiRangesForm.setValue({
      checkExclusionCriteriaRangeMin: kpi.checkExclusionCriteriaRange.min,
      checkExclusionCriteriaRangeMax: kpi.checkExclusionCriteriaRange.max,
      checkPermissibleDeviationRangeMin: kpi.checkPermissibleDeviationRange.min,
      checkPermissibleDeviationRangeMax: kpi.checkPermissibleDeviationRange.max,
      compliantWithSpecificationsRangeMin:
        kpi.compliantWithSpecificationsRange.min,
      compliantWithSpecificationsRangeMax:
        kpi.compliantWithSpecificationsRange.max,
    });
    this.checkPermissibleDeviationRangeMaxControl.enable();
    this.compliantWithSpecificationsRangeMaxControl.enable();
  }

  private applyValidators() {
    const kpiRangeConstraints = {
      asc: [
        { min: -200, max: this.isPercentageKpi ? 199.995 : 199.95 },
        {
          min: this.isPercentageKpi ? -199.999 : -199.99,
          max: this.isPercentageKpi ? 199.996 : 199.96,
        },
        {
          min: this.isPercentageKpi ? -199.997 : -199.97,
          max: this.isPercentageKpi ? 199.998 : 199.98,
        },
        { min: this.isPercentageKpi ? -199.995 : -199.95, max: 200 },
      ],
      desc: [
        { min: this.isPercentageKpi ? -199.995 : -199.95, max: 200 },
        {
          min: this.isPercentageKpi ? -199.996 : -199.96,
          max: this.isPercentageKpi ? 199.999 : 199.99,
        },
        {
          min: this.isPercentageKpi ? -199.998 : -199.98,
          max: this.isPercentageKpi ? 199.997 : 199.97,
        },
        { min: -200, max: this.isPercentageKpi ? 199.995 : 199.95 },
      ],
    };

    const validatedControlNames = [
      KpiRangesFormControl.CHECK_EXCLUSION_CRITERIA_RANGE_MIN,
      KpiRangesFormControl.CHECK_EXCLUSION_CRITERIA_RANGE_MAX,
      KpiRangesFormControl.CHECK_PERMISSIBLE_DEVIATION_RANGE_MAX,
      KpiRangesFormControl.COMPLIANT_WITH_SPECIFICATIONS_RANGE_MAX,
    ];

    validatedControlNames.forEach((controlName, index) => {
      const currentRangeConstraints = this.isKpiRangeAscending
        ? kpiRangeConstraints.asc[index]
        : kpiRangeConstraints.desc[index];
      const validators = [
        Validators.required,
        Validators.min(currentRangeConstraints.min),
        Validators.max(currentRangeConstraints.max),
      ];

      if (controlName.endsWith('Max')) {
        validators.push(
          kpiRangeMaxValidator(
            controlName.replace('Max', 'Min'),
            this.isKpiRangeAscending,
          ),
        );
      }

      this.kpiRangesForm.get(controlName).setValidators(validators);
      this.kpiRangesForm.get(controlName).updateValueAndValidity();
    });
  }
}
