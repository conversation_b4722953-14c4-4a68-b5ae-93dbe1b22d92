import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {
  SectionApplicationComponent,
  SectionInvitationComponent,
  financingStructureActivateGuard,
} from '@fincloud/neoshare/business-case';
import {
  neogptChatActivateGuard,
  neogptChatDeactivateGuard,
} from '@fincloud/neoshare/neogpt-chat';
import {
  FinancingDetailsPath,
  FinancingDetailsSubPage,
  NeoGptActiveSession,
} from '@fincloud/types/enums';
import { RefsContentComponent } from './components/refs-content/refs-content.component';
import { RefsFinancingDetailsComponent } from './components/refs-financing-details/refs-financing-details.component';
import { RefsParticipationComponent } from './components/refs-participation/refs-participation.component';
import { RefsSharedFinancingStructureComponent } from './components/refs-shared-financing-structure/refs-shared-financing-structure.component';
import { financingDetailsRealEstateActivateGuard } from './guards/financing-details-real-estate-activate.guard';
import { financingStructureDeactivateGuard } from './guards/financing-structure-deactivate.guard';
import { financingStructureResolver } from './guards/financing-structure-resolver.guard';
import { initialSharedGroupsResolver } from './guards/initial-shared-groups-resolver';
import { participationRealEstateMatchGuard } from './guards/participation-real-estate-match.guard';
import { refsResolver } from './guards/refs-resolver';
import { sectionApplicationRealEstateMatchGuard } from './guards/section-application-real-estate-match.guard';
import { sectionInvitationRealEstateMatchGuard } from './guards/section-invitation-real-estate-match.guard';
import { sharedFinancingStructureActivateGuard } from './guards/shared-financing-structure-activate.guard';
const routes: Routes = [
  {
    path: '',
    component: RefsFinancingDetailsComponent,
    canActivate: [
      financingDetailsRealEstateActivateGuard,
      financingStructureActivateGuard,
    ],
    resolve: [refsResolver],
    children: [
      {
        path: FinancingDetailsPath.FINANCING_STRUCTURE,
        data: {
          activePath: FinancingDetailsSubPage.FINANCING_STRUCTURE,
          activeSession: NeoGptActiveSession.FINANCING_DETAILS,
        },
        component: RefsContentComponent,
        canActivate: [neogptChatActivateGuard],
        canDeactivate: [
          neogptChatDeactivateGuard,
          financingStructureDeactivateGuard,
        ],
        resolve: [financingStructureResolver],
      },
      {
        path: FinancingDetailsPath.SHARED_FINANCING_STRUCTURE,
        canActivate: [sharedFinancingStructureActivateGuard],
        canDeactivate: [financingStructureDeactivateGuard],
        resolve: [initialSharedGroupsResolver],
        data: {
          activePath: FinancingDetailsSubPage.SHARED_FINANCING_STRUCTURE,
        },
        component: RefsSharedFinancingStructureComponent,
      },
      {
        path: FinancingDetailsPath.MY_PARTICIPATION,
        component: RefsParticipationComponent,
        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },
        canMatch: [participationRealEstateMatchGuard],
      },
      {
        path: FinancingDetailsPath.MY_PARTICIPATION,
        component: SectionInvitationComponent,
        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },
        canMatch: [sectionInvitationRealEstateMatchGuard],
      },
      {
        path: FinancingDetailsPath.MY_PARTICIPATION,
        component: SectionApplicationComponent,
        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },
        canMatch: [sectionApplicationRealEstateMatchGuard],
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BusinessCaseRealEstateFinancingStructureRoutingModule {}
