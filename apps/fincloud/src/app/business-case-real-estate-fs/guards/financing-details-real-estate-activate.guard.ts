import { inject } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  GuardR<PERSON>ult,
  MaybeAsync,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import { selectHasAnyBusinessCasePermission } from '@fincloud/state/business-case';
import {
  BusinessCasePermission,
  FinancingDetailsPath,
} from '@fincloud/types/enums';
import { Store } from '@ngrx/store';
import { map } from 'rxjs';

export function financingDetailsRealEstateActivateGuard(
  next: ActivatedRouteSnapshot,
  state: RouterStateSnapshot,
): MaybeAsync<GuardResult> {
  const router = inject(Router);
  const store = inject(Store);
  return store
    .select(
      selectHasAnyBusinessCasePermission([
        BusinessCasePermission.BCP_00064,
        BusinessCasePermission.BCP_00066,
      ]),
    )
    .pipe(
      map((canActivate) => {
        if (!state.url.endsWith(FinancingDetailsPath.FINANCING_DETAILS)) {
          return true;
        }
        const segments = state.url.split('/').slice(1);
        if (canActivate) {
          return router.createUrlTree(
            segments.concat(FinancingDetailsPath.FINANCING_STRUCTURE),
          );
        }

        return router.createUrlTree(
          segments.concat([FinancingDetailsPath.MY_PARTICIPATION]),
        );
      }),
    );
}
