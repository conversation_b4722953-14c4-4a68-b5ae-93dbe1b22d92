import { FieldTypeEnum } from '@fincloud/core/formly';
import { GroupKey } from '@fincloud/types/enums';
import { CustomFinStructureGroup } from '@fincloud/types/models';
import { REFS_BLOCKS_GROUP_KEY } from '@fincloud/utils';
import { isNil } from 'lodash-es';
import { SectionField } from './section-field';

function mapBuildingBlockGroupToSectionField(
  group: CustomFinStructureGroup,
  dynamicBlocks: CustomFinStructureGroup,
) {
  if (group.key !== REFS_BLOCKS_GROUP_KEY || isNil(dynamicBlocks?.subGroups)) {
    return null;
  }

  return {
    fields: dynamicBlocks.subGroups.map((item) => ({
      id: item.key,
      fieldName: item.group,
      key: item.key,
      isDynamic: true,
      isEnabled: true,
      isEditable: false,
    })),
    isField: true,
  };
}

function mapPayoutCriteriaGroupToSectionField(group: CustomFinStructureGroup) {
  if (group.key !== GroupKey.PAYOUT_CRITERIA) {
    return null;
  }
  return {
    fields: group.subGroups.map(
      (group: CustomFinStructureGroup) =>
        new SectionField(
          group.id,
          group.group,
          false,
          group.key,
          group.fields?.every((field) => !field.disabled),
          true,
          !!group.fields.find(
            (field) =>
              field.fieldType === FieldTypeEnum.DATE &&
              !field.disabled &&
              !!field.value,
          ),
        ),
    ),
    isField: false,
  };
}

function mapExternalRequirementsToSectionField(group: CustomFinStructureGroup) {
  if (group.key !== GroupKey.EXTERNAL_REQUIREMENTS) {
    return null;
  }

  return {
    fields: group.subGroups.map(
      (group) =>
        new SectionField(
          group.id,
          group.group,
          false,
          group.key,
          group.fields?.every((field) => !field.disabled),
          true,
        ),
    ),
    isField: false,
  };
}

function mapSecuritiesAndCovenantsToSectionField(
  group: CustomFinStructureGroup,
) {
  if (group.key !== GroupKey.SECURITIES_AND_COVENANTS) {
    return null;
  }
  return {
    fields: group.subGroups.map(
      (group) =>
        new SectionField(
          group.id,
          group.group,
          false,
          group.key,
          group.fields?.every((field) => !field.disabled),
          true,
        ),
    ),
    isField: false,
  };
}

export function mapGroupToSideNavigation(
  group: CustomFinStructureGroup,
  dynamicGroup: CustomFinStructureGroup,
) {
  return (
    mapBuildingBlockGroupToSectionField(group, dynamicGroup) ||
    mapExternalRequirementsToSectionField(group) ||
    mapSecuritiesAndCovenantsToSectionField(group) ||
    mapPayoutCriteriaGroupToSectionField(group) || {
      isField: false,
      fields: [],
    }
  );
}
