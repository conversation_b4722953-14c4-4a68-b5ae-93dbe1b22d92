import { SpecialFieldLabelOrKeyEnum } from '@fincloud/components/refs';
import { ChartData, CustomFinStructureGroup } from '@fincloud/types/models';
import { REFS_BLOCKS_GROUP_KEY } from '@fincloud/utils';
import { CustomGroupMetadata } from '../models/custom-group-metadata';

export function collectRealEstateChartData(
  staticGroups: CustomFinStructureGroup[],
  breakdownData: CustomGroupMetadata[],
  totalParticipationAmount: number,
): ChartData[] {
  if (!totalParticipationAmount) {
    return [] as ChartData[];
  }

  let dfsValueError = false;

  const staticGroup = staticGroups?.find(
    ({ key }) => key === REFS_BLOCKS_GROUP_KEY,
  );
  const staticGroupField = staticGroup?.fields?.find(
    ({ key }) => key === SpecialFieldLabelOrKeyEnum.REQUESTED_FINANCING_VOLUME,
  );
  const staticGroupFieldValue = staticGroupField?.value?.toString() || '0';

  const totalFieldSetValue = staticGroupFieldValue
    ? parseFloat(staticGroupFieldValue)
    : 0;

  if (
    totalFieldSetValue <= 0 ||
    (totalParticipationAmount && !breakdownData?.length)
  ) {
    return [
      {
        name: $localize`:@@dashboard.businessCase.collected:Gesammelt`,
        value: totalParticipationAmount,
      } as ChartData,
    ];
  }
  const chartDataArray = breakdownData.map((breakDownItem) => {
    const amountOfSection = breakDownItem.amountFields.reduce(
      (accumulator, currentBreakDownItem) => {
        const amount = parseFloat(currentBreakDownItem.value.toString());

        if (!dfsValueError && amount > totalFieldSetValue) {
          dfsValueError = true;
        }
        return accumulator + amount;
      },
      0,
    );

    return {
      name: breakDownItem.group,
      value: Number(
        (
          (amountOfSection / totalFieldSetValue) *
          totalParticipationAmount
        ).toFixed(2),
      ),
      extra: { dfsValueError },
    } as ChartData;
  });
  return chartDataArray;
}
