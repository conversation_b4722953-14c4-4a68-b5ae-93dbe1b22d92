import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  TemplateRef,
} from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import { FinStructureGroup } from '@fincloud/swagger-generator/financing-details';
import { CustomerType } from '@fincloud/types/enums';
import { CustomFinStructureField } from '@fincloud/types/models';
import { FinBadgeStatus } from '@fincloud/ui/badges';
import { FinSize } from '@fincloud/ui/types';
import { HIGHLIGHT_PREFIX } from '@fincloud/utils';
import { NgxCurrencyConfig } from 'ngx-currency';

@Component({
  selector: 'app-refs-multiselect-field',
  templateUrl: './refs-multiselect-field.component.html',
  styleUrls: ['./refs-multiselect-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RefsMultiselectFieldComponent {
  public fieldValue = '';
  public fieldMetaData: string[] = [];
  highlightPrefix = HIGHLIGHT_PREFIX;
  private _isEditMode = false;
  @Input() set isEditMode(value: boolean) {
    this.disableFormGroup(value);
    this._isEditMode = value;
  }
  get isEditMode() {
    return this._isEditMode;
  }
  @Input() optionsTemplate: TemplateRef<unknown>;
  @Input() finInputLabelTemplate: TemplateRef<unknown>;
  @Input() isFinStructureField = false;
  @Input() businessCase: ExchangeBusinessCase;
  @Input() finStructureId: string;
  @Input() emitValueOnChange = false;
  @Input() hasFreeTextEnabled = false;
  @Input() group: FinStructureGroup;
  @Input() fieldGroupFieldsetName: string | null = null;
  @Input() mirroredFieldKeys: string[];
  @Input() isCustomerLead: boolean;
  @Input() customerType: CustomerType;

  readonly isAdditionalFieldAttached = true;
  private _field: CustomFinStructureField | null = null;
  get field() {
    return this._field;
  }
  @Input() set field(newValue: CustomFinStructureField) {
    this._field = newValue;
    this.fieldMetaData = newValue.fieldMetaData as string[];
    this.fieldValue = (newValue.value as string) || '';
    this.handleFormControls(newValue.dependents);
  }
  @Input() value: string | null = null;
  @Input() instantFeedback = false;
  @Input() configurationOverrides: Partial<NgxCurrencyConfig>;
  @Output() feedback = new EventEmitter();
  @Output() save = new EventEmitter();
  @Output() valueChecked = new EventEmitter();

  formGroup = new FormGroup<Record<string, FormControl>>({});
  readonly finBadgeStatus = FinBadgeStatus;
  readonly finSize = FinSize;

  onCheckboxChange(isChecked: boolean, key: string): void {
    this.valueChecked.emit({ isChecked, value: key });
  }

  private handleFormControls(
    fields: Record<string, CustomFinStructureField[]>,
  ): void {
    for (const property in fields) {
      const isChecked = this.fieldValue.includes(property);
      this.formGroup.addControl(
        property,
        new FormControl({ value: isChecked, disabled: !this.isEditMode }),
      );
    }
  }

  private disableFormGroup(isEnabled: boolean): void {
    if (isEnabled) {
      return this.formGroup.enable();
    }
    this.formGroup.disable();
  }
}
