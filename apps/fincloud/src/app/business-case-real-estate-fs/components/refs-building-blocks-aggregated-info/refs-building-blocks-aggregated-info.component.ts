import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { RefsGroupType } from '@fincloud/components/refs';
import { selectBusinessCase } from '@fincloud/state/business-case';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import { FinStructureField } from '@fincloud/swagger-generator/financing-details';
import { CustomFinStructureGroup } from '@fincloud/types/models';
import { REFS_BLOCKS_GROUP_KEY } from '@fincloud/utils';
import { Store } from '@ngrx/store';
import { Observable, filter, map, switchMap } from 'rxjs';
import {
  selectIsSharedRefsTabActive,
  selectStaticGroups,
  selectStaticGroupsFromCustomer,
} from '../../+state/selectors';
import { CustomGroupMetadata } from '../../models/custom-group-metadata';
import { GroupField } from '../../models/group-field';

@Component({
  selector: 'app-refs-building-blocks-aggregated-info',
  templateUrl: './refs-building-blocks-aggregated-info.component.html',
  styleUrls: ['./refs-building-blocks-aggregated-info.component.scss'],
})
export class RefsBuildingBlocksAggregatedInfoComponent implements OnInit {
  @Output() addFinancingBlock: EventEmitter<void> = new EventEmitter();

  readonly refsGroupType = RefsGroupType;

  readonly isSharedActiveRefsTab$: Observable<boolean> = this.store.select(
    selectIsSharedRefsTabActive,
  );

  readonly financingStructure$: Observable<CustomFinStructureGroup[]> =
    this.isSharedActiveRefsTab$.pipe(
      switchMap((isSharedActiveRefsTab) =>
        isSharedActiveRefsTab
          ? this.store.select(selectStaticGroupsFromCustomer)
          : this.store.select(selectStaticGroups),
      ),
    );

  readonly buildingBlockGroup$: Observable<CustomFinStructureGroup> =
    this.financingStructure$.pipe(
      map((finStructure) =>
        finStructure?.find((g) => g.key === REFS_BLOCKS_GROUP_KEY),
      ),
    );

  readonly businessCase$: Observable<ExchangeBusinessCase> =
    this.store.select(selectBusinessCase);

  topFields$: Observable<FinStructureField[]>;
  canUpdateValues = false;
  ungroupedFieldSets: CustomGroupMetadata[];
  groupedFieldSets: CustomGroupMetadata[];
  fieldFillStates: { [key: string]: boolean };

  private fieldStates: GroupField[];

  constructor(private store: Store) {}

  ngOnInit() {
    this.topFields$ = this.buildingBlockGroup$.pipe(
      filter(Boolean),
      map((buildingBlockGroup) => buildingBlockGroup.fields),
    );
  }

  setFieldChange(newValue: string, field: FinStructureField) {
    const foundField = this.fieldStates.find(
      (item) => item.fieldsetName === field.label && item.fieldId === field.id,
    );
    const newFieldValue = parseFloat(newValue);
    foundField.value = newFieldValue;

    this.fieldFillStates[field.label] = !!newFieldValue;

    this.canUpdateValues = this.fieldStates.some(
      (item) => item.initialValue !== item.value,
    );
  }
}
