import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { SEARCH_FINANCING_STRUCTURE_TYPEAHEAD_NO_RESULTS } from '@fincloud/utils';

@Component({
  selector: 'app-refs-no-results',
  styleUrl: './refs-no-results.component.scss',
  templateUrl: './refs-no-results.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RefsNoResultsComponent {
  @Input() message: string = SEARCH_FINANCING_STRUCTURE_TYPEAHEAD_NO_RESULTS;
}
