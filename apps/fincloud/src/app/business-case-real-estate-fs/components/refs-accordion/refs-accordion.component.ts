import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  ViewChild,
} from '@angular/core';
import { RefsGroupType } from '@fincloud/components/refs';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import { CustomerType, FieldType } from '@fincloud/types/enums';
import {
  CustomFinStructureField,
  CustomFinStructureGroup,
} from '@fincloud/types/models';
import { FinBadgeStatus } from '@fincloud/ui/badges';
import {
  FinAccordionComponent,
  FinAccordionToggleDirection,
  FinAccordionType,
} from '@fincloud/ui/expansion-panel';
import { FinSize } from '@fincloud/ui/types';
import { REFS_BLOCKS_GROUP_KEY } from '@fincloud/utils';
import { isEmpty } from 'lodash-es';

@Component({
  selector: 'app-refs-accordion',
  templateUrl: './refs-accordion.component.html',
  styleUrls: ['./refs-accordion.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RefsAccordionComponent {
  @Input() staticGroups: CustomFinStructureGroup[] = [];
  @Input() mirroredFieldKeys: string[] = [];
  @Input() isCustomerLead: boolean;
  @Input() firstIdToExpand = '';
  @Input() secondIdToExpand = '';
  @Input() highlightId = '';
  @Input() closeOthers = true;
  @Input() highlightPrefix = '';
  @Input() matchingToSearchGroupIds: string[] = [];
  @Input() businessCase: ExchangeBusinessCase;
  @Input() finStructureId = '';
  @Input() isEditMode = false;
  @Input() shouldExpandSubAccordions = false;
  @Input() customerType: CustomerType;
  @Output() scrollToExpandedGroup = new EventEmitter<string>();

  @Output() clearScrollToSection = new EventEmitter<void>();
  refsGroupType = RefsGroupType;
  fieldType = FieldType;
  refsBuildingBlocks = REFS_BLOCKS_GROUP_KEY;
  toggleDirection = FinAccordionToggleDirection.AUTO;
  finSize = FinSize;
  readonly finBadgeStatus = FinBadgeStatus;
  readonly finAccordionType = FinAccordionType;

  @ViewChild('panel') private panel: FinAccordionComponent;
  @ViewChild('subPanel') private subPanel: FinAccordionComponent;

  infoBadgeTooltip = $localize`:@@refs.accordion.infoBadge.tooltip:Mit Datenraum synchronisiert`;
  expandMainAccordions(): void {
    this.panel?.openAll();
  }
  onScrollToExpandedGroup(groupId: string) {
    if (isEmpty(this.matchingToSearchGroupIds) && !this.highlightId) {
      this.scrollToExpandedGroup.emit(groupId);
    }
  }

  collapseMainAccordions(): void {
    this.panel?.closeAll();
  }

  expandSubAccordions(): void {
    if (this.shouldExpandSubAccordions) {
      this.subPanel?.openAll();
    }
  }

  trackById(index: number, item: CustomFinStructureField): string {
    //TODO fix it properly, revisions component : VOL-1973
    return item.id + item.value;
  }

  onClearScrollToSection() {
    if (this.firstIdToExpand || this.secondIdToExpand || this.highlightId) {
      this.clearScrollToSection.emit();
    }
  }
}
