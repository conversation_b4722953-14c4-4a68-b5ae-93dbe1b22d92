import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FinButtonAppearance } from '@fincloud/ui/button';
import { FinSize } from '@fincloud/ui/types';
import { HIGHLIGHT_PREFIX } from '@fincloud/utils';
import { isNil } from 'lodash-es';
import { SelectedFinancingBlockInfo } from '../../models/refs-dfs';

@Component({
  selector: 'app-refs-financing-block',
  templateUrl: './refs-financing-block.component.html',
  styleUrls: ['./refs-financing-block.component.scss'],
})
export class RefsFinancingBlockComponent {
  @Input() id: string;
  @Input() activeBlockId: string;
  @Input() blockId: string;
  @Input() title: string;
  @Input() subTitle: string;
  @Input() isReadOnly = false;
  @Input() amount = 0;
  @Input() highlightedBlockId: string;
  @Output() deleteBlockTrigger = new EventEmitter<string>();
  @Output() selectBlockTrigger = new EventEmitter<SelectedFinancingBlockInfo>();

  isNil = isNil;

  showDelete: boolean;
  highlightPrefix = HIGHLIGHT_PREFIX;
  readonly finButtonAppearance = FinButtonAppearance;
  readonly finSize = FinSize;

  deleteBlock(e: Event) {
    e.preventDefault();
    e.stopPropagation();
    this.deleteBlockTrigger.emit(this.blockId);
  }

  onSelectBlock() {
    this.selectBlockTrigger.emit({
      fieldsetName: this.subTitle,
      id: this.blockId,
    });
  }

  wrapperClick(e: Event) {
    e.preventDefault();
    e.stopPropagation();
    this.onSelectBlock();
  }
}
