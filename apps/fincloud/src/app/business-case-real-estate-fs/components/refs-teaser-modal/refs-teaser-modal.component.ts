import { <PERSON><PERSON><PERSON>cyPipe, DatePipe, DecimalPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { CellData, DataGridConfig } from '@fincloud/components/data-grid';
import { SelectGroupTreeComponent } from '@fincloud/components/select-group-tree';
import { RemoveTrailingZerosPipe } from '@fincloud/core/pipes';
import { StateLibTeaserExportPageActions } from '@fincloud/state/business-case';
import { selectUserCustomerKey } from '@fincloud/state/user';
import { Group } from '@fincloud/swagger-generator/document-generator';
import { FinStructureField } from '@fincloud/swagger-generator/financing-details';
import { CustomFinStructureGroup } from '@fincloud/types/models';
import { FinButtonAppearance } from '@fincloud/ui/button';
import { FinDropdownOption } from '@fincloud/ui/dropdown';
import { FinSize } from '@fincloud/ui/types';
import { FinWarningMessageAppearance } from '@fincloud/ui/warning-message';
import { TABLE_NUMERIC_FIELD_TYPES } from '@fincloud/utils';
import { Store } from '@ngrx/store';
import { isNil } from 'lodash-es';
import { Observable, tap } from 'rxjs';
import { TeaserExportPageActions } from '../../+state/actions';
import { teaserExportFeature } from '../../+state/reducers/teaser-export.reducer';
import { selectParticipantsForExportFinancintStructure } from '../../+state/selectors';

@Component({
  selector: 'app-refs-teaser-modal',
  templateUrl: './refs-teaser-modal.component.html',
  styleUrls: ['./refs-teaser-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [DatePipe, CurrencyPipe, DecimalPipe, RemoveTrailingZerosPipe],
})
export class RefsTeaserModalComponent {
  @ViewChild('selectGroupTree')
  selectGroupTreeComponent: SelectGroupTreeComponent;
  readonly formGroup = new FormGroup({
    selectedParticipantKey: new FormControl('', Validators.required),
    groupTreeForm: new FormGroup({}),
  });

  userCustomerKey$: Observable<string> = this.store.select(
    selectUserCustomerKey,
  );
  participants$: Observable<FinDropdownOption[]> = this.store
    .select(selectParticipantsForExportFinancintStructure)
    .pipe(
      tap((participants) => {
        if (participants.length) {
          // Initial own financing structure for export
          this.formGroup.controls.selectedParticipantKey.setValue(
            participants[0].value,
          );
        }
      }),
    );

  finStructureTeaserExport$ = this.store.select(
    teaserExportFeature.selectFinStructureTeaserExport,
  );

  finWarningMessageAppearance = FinWarningMessageAppearance;
  finSize = FinSize;
  finButtonAppearance = FinButtonAppearance;

  constructor(
    private store: Store,
    private datePipe: DatePipe,
    private currencyPipe: CurrencyPipe,
    private decimalPipe: DecimalPipe,
    private removeTrailingZerosPipe: RemoveTrailingZerosPipe,
  ) {}

  downloadTeaser(
    groups: CustomFinStructureGroup[],
    disabledGroupIds: string[],
  ): void {
    const selectedGroupsForExport = [
      ...this.selectGroupTreeComponent.collectedGroupIds,
    ];
    const staticGroups = this.groupsForExport(
      groups,
      disabledGroupIds,
      selectedGroupsForExport,
    );

    this.store.dispatch(
      StateLibTeaserExportPageActions.downloadFinancingTeaserExport({
        staticGroups,
        customerKey: this.formGroup.controls.selectedParticipantKey.value,
      }),
    );
  }

  onSelectionChange(customerKey: string, userCustomerKey: string): void {
    if (isNil(customerKey)) {
      this.store.dispatch(TeaserExportPageActions.clearTeaserExport());
      return;
    }

    if (customerKey === userCustomerKey) {
      return this.store.dispatch(
        TeaserExportPageActions.getMyFinStructureTeaser(),
      );
    }
    this.store.dispatch(
      TeaserExportPageActions.getCustomerFinStructureTeaser({
        customerKey,
      }),
    );
  }

  onAutoCompleteChange(query: string): void {
    //Deleting char by char from the input
    if (!query) {
      this.store.dispatch(TeaserExportPageActions.clearTeaserExport());
    }
  }

  private finStructureGroupToTeaserGroup(
    group: CustomFinStructureGroup,
    disabledGroupIds: string[],
  ): Group | null {
    if (disabledGroupIds.includes(group.id)) {
      return null;
    }
    return {
      group: group.group,
      fields: group.fields.map((field) =>
        this.finStructureFieldToTeaserField(field),
      ),
      subGroups: group.subGroups
        .map((subGroup) =>
          this.finStructureGroupToTeaserGroup(subGroup, disabledGroupIds),
        )
        .filter(Boolean),
    };
  }

  transformFieldValue(fieldType: string, value: string | number) {
    // This check is necessary because the table fields do not have validations.
    if (TABLE_NUMERIC_FIELD_TYPES.includes(fieldType) && isNaN(Number(value))) {
      return value;
    }
    switch (fieldType) {
      case 'PERCENT':
        return this.removeTrailingZerosPipe.transform(
          this.decimalPipe.transform(value) + '%',
        );

      case 'MONETARY':
      case 'CURRENCY':
        return this.removeTrailingZerosPipe.transform(
          this.currencyPipe.transform(value),
        );

      case 'DECIMAL':
        return this.removeTrailingZerosPipe.transform(
          this.decimalPipe.transform(value),
        );

      case 'INTEGER':
        return this.decimalPipe.transform(value);

      case 'DATE':
        return this.datePipe.transform(value);

      default:
        return value;
    }
  }

  private formatTableFieldValue(field: FinStructureField) {
    const updatedRowData = (field.value as DataGridConfig).rowData.map(
      (row: Record<string, unknown>) => {
        const updatedRow: Record<string, CellData> = {};

        for (const key in row) {
          const cell = row[key] as CellData;
          const dataType = cell?.formatting?.dataType;

          if (dataType && !isNil(cell.value)) {
            updatedRow[key] = {
              ...cell,
              value: this.transformFieldValue(
                dataType === 'date' ? dataType : dataType.toUpperCase(),
                cell.value,
              ) as string,
            };
          } else {
            updatedRow[key] = cell;
          }
        }

        return updatedRow;
      },
    );

    return {
      ...field.value,
      rowData: updatedRowData,
    };
  }

  private finStructureFieldToTeaserField(
    field: FinStructureField,
  ): Pick<FinStructureField, 'fieldType' | 'label' | 'value'> {
    const value =
      field.fieldType === 'TABLE'
        ? this.formatTableFieldValue(field)
        : this.transformFieldValue(
            field.fieldType,
            field.value as string | number,
          );

    return {
      fieldType: field.fieldType,
      label: field.label,
      value,
    };
  }

  private groupsForExport(
    groups: CustomFinStructureGroup[],
    disabledGroupIds: string[],
    selectedGroupsForExport: string[],
  ): Group[] {
    const staticGroups: Group[] = [];
    groups.forEach((group) => {
      const subGroupIdForExport = group.subGroups?.some((subGroup) =>
        selectedGroupsForExport.includes(subGroup.id),
      );
      if (selectedGroupsForExport.includes(group.id) || subGroupIdForExport) {
        const subGroups: Group[] = [];

        group.subGroups.forEach((subGroup) => {
          if (selectedGroupsForExport.includes(subGroup.id)) {
            subGroups.push({
              group: subGroup.group,
              fields: subGroup.fields.map((field) =>
                this.finStructureFieldToTeaserField(field),
              ),
              subGroups: subGroup.subGroups
                .map((subSubGroup) =>
                  this.finStructureGroupToTeaserGroup(
                    subSubGroup,
                    disabledGroupIds,
                  ),
                )
                .filter(Boolean),
            });
          }
        });
        staticGroups.push({
          group: group.group,
          fields: group.fields.map((field) =>
            this.finStructureFieldToTeaserField(field),
          ),
          subGroups,
        });
      }
    });
    return staticGroups;
  }
}
