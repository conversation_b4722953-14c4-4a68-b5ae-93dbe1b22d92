<div class="content">
  <ng-container *ngTemplateOutlet="fieldComponent"></ng-container>
</div>

<!-- FIELD COMPONENT TEMPLATE -->
<ng-template #fieldComponent>
  @switch (field.fieldType) {
    @case (fieldType.BOOLEAN) {
      <ui-boolean-field
        [class.has-error]="hasError"
        [isEditMode]="isEditUnlocked"
        [optionsTemplate]="menuOptions"
        [emitValueOnChange]="emitValueOnChange"
        [placeHolder]="placeHolder"
        [isFinStructureField]="true"
        [field]="field"
        [value]="value"
        [instantFeedback]="instantFeedback"
        (feedback)="onFeedback($event)"
        (save)="onValueSaved($event)"
      ></ui-boolean-field>
    }
    @case (fieldType.DATE) {
      <ui-date-field
        dynamicErrorSpace="dynamic"
        [class.has-error]="hasError"
        [isEditMode]="isEditUnlocked"
        [placeHolder]="placeHolder"
        [optionsTemplate]="menuOptions"
        [isFinStructureField]="true"
        [emitValueOnChange]="emitValueOnChange"
        [field]="field"
        [value]="value"
        [instantFeedback]="instantFeedback"
        (feedback)="onFeedback($event)"
        (save)="onValueSaved($event)"
      >
        <ng-container
          *ngTemplateOutlet="finInputLabelTemplate"
          information-actions
        ></ng-container>
        <ng-container
          *ngTemplateOutlet="finFieldSuffixTemplate"
          field-actions
        ></ng-container>
      </ui-date-field>
    }
    @case ('DATE_RANGE') {
      <ui-date-range-field
        [class.has-error]="hasError"
        [isEditMode]="isEditUnlocked"
        [optionsTemplate]="menuOptions"
        [isFinStructureField]="true"
        [placeHolder]="placeHolder"
        [emitValueOnChange]="emitValueOnChange"
        [field]="field"
        [value]="value"
        [instantFeedback]="instantFeedback"
        (feedback)="onFeedback($event)"
        (save)="onValueSaved($event)"
      ></ui-date-range-field>
    }
    @case (fieldType.SHORT_TEXT) {
      <fin-input
        #field
        [size]="finSize.L"
        [formControl]="formControl"
        [readonly]="!isEditMode"
        (blurred)="onValueSaved($event)"
        [placeholder]="placeHolder"
        (keydown.enter)="field.blur($event)"
      >
        <ng-container
          finInputLabel
          *ngTemplateOutlet="finInputLabelTemplate"
        ></ng-container>
        <ng-container
          finInputSuffix
          *ngTemplateOutlet="finFieldSuffixTemplate"
        ></ng-container>
      </fin-input>
    }
    @case (fieldType.LONG_TEXT) {
      <app-refs-text-area
        [class.half-width]="!isAdditionalFieldAttached"
        [finFieldTemplate]="finFieldSuffixTemplate"
        [finInputLabelTemplate]="finInputLabelTemplate"
        [isEditMode]="isEditUnlocked"
        [emitValueOnChange]="emitValueOnChange"
        [isFinStructureField]="true"
        [field]="field"
        [label]="fieldLabel"
        [value]="value"
        [instantFeedback]="instantFeedback"
        (feedback)="onFeedback($event)"
        (save)="onValueSaved($event)"
        [hasError]="hasError"
        [errorText]="errorText"
      ></app-refs-text-area>
    }
    @case (fieldType.LOCATION) {
      <ui-location-field
        class="half-width"
        [class.has-error]="hasError"
        [isEditMode]="isEditUnlocked"
        [optionsTemplate]="menuOptions"
        [emitValueOnChange]="emitValueOnChange"
        [isFinStructureField]="true"
        [field]="fieldInfo"
        [information]="fieldInfo"
        [instantFeedback]="instantFeedback"
        [placeholder]="locationPlaceholder"
        (feedback)="onFeedback($event)"
        (save)="onValueSaved($event)"
      >
        <ng-container
          information-actions
          *ngTemplateOutlet="finInputLabelTemplate"
        >
          <app-refs-field-actions
            [field]="field"
            [isMirrored]="mirroredFieldKeys | includes: field?.key"
            [isCustomerLead]="isCustomerLead"
            [class.no-label-sync-icon]="
              hideFieldLabelSection &&
              (mirroredFieldKeys | includes: field?.key)
            "
          ></app-refs-field-actions>
        </ng-container>
        <ng-container
          *ngTemplateOutlet="finFieldSuffixTemplate"
          field-actions
        ></ng-container>
        <ng-container field-prefix>
          <fin-icon
            finFieldPrefix
            class="tw-cursor-pointer"
            name="location_on"
            finTooltip
            i18n-content="@@templateField.showOnMap"
            content="Auf der Karte anzeigen"
            (click)="onLocation(); $event.stopPropagation()"
          ></fin-icon>
        </ng-container>
      </ui-location-field>
    }
    @case (fieldType.SELECT) {
      <fin-dropdown
        [size]="finSize.L"
        [formControl]="formControl"
        [placeholder]="placeHolder"
        [options]="selectOptions | filterByTerm: 'value' : formControl.value"
        [autocomplete]="true"
        [readonly]="!isEditMode"
        (selectionChange)="onValueSaved($event)"
      >
        <ng-container
          finFieldLabel
          *ngTemplateOutlet="finInputLabelTemplate"
        ></ng-container>
        <ng-container
          finFieldSuffix
          *ngTemplateOutlet="finFieldSuffixTemplate"
        ></ng-container>
      </fin-dropdown>
    }
    @case (fieldType.MULTI_SELECT) {
      <app-refs-multiselect-field
        class="full-span"
        [class.has-error]="hasError"
        [isEditMode]="isEditUnlocked"
        [businessCase]="businessCase"
        [finStructureId]="finStructureId"
        [finInputLabelTemplate]="finInputLabelTemplate"
        [optionsTemplate]="menuOptions"
        [isFinStructureField]="true"
        [emitValueOnChange]="emitValueOnChange"
        [hasFreeTextEnabled]="isFreeTextSelect"
        [group]="group"
        [fieldGroupFieldsetName]="fieldGroupFieldsetName"
        [field]="field"
        [value]="value"
        [instantFeedback]="instantFeedback"
        (feedback)="onFeedback($event)"
        (valueChecked)="onValueChecked($event)"
        (save)="onValueSaved($event)"
        [mirroredFieldKeys]="mirroredFieldKeys"
        [isCustomerLead]="isCustomerLead"
        [customerType]="customerType"
      ></app-refs-multiselect-field>
    }
    @case (fieldType.TABLE) {
      <ui-data-grid-field
        [class.has-error]="hasError"
        [isEditMode]="isEditUnlocked"
        [optionsTemplate]="menuOptions"
        [isFinStructureField]="true"
        [field]="field"
        [label]="field.label"
        [information]="field"
        (feedback)="onFeedback($event)"
        [businessCaseId]="businessCase?.autoGeneratedBusinessCaseName"
        (save)="onTableSave($event)"
      >
        <ng-container
          *ngTemplateOutlet="finInputLabelTemplate"
          information-actions
        ></ng-container>
        <ng-container
          *ngTemplateOutlet="finFieldSuffixTemplate"
          field-actions
        ></ng-container
      ></ui-data-grid-field>
    }
    @case (fieldType.COMPOSITE) {
      <app-refs-composite-field
        [businessCase]="businessCase"
        [finStructureId]="finStructureId"
        [field]="field"
        [isEditMode]="isEditUnlocked"
        [parentGroup]="group"
        [mirroredFieldKeys]="mirroredFieldKeys"
        [isCustomerLead]="isCustomerLead"
        [customerType]="customerType"
      >
      </app-refs-composite-field>
    }
    @default {
      @if (this.field.value === 'Infinity' || this.field.value === 'NaN') {
        <ui-text-field
          [class.has-error]="true"
          [isEditMode]="false"
          [optionsTemplate]="menuOptions"
          [emitValueOnChange]="emitValueOnChange"
          [placeHolder]="placeHolder"
          [isFinStructureField]="true"
          [field]="field"
          [value]="'Kalkulationsfehler'"
          [instantFeedback]="instantFeedback"
          (feedback)="onFeedback($event)"
          (save)="onValueSaved($event)"
        ></ui-text-field>
      } @else {
        <fin-input
          #finNumberField
          [size]="finSize.L"
          [formControl]="formControl"
          [type]="
            (fieldInfo?.fieldType | find: numberInputTypes : 'key')?.value
          "
          [placeholder]="placeHolder"
          [readonly]="!isEditMode"
          (blurred)="onNumberValueChange(formControl.value)"
          (keydown.enter)="finNumberField.blur($event)"
        >
          <fin-field-messages>
            <ng-template
              finFieldMessage
              type="warning"
              errorKey="exceedTotalSum"
            >
              {{ warningText }}
            </ng-template>
          </fin-field-messages>

          <ng-container
            finInputLabel
            *ngTemplateOutlet="finInputLabelTemplate"
          ></ng-container>
          <ng-container
            finInputSuffix
            *ngTemplateOutlet="finFieldSuffixTemplate"
          ></ng-container>
        </fin-input>
      }
    }
  }
</ng-template>

<!-- MENU OPTIONS TEMPLATE -->
<ng-template #finFieldSuffixTemplate>
  @if (
    (kpiRelatedFieldsList | includes: field.key) &&
    (customerType === customerTypeEnum.REAL_ESTATE ||
      customerType === customerTypeEnum.BANK)
  ) {
    <fin-badge-status
      class="tw-break-keep"
      text="KPI"
      iconName="show_chart"
      [type]="finBadgeStatus.DRAFT"
      [size]="finSize.S"
    ></fin-badge-status>
  }
  @if (isEditUnlocked) {
    <button type="button" [finActionMenuTrigger]="finMenu.panel">
      <fin-icon class="menu-icon" name="more_vert"></fin-icon>
    </button>

    <fin-actions-menu #finMenu="finActionMenu">
      @for (option of options; track option.value) {
        <button
          fin-menu-item
          [size]="finSize.M"
          [iconName]="option.icon"
          (click)="onMenuItemClick(option.value)"
        >
          <ng-container finMenuItemTitle> {{ option.label }}</ng-container>
        </button>
      }
    </fin-actions-menu>
  }
</ng-template>

<ng-template #menuOptions>
  <button
    fin-menu-item
    [size]="finSize.M"
    iconName="article"
    (click)="onEditField()"
  >
    <ng-container finMenuItemTitle> {{ editFieldLabel }}</ng-container>
  </button>

  <button
    fin-menu-item
    [size]="finSize.M"
    iconName="replay"
    (click)="onOpenRevisions()"
  >
    <ng-container finMenuItemTitle> {{ openRevisionLabel }}</ng-container>
  </button>
</ng-template>

<ng-template #finInputLabelTemplate>
  @if (!hideFieldLabelSection) {
    <label [id]="highlightPrefix + field?.id" finTruncateText>
      {{ fieldLabel }}
    </label>

    <app-refs-field-actions
      [field]="field"
      [isMirrored]="mirroredFieldKeys | includes: field?.key"
      [isCustomerLead]="isCustomerLead"
      [class.no-label-sync-icon]="
        hideFieldLabelSection && (mirroredFieldKeys | includes: field?.key)
      "
    ></app-refs-field-actions>
  }
</ng-template>

<ng-template #finInputError>
  <fin-field-messages>
    <ng-template finFieldMessage type="error" errorKey="error">
      {{ errorText }}
    </ng-template>
  </fin-field-messages>
</ng-template>
