/* tslint:disable */
/* eslint-disable */

import {
  Component,
  EventEmitter,
  HostBinding,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
} from '@angular/core';

import { ColDef } from '@ag-grid-community/core';
import { AbstractControl, FormControl } from '@angular/forms';
import { BusinessCaseLocation, FieldTypeEnum } from '@fincloud/core/formly';
import { MathUtils } from '@fincloud/core/math';
import { CompositeFieldType } from '@fincloud/core/types';
import { KPI_RELATED_FIELDS } from '@fincloud/neoshare/business-case-fields';
import { LocationService } from '@fincloud/neoshare/services';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import {
  FinStructureField,
  FinStructureGroup,
} from '@fincloud/swagger-generator/financing-details';
import { CustomerType } from '@fincloud/types/enums';
import { HIGHLIGHT_PREFIX } from '@fincloud/utils';
import { FinBadgeStatus } from '@fincloud/ui/badges';
import { FinInputType } from '@fincloud/ui/input';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';
import { isEmpty, isEqual, isNil } from 'lodash-es';
import { RefsPageActions } from '../../+state/actions';
import { ActionMenu } from '../../enums/action-menu';
import { FinStructureFieldInfo } from '../../models/fin-structure-field-info';
import { RefsFinancingStructureModalService } from '../../services/refs-modal.service';

@Component({
  selector: 'ui-refs-field',
  templateUrl: './refs-field.component.html',
  styleUrls: ['./refs-field.component.scss'],
})
export class RefsFieldComponent implements OnChanges {
  @Input() group: FinStructureGroup;
  @Input() fieldGroupFieldsetName: string;
  @Input() valueHandling: 'emit-event' | 'persist' = 'persist';
  @Input() businessCase: ExchangeBusinessCase;
  @Input() label: string;
  @Input() emitValueOnChange = false;
  @Input() isDynamic = false;
  @Input() labelTooltip: string;
  @Input() hideVisibilityIcon = false;
  @Input() hideFieldLabelSection = false;
  @Input() instantFeedback = false;
  @Input() hasWarning = true;
  @Input() errorText: string;
  @Input() placeHolder = '';
  @Input() finStructureId: string;
  @Input() mirroredFieldKeys: string[] = [];
  @Input() isCustomerLead: boolean;
  @Input() customerType: CustomerType;
  @Input() isAdditionalFieldAttached = false;
  @Input() set hasError(value: boolean) {
    this._hasError = value;
    this.setErrorValidator();
  }
  get hasError() {
    return this._hasError;
  }

  @Input() set warningText(value: string) {
    if (value) {
      this.setWarningValidator(value, this.isEditMode);
    } else {
      this.clearValidations();
    }

    this._warningText = value;
  }

  get warningText() {
    return this._warningText;
  }

  @Input() set field(field: FinStructureField) {
    this.formControl.setValue(field?.value);
    this._field = field;
    this.selectOptions = this.getOptions(this.field.fieldMetaData as []);
  }
  get field(): FinStructureField {
    return this._field;
  }

  @Input() set isEditMode(isEdit: boolean) {
    if (isEdit && !this.field?.disabled) {
      this.formControl.enable();
      this.setWarningValidator(this.warningText, isEdit);
    }

    if (!isEdit && this.field?.disabled) {
      this.formControl.disable();
    }
    if (!isEdit) {
      this.clearValidations();
    }

    this._isEditMode = isEdit;
  }

  get isEditMode() {
    return this._isEditMode;
  }

  @Output() valueChange = new EventEmitter<string>();
  @Output() feedback = new EventEmitter<string>();
  @HostBinding('class.full-span') isFullWidth = false;

  readonly fieldType = FieldTypeEnum;
  readonly finSize = FinSize;
  readonly finBadgeStatus = FinBadgeStatus;
  readonly SELECT_FIELD_PLACEHOLDER = '';

  private _warningText = '';
  private _isEditMode = true;
  private _field: FinStructureField;
  private _hasError = false;
  highlightPrefix = HIGHLIGHT_PREFIX;
  fieldInfo: FinStructureFieldInfo;
  compositeFieldType: CompositeFieldType | null;
  isFreeTextSelect = false;
  formControl: FormControl = new FormControl('', []);
  customerTypeEnum = CustomerType;
  kpiRelatedFieldsList = KPI_RELATED_FIELDS;
  editFieldLabel = $localize`:@@facilityField.actionsMenu.item.editField:Feld bearbeiten`;
  openRevisionLabel = $localize`:@@facilityField.actionsMenu.item.revisions:Revisionen`;
  locationPlaceholder = $localize`:@@locationField.placeholder:Adresse eingeben`;

  get value() {
    if (this.field?.fieldType === FieldTypeEnum.PERCENT) {
      return MathUtils.getSafeValue(
        parseFloat(this.field?.value?.toString() || '0'),
        3,
      ).toString();
    }

    return this.field?.value as string;
  }

  get fieldLabel() {
    return this.label ?? this.field?.label;
  }

  get isEditUnlocked() {
    return this.isEditMode;
  }
  private isInitiallySynced: boolean;

  options = [
    {
      icon: 'article',
      label: this.editFieldLabel,
      value: ActionMenu.EDIT_FIELD,
    },

    {
      icon: 'replay',
      label: this.openRevisionLabel,
      value: ActionMenu.OPEN_REVISIONS,
    },
  ];

  numberInputTypes = [
    { key: 'MONETARY', value: FinInputType.CURRENCY },
    { key: 'DECIMAL', value: FinInputType.DECIMAL },
    { key: 'INTEGER', value: FinInputType.INTEGER },
    { key: 'PERCENT', value: FinInputType.PERCENTAGE },
  ];

  selectOptions: Record<string, string>[] = [];

  constructor(
    private financingStructureModalService: RefsFinancingStructureModalService,
    private store: Store,
    private locationService: LocationService,
  ) {}

  private getOptions(metaData: []) {
    return (
      metaData?.map((data) => {
        return {
          label: data,
          value: data,
        };
      }) ?? []
    );
  }

  private setErrorValidator() {
    this.formControl.setValidators([
      this.generateErrorValidator(this.errorText),
    ]);
    this.formControl.updateValueAndValidity();
  }

  private generateErrorValidator(errorMessage: string) {
    return (_control: AbstractControl) => {
      if (this.hasError) {
        return { error: errorMessage };
      } else {
        return null;
      }
    };
  }

  private setWarningValidator(warningMessage: string, isEditMode: boolean) {
    if (warningMessage && isEditMode) {
      this.formControl.setValidators([
        this.generateWarningValidator(warningMessage),
      ]);
      this.formControl.markAllAsTouched();
      this.formControl.updateValueAndValidity();
    }
  }

  private generateWarningValidator(warningMessage: string) {
    return (_control: AbstractControl) => {
      return { exceedTotalSum: warningMessage };
    };
  }

  private clearValidations() {
    this.formControl.setErrors(null);
    this.formControl.markAsPristine();
    this.formControl.markAsUntouched();
    this.formControl.updateValueAndValidity();
  }

  ngOnChanges(data: SimpleChanges): void {
    if (
      data.field?.currentValue !== data.field?.previousValue &&
      !!data.field?.currentValue
    ) {
      let fieldMetadata: string[];

      if ((this.field?.fieldMetaData as string[])?.length) {
        fieldMetadata = (this.field?.fieldMetaData as string[]) || [];
        this.isFreeTextSelect = fieldMetadata.indexOf('FREE_TEXT') >= 0;
      }

      if (this.isFreeTextSelect) {
        fieldMetadata = [...fieldMetadata.filter((i) => i !== 'FREE_TEXT')];

        const fieldValue = this.field?.value as string;

        if (
          fieldValue &&
          fieldValue !== 'null' &&
          fieldMetadata.indexOf(fieldValue) < 0
        ) {
          fieldMetadata.push(fieldValue);
        }
      }

      this.fieldInfo = {
        id: this.field.id,
        description: this.field?.description,
        expression: this.field?.expression,
        fieldMetaData: fieldMetadata,
        fieldType: this.field?.fieldType,
        isHidden: this.field?.isHidden,
        isPromoted: this.field?.isPromoted,
        isRequired: this.field?.isRequired,
        key: this.field?.key,
        label: this.field?.label || this.label,
        portalVisibility: this.field?.portalVisibility,
        priority: this.field?.priority,
        value: this.field?.value || this.value,
        visibilityExpression: this.field?.visibilityExpression,
        isRestoredFromRevision: this.field.restoredFromRevision,
        appliedRevisionId: this.field.restoredFromRevisionId,
        isPublic: this.field?.isPublic,
        group: this.group.group,
        groupType: this.group.type,
        fieldsetName: this.fieldGroupFieldsetName,
        defaultInformation: this.field.defaultInformation,
      };

      this.isFullWidth = [
        'LONG_TEXT',
        'TABLE',
        'DATE_RANGE',
        'COMPOSITE',
        'LOCATION',
        'MULTI_SELECT',
      ].includes(this.field.fieldType);
    }
  }

  onEditField() {
    this.openModal('edit');
  }

  onOpenRevisions() {
    this.openModal('revisions');
  }

  onFeedback(text: string) {
    this.feedback.emit(text);
  }

  onTableSave(value: {
    rowData: Record<string, unknown>[];
    columnDefs: ColDef[];
    version: string;
    filteredData: Record<string, unknown>;
  }) {
    this.store.dispatch(
      RefsPageActions.addValueToFinStructureField({
        payload: {
          businessCaseId: this.businessCase.id,
          body: {
            fieldId: this.field.id,
            dynamic: this.isDynamic,
            group: this.group.group,
            value,
            fieldsetName: this.fieldInfo.fieldsetName,
          },
          isSynced: this.isInitiallySynced,
          fieldKey: this.field.key,
        },
      }),
    );
  }

  onValueChecked(event: { isChecked: boolean; value: string }): void {
    const existingOptions = this.field.value
      ? (this.field.value as string).split(',')
      : [];
    const updatedOptions = event.isChecked
      ? [...existingOptions, event.value]
      : existingOptions.filter((item) => item !== event.value);

    this.store.dispatch(
      RefsPageActions.addValueToFinStructureField({
        payload: {
          businessCaseId: this.businessCase.id,
          body: {
            fieldId: this.field.id,
            dynamic: this.isDynamic,
            group: this.group.group,
            value: updatedOptions.join(','),
            fieldsetName: this.fieldInfo.fieldsetName,
          },
          isSynced: this.isInitiallySynced,
          fieldKey: this.field.key,
        },
      }),
    );
  }

  onNumberValueChange(value: number | string) {
    if (this.valueHandling === 'emit-event') {
      this.valueChange.emit(value.toString());
      return;
    }

    if (
      isEqual(value?.toString(), this.field?.value?.toString()) ||
      (value === null && this.field?.value === undefined)
    ) {
      return;
    }

    let newValue = value;

    if (this.field?.fieldType === 'PERCENT') {
      if (
        parseFloat(this.field?.value?.toString() || '0')?.toString() ===
        value?.toString()
      ) {
        return;
      }

      newValue = MathUtils.getSafeValue(
        parseFloat(value?.toString() || '0'),
        4,
      );
    }

    this.store.dispatch(
      RefsPageActions.addValueToFinStructureField({
        payload: {
          businessCaseId: this.businessCase.id,
          body: {
            fieldId: this.field.id,
            dynamic: this.isDynamic,
            group: this.group.group,
            value: newValue,
            fieldsetName: this.fieldInfo.fieldsetName,
          },
          isSynced: this.isInitiallySynced,
          fieldKey: this.field.key,
        },
      }),
    );
  }
  onValueSaved(value: string) {
    if (this.valueHandling === 'emit-event') {
      this.valueChange.emit(value);
    } else {
      if (
        isEqual(value, this.field?.value) ||
        (isNil(value) && isNil(this.field?.value)) ||
        (isEmpty(value) && isNil(this.field?.value))
      ) {
        return;
      }
      this.store.dispatch(
        RefsPageActions.addValueToFinStructureField({
          payload: {
            businessCaseId: this.businessCase.id,
            body: {
              fieldId: this.field.id,
              dynamic: this.isDynamic,
              group: this.group.group,
              value,
              fieldsetName: this.fieldInfo.fieldsetName,
            },
            isSynced: this.isInitiallySynced,
            fieldKey: this.field.key,
          },
        }),
      );
    }
  }

  openModal(tab: 'edit' | 'revisions') {
    this.financingStructureModalService.openManageFinancingStructureFieldModal(
      tab,
      {
        isEdit: true,
        field: this.fieldInfo,
      },
      this.businessCase,
      this.isDynamic,
      this.finStructureId,
      this.group,
    );
  }

  onMenuItemClick(value: string): void {
    switch (value) {
      case ActionMenu.EDIT_FIELD:
        this.onEditField();
        break;
      case ActionMenu.OPEN_REVISIONS:
        this.onOpenRevisions();
        break;
      default:
        break;
    }
  }

  onLocation() {
    this.locationService.showOnMap(this.field.value as BusinessCaseLocation);
  }
}
