import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AvatarComponent } from '@fincloud/components/avatar';
import { NsUiButtonsModule } from '@fincloud/components/buttons';
import { NsUiDataGridModule } from '@fincloud/components/data-grid';
import { NsUiFormlyModule } from '@fincloud/components/formly';
import { NsUiFormsModule } from '@fincloud/components/forms';
import { NsUiIconsModule } from '@fincloud/components/icons';
import { NsUiLayoutModule } from '@fincloud/components/layout';
import { NsUiListsModule } from '@fincloud/components/lists';
import { NsUiNumberModule } from '@fincloud/components/number';
import { NsUiSelectGroupTreeModule } from '@fincloud/components/select-group-tree';
import { NsUiSelectsModule } from '@fincloud/components/selects';
import { NsUiTextModule } from '@fincloud/components/text';
import { NsUiNgBootstrapModule } from '@fincloud/components/third-party-modules';
import { NsUiTooltipModule } from '@fincloud/components/tooltip';
import { NsCoreDateModule } from '@fincloud/core/date';
import { NsCoreDirectivesModule } from '@fincloud/core/directives';
import {
  NsCorePipesModule,
  RemoveTrailingZerosPipe,
} from '@fincloud/core/pipes';
import { NsBusinessCaseRefactoringModule } from '@fincloud/neoshare/business-case';
import { NsBusinessCaseFieldsModule } from '@fincloud/neoshare/business-case-fields';
import { NsBusinessCaseModalBaseModule } from '@fincloud/neoshare/business-case-modal-base';
import { NsNeogptChatModule } from '@fincloud/neoshare/neogpt-chat';
import { FINANCING_DETAILS_FEATURE_KEY } from '@fincloud/state/utils';
import { FinActionsMenuModule } from '@fincloud/ui/actions-menu';
import { FinBadgesModule } from '@fincloud/ui/badges';
import { FinButtonComponent, FinButtonModule } from '@fincloud/ui/button';
import { FinCardLabelModule } from '@fincloud/ui/card-label';
import { FinChartsModule } from '@fincloud/ui/charts';
import { FinCheckboxComponent, FinCheckboxModule } from '@fincloud/ui/checkbox';
import { FinDropdownModule } from '@fincloud/ui/dropdown';
import {
  FinAccordionComponent,
  FinExpansionPanelComponent,
  FinExpansionPanelModule,
} from '@fincloud/ui/expansion-panel';
import { FinFieldMessageModule } from '@fincloud/ui/field-message';
import { FinIconComponent, FinIconModule } from '@fincloud/ui/icon';
import { FinInputModule } from '@fincloud/ui/input';
import { FinMenuItemModule } from '@fincloud/ui/menu-item';
import { FinModalModule } from '@fincloud/ui/modal';
import { FinProgressBarModule } from '@fincloud/ui/progress-bar';
import { FinScrollbarModule } from '@fincloud/ui/scrollbar';
import { FinSearchModule } from '@fincloud/ui/search';
import {
  FinHorizontalSeparatorDirective,
  FinSeparatorsModule,
  FinVerticalSeparatorDirective,
} from '@fincloud/ui/separators';
import { FinSlideToggleModule } from '@fincloud/ui/slide-toggle';
import {
  FinSwitchToggleComponent,
  FinSwitchToggleModule,
} from '@fincloud/ui/switch-toggle';
import { FinTabLabelDirective, FinTabsModule } from '@fincloud/ui/tabs';
import { FinTextAreaModule } from '@fincloud/ui/text-area';
import { FinToolbarModule } from '@fincloud/ui/toolbar';
import { FinTooltipModule } from '@fincloud/ui/tooltip';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinWarningMessageModule } from '@fincloud/ui/warning-message';
import { NgbActiveModal, NgbHighlight } from '@ng-bootstrap/ng-bootstrap';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { TraceModule } from '@sentry/angular';
import { NgxPermissionsModule } from 'ngx-permissions';
import { NgScrollbarModule } from 'ngx-scrollbar';
import {
  FinancingDetailsEffects,
  SearchFinStructureEffects,
} from './+state/effects';
import { TeaserExportEffects } from './+state/effects/teaser-export.effects';
import { reducers } from './+state/reducers';
import { teaserExportFeature } from './+state/reducers/teaser-export.reducer';
import { BusinessCaseRealEstateFinancingStructureRoutingModule } from './business-case-real-estate-financing-structure-routing.module';
import { RefsAccordionComponent } from './components/refs-accordion/refs-accordion.component';
import { RefsAddFinancingBlockModalComponent } from './components/refs-add-financing-block-modal/refs-add-financing-block-modal.component';
import { RefsBuildingBlocksComponent } from './components/refs-building-block/refs-building-blocks.component';
import { RefsBuildingBlocksAggregatedInfoComponent } from './components/refs-building-blocks-aggregated-info/refs-building-blocks-aggregated-info.component';
import { RefsCompositeFieldComponent } from './components/refs-composite-field/refs-composite-field.component';
import { RefsContentComponent } from './components/refs-content/refs-content.component';
import { RefsFieldActionsComponent } from './components/refs-field-actions/refs-field-actions.component';
import { RefsFieldEditComponent } from './components/refs-field-edit/refs-field-edit.component';
import { RefsFieldModalComponent } from './components/refs-field-modal/refs-field-modal.component';
import { RefsFieldRevisionsComponent } from './components/refs-field-revisions/refs-field-revisions.component';
import { RefsFieldComponent } from './components/refs-field/refs-field.component';
import { RefsFinancingBlockComponent } from './components/refs-financing-block/refs-financing-block.component';
import { RefsFinancingDetailsComponent } from './components/refs-financing-details/refs-financing-details.component';
import { RefsFinancingTabsComponent } from './components/refs-financing-tabs/refs-financing-tabs.component';
import { RefsGroupsSharingModalComponent } from './components/refs-groups-sharing-modal/refs-groups-sharing-modal.component';
import { RefsMultiselectFieldComponent } from './components/refs-multiselect-field/refs-multiselect-field.component';
import { RefsNoResultsComponent } from './components/refs-no-results/refs-no-results.component';
import { RefsParticipationComponent } from './components/refs-participation/refs-participation.component';
import { RefsRemoveFinancingBlockModalComponent } from './components/refs-remove-financing-block-modal/refs-remove-financing-block-modal.component';
import { RefsSharedFinancingStructureComponent } from './components/refs-shared-financing-structure/refs-shared-financing-structure.component';
import { RefsSideNavListComponent } from './components/refs-side-nav-list/refs-side-nav-list.component';
import { RefsStatsComponent } from './components/refs-stats/refs-stats.component';
import { RefsTeaserModalComponent } from './components/refs-teaser-modal/refs-teaser-modal.component';
import { RefsTextAreaComponent } from './components/refs-text-area/refs-text-area.component';
import { RefsContentService } from './services/refs-content.service';
import { SearchFinancingService } from './services/search-financing.service';

@NgModule({
  declarations: [
    RefsFieldActionsComponent,
    RefsFieldComponent,
    RefsFieldModalComponent,
    RefsFieldRevisionsComponent,
    RefsFieldEditComponent,
    RefsRemoveFinancingBlockModalComponent,
    RefsStatsComponent,
    RefsTextAreaComponent,
    RefsCompositeFieldComponent,
    RefsBuildingBlocksAggregatedInfoComponent,
    RefsFinancingTabsComponent,
    RefsFinancingBlockComponent,
    RefsContentComponent,
    RefsParticipationComponent,
    RefsMultiselectFieldComponent,
    RefsSharedFinancingStructureComponent,
    RefsAccordionComponent,
    RefsBuildingBlocksComponent,
    RefsNoResultsComponent,
    RefsFinancingDetailsComponent,
    RefsTeaserModalComponent,
    RefsGroupsSharingModalComponent,
    RefsAddFinancingBlockModalComponent,
  ],
  imports: [
    BusinessCaseRealEstateFinancingStructureRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    FinScrollbarModule,
    NsUiFormlyModule,
    NgScrollbarModule,
    NgxPermissionsModule.forChild(),
    AvatarComponent,
    NsCorePipesModule,
    NsBusinessCaseFieldsModule,
    TraceModule,
    NgbHighlight,
    NsBusinessCaseModalBaseModule,
    FinTabsModule,
    FinSlideToggleModule,
    FinSeparatorsModule,
    FinExpansionPanelModule,
    FinProgressBarModule,
    FinIconModule,
    FinActionsMenuModule,
    NsNeogptChatModule,
    FinCheckboxComponent,
    FinIconComponent,
    FinButtonComponent,
    FinExpansionPanelComponent,
    FinAccordionComponent,
    RefsSideNavListComponent,
    FinTabsModule,
    FinTabLabelDirective,
    FinTruncateTextModule,
    FinButtonModule,
    FinChartsModule,
    FinSwitchToggleModule,
    FinWarningMessageModule,
    FinTextAreaModule,
    FinInputModule,
    FinFieldMessageModule,
    FinDropdownModule,
    FinCheckboxModule,
    FinHorizontalSeparatorDirective,
    FinVerticalSeparatorDirective,
    FinCardLabelModule,
    FinTooltipModule,
    FinSwitchToggleComponent,
    NsUiLayoutModule,
    NsCoreDirectivesModule,
    FinToolbarModule,
    FinSearchModule,
    FinBadgesModule,
    FinModalModule,
    NsCoreDateModule,
    NsUiTooltipModule,
    NsUiIconsModule,
    NsUiNgBootstrapModule,
    NsUiButtonsModule,
    NsUiDataGridModule,
    NsUiListsModule,
    NsUiTextModule,
    NsUiNumberModule,
    FinMenuItemModule,
    NsBusinessCaseRefactoringModule,
    NsUiSelectsModule,
    NsUiSelectGroupTreeModule,
    NsUiFormsModule,
  ],

  providers: [
    NgbActiveModal,
    provideState(FINANCING_DETAILS_FEATURE_KEY, reducers),
    provideState(teaserExportFeature),
    provideEffects([
      FinancingDetailsEffects,
      SearchFinStructureEffects,
      TeaserExportEffects,
    ]),
    RefsContentService,
    SearchFinancingService,
    RemoveTrailingZerosPipe,
  ],
})
export class BusinessCaseRealEstateFinancingStructureModule {}
