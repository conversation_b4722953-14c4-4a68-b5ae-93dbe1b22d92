import { Injectable, inject } from '@angular/core';
import { FieldTypeEnum } from '@fincloud/core/formly';
import {
  REGIONAL_SETTINGS_MAP,
  RegionalSettingsService,
} from '@fincloud/core/regional-settings';
import { FieldSearchCriteria } from '@fincloud/swagger-generator/financing-details';
import { Locale } from '@fincloud/types/enums';
import {
  SEARCH_FINANCING_STRUCTURE_TYPEAHEAD_HINT,
  SEARCH_FINANCING_STRUCTURE_TYPEAHEAD_NO_RESULTS,
  SearchFinStructureHandlerResponse,
  normalizeBrackets,
  normalizeQuotes,
  normalizeUmlauts,
} from '@fincloud/utils';
import { isFinite, isNil, isNumber } from 'lodash-es';

@Injectable()
export class SearchFinancingService {
  regionalSettingsService = inject(RegionalSettingsService);

  thousandsSeparatorReplaceRegex = new RegExp(
    `\\${REGIONAL_SETTINGS_MAP[this.regionalSettingsService.region].thousandSeparator}`,
    'g',
  );

  thousandsSeparatorMatch: Record<Locale, RegExp> = {
    de: new RegExp(/^\d+((\.\d+)*)$/),
    en: new RegExp(/^\d+((,\d+)*)$/),
  };

  searchCriteria(searchTerm: string): FieldSearchCriteria[] {
    const searchCriteria: FieldSearchCriteria[] = [
      this.decimalHandler(searchTerm),
      this.dateHandler(searchTerm),
      this.percentHandler(searchTerm),
      this.monetaryHandler(searchTerm),
      this.integerHandler(searchTerm),
      this.textHandler(searchTerm),
    ]
      .filter(({ canHandle }) => !!canHandle)
      .map(({ searchValues, fieldTypes }) => ({ searchValues, fieldTypes }));

    return searchCriteria;
  }

  dispatchAutocomplete(searchTerm: string): {
    canDispatch: boolean;
    searchTerm: string;
  } {
    if (
      isNil(searchTerm) ||
      searchTerm.length === 0 ||
      [
        SEARCH_FINANCING_STRUCTURE_TYPEAHEAD_HINT,
        SEARCH_FINANCING_STRUCTURE_TYPEAHEAD_NO_RESULTS,
      ].includes(searchTerm)
    ) {
      return { canDispatch: false, searchTerm: '' };
    }

    return { canDispatch: true, searchTerm };
  }

  labelSearchCriteria(searchTerm: string): {
    canHandle: boolean;
    searchLabels: string[];
  } {
    if (
      !this.isBasicSearchCriteriaMet(searchTerm) ||
      !this.dispatchAutocomplete(searchTerm).canDispatch
    ) {
      return { canHandle: false, searchLabels: [] };
    }

    return { canHandle: true, searchLabels: this.normalizeText(searchTerm) };
  }

  private textHandler(searchTerm: string): SearchFinStructureHandlerResponse {
    return {
      canHandle: this.isBasicSearchCriteriaMet(searchTerm),
      searchValues: this.normalizeText(searchTerm.toLocaleLowerCase()),
      fieldTypes: [
        FieldTypeEnum.SHORT_TEXT,
        FieldTypeEnum.LONG_TEXT,
        FieldTypeEnum.SELECT,
        FieldTypeEnum.TABLE,
      ],
    };
  }

  private monetaryHandler(
    searchTerm: string,
  ): SearchFinStructureHandlerResponse {
    return this.specialCharWithNumberHandler(
      searchTerm,
      '€',
      (values: string[]) => ({
        canHandle: true,
        searchValues: values.length ? values : [],
        fieldTypes: [FieldTypeEnum.MONETARY, FieldTypeEnum.TABLE],
      }),
    );
  }

  private dateHandler(searchTerm: string): SearchFinStructureHandlerResponse {
    // matching dates like (1.12, 01.12) or (1/12,  01/12) (first december), also (1.12.1985 and 1/12/1985) but not (1.12.85 or 1/12/85)
    // possible years 1900  till 2099. When we reach 2099 year someone have to update it
    const values = searchTerm.match(
      /^(0[1-9]|[12][0-9]|3[01]|[1-9])[./](0[1-9]|1[0-2]|[1-9])([./]((19|20)\d{2}))?$/, //
    );
    if (isNil(values)) {
      return { canHandle: false, searchValues: [], fieldTypes: [] };
    }
    const [, day, month, year] = values;
    const dateRepresentation =
      month.padStart(2, '0') + '-' + day.padStart(2, '0');

    return {
      canHandle: true,
      searchValues: [
        year
          ? year.replace(/[./]/, '') + '-' + dateRepresentation
          : dateRepresentation,
      ],
      fieldTypes: [FieldTypeEnum.DATE, FieldTypeEnum.TABLE],
    };
  }

  private integerHandler(
    searchTerm: string,
  ): SearchFinStructureHandlerResponse {
    const { thousandSeparator } =
      REGIONAL_SETTINGS_MAP[this.regionalSettingsService.region];
    const regex =
      this.thousandsSeparatorMatch[this.regionalSettingsService.locale];
    // handle values e.g. 2190 or 2.190 or 111.222 but not 2.190,3
    // on EN  calues e.g. 2190 or 2,190 or 111,222 but not 2,190.3
    if (!regex.test(searchTerm)) {
      return { canHandle: false, searchValues: [], fieldTypes: [] };
    }
    return {
      canHandle: true,
      searchValues: [searchTerm.split(thousandSeparator).join('')],
      fieldTypes: Object.values(FieldTypeEnum),
    };
  }

  private percentHandler(
    searchTerm: string,
  ): SearchFinStructureHandlerResponse {
    return this.specialCharWithNumberHandler(
      searchTerm,
      '%',
      (values: string[]) => {
        const possibleNumber = Number(values[0]);

        return {
          canHandle: true,
          searchValues:
            isNumber(possibleNumber) && isFinite(possibleNumber)
              ? [possibleNumber.toString()]
              : [],
          fieldTypes: [FieldTypeEnum.PERCENT, FieldTypeEnum.TABLE],
        };
      },
    );
  }

  private decimalHandler(searchTerm: string) {
    const { decimalSeparator } =
      REGIONAL_SETTINGS_MAP[this.regionalSettingsService.region];
    return this.specialCharWithNumberHandler(
      searchTerm,
      decimalSeparator || ',',
      (values: string[]) => {
        if (values.length > 2) {
          return { canHandle: false, searchValues: [], fieldTypes: [] };
        }
        const wholeNumber = values[0].replace(
          this.thousandsSeparatorReplaceRegex,
          '',
        );
        const decimalPart = values[1];
        const decimalRepresentation = wholeNumber + '.' + (decimalPart ?? 0);
        return {
          canHandle: true,
          searchValues: [parseFloat(decimalRepresentation).toString()],
          fieldTypes: [
            FieldTypeEnum.DECIMAL,
            FieldTypeEnum.MONETARY,
            FieldTypeEnum.TABLE,
          ],
        };
      },
    );
  }

  private specialCharWithNumberHandler(
    text: string,
    specialChar: string,
    mapToResult: (values: string[]) => SearchFinStructureHandlerResponse,
  ): SearchFinStructureHandlerResponse {
    if (!text.includes(specialChar)) {
      return { canHandle: false, searchValues: [], fieldTypes: [] };
    }
    const nonSpecialCharValues = [];
    if (text.length > 1) {
      nonSpecialCharValues.push(
        ...text
          .split(specialChar)
          .filter(Boolean)
          .map((value) => value.trim()),
      );
    }

    const isAnyNaN = nonSpecialCharValues.find((value) => {
      const formatedValue = this.thousandsSeparatorReplaceRegex.test(value)
        ? value.replace(
            this.thousandsSeparatorReplaceRegex,
            REGIONAL_SETTINGS_MAP.GERMANY.thousandSeparator,
          )
        : value;
      return isNaN(Number(formatedValue));
    });

    if (isAnyNaN) {
      return { canHandle: false, searchValues: [], fieldTypes: [] };
    }

    return mapToResult(nonSpecialCharValues);
  }

  private normalizeText(searchTerm: string): string[] {
    return [
      ...normalizeUmlauts(searchTerm),
      ...normalizeQuotes(searchTerm),
      ...normalizeBrackets(searchTerm),
    ];
  }

  isBasicSearchCriteriaMet(searchTerm: string): boolean {
    return /[0-9@#$%&€*]/.test(searchTerm) || searchTerm.length > 2;
  }
}
