import {
  BusinessCaseRealEstateTeaserExportState,
  CustomFinStructureGroup,
} from '@fincloud/types/models';
import { REFS_BLOCKS_GROUP_KEY } from '@fincloud/utils';
import { createSelector } from '@ngrx/store';
import { BaseSelectors } from '@ngrx/store/src/feature_creator';
import { sortBy } from 'lodash-es';

// eslint-disable-next-line @ngrx/prefix-selectors-with-select
export const teaserExportSelectors = (
  teaserExport: BaseSelectors<
    'teaserExport',
    BusinessCaseRealEstateTeaserExportState
  >,
) => {
  const selectFinStructureTeaserExport = createSelector(
    teaserExport.selectFinancingStructure,
    teaserExport.selectDisabledGroups,
    (financingStructure, disabledGroups) => {
      const staticGroups = financingStructure.staticGroups;
      const dynamicGroups = financingStructure.dynamicFieldsets;

      const staticAndDynamicGroups = staticGroups.map((sg) =>
        sg.key === REFS_BLOCKS_GROUP_KEY
          ? {
              ...sg,
              subGroups: sortBy(dynamicGroups, ['group']),
            }
          : sg,
      );

      const idsForExport = staticAndDynamicGroups
        .map((group: CustomFinStructureGroup) => {
          if (disabledGroups.includes(group.id)) {
            return [];
          }

          const subGroupIds = group.subGroups
            .map((subGroup) =>
              disabledGroups.includes(subGroup.id) ? null : subGroup.id,
            )
            .filter(Boolean);
          return [group.id, ...subGroupIds];
        })
        .flat();

      return {
        staticGroups: staticAndDynamicGroups,
        disabledGroups,
        idsForExport,
      };
    },
  );

  return {
    selectFinStructureTeaserExport,
  };
};
