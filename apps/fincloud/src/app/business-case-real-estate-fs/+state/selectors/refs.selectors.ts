import { SpecialFieldLabelOrKeyEnum } from '@fincloud/components/refs';
import { FieldTypeEnum } from '@fincloud/core/formly';
import { TEMPLATE_FIELD_KEYS } from '@fincloud/core/types';
import { neitherNullNorUndefined } from '@fincloud/core/utils';
import {
  selectActiveIndexFinanceRouterPath,
  selectBusinessCase,
  selectBusinessCaseCompactHeaderHeightDelta,
  selectBusinessCaseId,
  selectBusinessCaseParticipationType,
  selectCurrentUserPartner,
  selectCustomerNamesByKey,
  selectEditTemplateMode,
  selectFacilities,
  selectHasAnyBusinessCasePermission,
  selectInvitationTabVisibility,
  selectIsBusinessCaseCorporate,
  selectIsBusinessCaseMiscellaneous,
  selectIsBusinessCaseRealEstate,
  selectParticipationMaxAmount,
  selectParticipationMinAmount,
  selectShowApplicationView,
  selectStatisticsCorporateRealEstateVisibility,
  selectTotalParticipationAmount,
} from '@fincloud/state/business-case';
import {
  selectRawDynamicFieldsets,
  selectRawStaticGroups,
  selectSearchFieldIds,
  selectSearchGroupIds,
} from '@fincloud/state/business-case-real-estate';
import { selectCustomerType } from '@fincloud/state/customer';
import { selectHighlightedId } from '@fincloud/state/neogpt-chat';
import { selectRouteDataParam } from '@fincloud/state/router';
import { sideNavigationsFeature } from '@fincloud/state/side-navigations';
import { selectUserCustomerKey } from '@fincloud/state/user';
import { FINANCING_DETAILS_FEATURE_KEY } from '@fincloud/state/utils';
import { FinStructureField } from '@fincloud/swagger-generator/financing-details';
import {
  BusinessCasePermission,
  CustomerType,
  FinancingDetailsSubPage,
  GroupKey,
  ParticipationType,
} from '@fincloud/types/enums';
import {
  CustomFinStructureGroup,
  FinancingDetailsState,
} from '@fincloud/types/models';
import { REFS_BLOCKS_GROUP_KEY } from '@fincloud/utils';
import { createFeatureSelector, createSelector } from '@ngrx/store';
import { isNil, sortBy } from 'lodash-es';
import { CustomGroupMetadata } from '../../models/custom-group-metadata';
import { buildMultiSelectFields } from '../../utils/build-multiselect-field';
import { collectRealEstateChartData } from '../../utils/collect-refs-chart-data';
import { dynamicBuildingBlockAmount } from '../../utils/dynamic-building-block-amount';
import { FIELD_LABEL_SUFFIX } from '../../utils/field-label-suffix';
import { filterOutCompositeDependentFields } from '../../utils/filter-composite-dependent-fields';
import { KEY_SUFFIX } from '../../utils/key-suffix';
import { mapGroupToSideNavigation } from '../../utils/map-group-to-side-navigation';
import { setVisibleFields } from '../../utils/set-visible-fields';
import { setVisibleGroups } from '../../utils/set-visible-groups';
import { setWarningOnSpecialFields } from '../../utils/set-warning-on-special-fields';
import { transformRealEstateChartData } from '../../utils/transform-refs-chart-data';

const selectFinancingDetailsState = createFeatureSelector<{
  finStructure: FinancingDetailsState;
}>(FINANCING_DETAILS_FEATURE_KEY);

const selectFinStructureState = createSelector(
  selectFinancingDetailsState,
  (state) => state.finStructure,
);

const selectActiveBuildingBlockId = createSelector(
  selectFinStructureState,
  (state) => state.activeBuildingBlockId,
);

export const selectOverviewActiveTab = createSelector(
  selectFinStructureState,
  (state: FinancingDetailsState) => {
    return state.overviewActiveTab;
  },
);

export const selectSharedWithMe = createSelector(
  selectFinStructureState,
  (state: FinancingDetailsState) => {
    return state.sharedWithMeEntities;
  },
);

export const selectSharedWithMeByCustomer = createSelector(
  selectSharedWithMe,
  selectCustomerNamesByKey,
  (sharedWithMe, customerNamesByKey) => {
    return sharedWithMe
      .map((entity) => {
        const currentCustomer = customerNamesByKey[entity.fromCustomerKey];
        if (!isNil(currentCustomer) && entity.sharedGroups?.length) {
          return { ...entity, name: currentCustomer.name };
        }
        return null;
      })
      .filter(Boolean);
  },
);

const selectFinancingVolumeMiscellaneous = createSelector(
  selectIsBusinessCaseMiscellaneous,
  selectBusinessCase,
  (isMisc, businessCase) => {
    if (!isMisc || isNil(businessCase)) {
      return 0;
    }

    const value = Number(businessCase.information.financingVolume.value);
    return isNaN(value) ? 0 : value;
  },
);

const selectFinancingVolumeCorporate = createSelector(
  selectIsBusinessCaseCorporate,
  selectFacilities,
  (isCorporate, facilities) => {
    if (!isCorporate || isNil(facilities) || facilities.length === 0) {
      return 0;
    }

    const value = Number(
      facilities
        .map((facility) => facility.facilityFields)
        ?.flat()
        .find((field) => field.key === TEMPLATE_FIELD_KEYS.FinancingVolume)
        ?.value,
    );
    return isNaN(value) ? 0 : value;
  },
);

export const selectRealEstateStructureFinancingVolume = createSelector(
  selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00130]),
  selectRawStaticGroups,
  (canManageBankGroup, groups) => {
    if (!canManageBankGroup || groups.length === 0) {
      return 0;
    }
    const value = Number(
      groups
        .find(({ key }) => key === REFS_BLOCKS_GROUP_KEY)
        ?.fields.find(
          ({ key }) =>
            key === SpecialFieldLabelOrKeyEnum.REQUESTED_FINANCING_VOLUME,
        )?.value,
    );
    return isNaN(value) ? 0 : value;
  },
);

const selectRealEstateParticipantLeaderFinancingVolume = createSelector(
  selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00130]),
  selectRawStaticGroups,
  (canManageBankGroup, groups: CustomFinStructureGroup[]) => {
    if (canManageBankGroup || groups.length === 0) {
      return 0;
    }
    const mainGroup = groups.find((group) => group.key === GroupKey.FINANCING);
    if (isNil(mainGroup)) {
      return 0;
    }
    const subGroup = mainGroup.subGroups.find(
      (group) => group.key === GroupKey.REQUESTED_FINANCING,
    );
    if (isNil(subGroup)) {
      return 0;
    }
    const field = subGroup.fields.find(
      (field) =>
        field.key === SpecialFieldLabelOrKeyEnum.REQUESTED_FINANCING_VOLUME,
    );
    if (isNil(field)) {
      return 0;
    }
    const value = field.value ? Number(field.value) : 0;
    return isNaN(value) ? 0 : value;
  },
);

export const selectFinancingVolumeRealEstate = createSelector(
  selectIsBusinessCaseRealEstate,
  selectRealEstateStructureFinancingVolume,
  selectRealEstateParticipantLeaderFinancingVolume,
  (isRealEstate, structurerValue, participantValue) => {
    if (!isRealEstate) {
      return 0;
    }
    return structurerValue || participantValue;
  },
);

export const selectFinancingVolume = createSelector(
  selectFinancingVolumeMiscellaneous,
  selectFinancingVolumeCorporate,
  selectFinancingVolumeRealEstate,
  (miscellaneousVolume, corporateVolume, realEstateVolume) =>
    miscellaneousVolume || corporateVolume || realEstateVolume,
);

export const selectFinancingVolumeMinAndMax = createSelector(
  selectFinancingVolume,
  selectParticipationMaxAmount,
  selectParticipationMinAmount,
  (financingVolume, participationMaxAmount, participationMinAmount) => {
    return { financingVolume, participationMaxAmount, participationMinAmount };
  },
);

export const selectFinancingVolumeWithFields = createSelector(
  selectIsBusinessCaseRealEstate,
  selectRealEstateStructureFinancingVolume,
  selectRawDynamicFieldsets,
  (
    isRealEstate,
    financingVolume,
    dynamicFieldsets: CustomFinStructureGroup[],
  ) => {
    if (!isRealEstate || dynamicFieldsets.length === 0) {
      return 0;
    }
    const summedFieldAmount = dynamicFieldsets
      .filter(
        (entity) =>
          entity.group ===
          SpecialFieldLabelOrKeyEnum.DERIVATE_LINE_CREDIT_AMOUNT,
      )
      .map((group: CustomFinStructureGroup) => {
        const generalGroup = group.subGroups.find(
          (subGroup) => subGroup.key === GroupKey.GENERAL,
        );
        return generalGroup?.fields?.find(
          (field) => field.label === FIELD_LABEL_SUFFIX,
        );
      })
      .reduce((acc, curr) => {
        return acc + Number(curr.value);
      }, 0);

    return financingVolume + summedFieldAmount;
  },
);

export const selectAmountToBeCollected = createSelector(
  selectCurrentUserPartner,
  selectFinancingVolume,
  (ownAmount, financingVolume) =>
    financingVolume -
    (neitherNullNorUndefined(ownAmount)
      ? ownAmount.totalParticipationAmount
      : 0),
);

export const selectChartBreakDownVisibility = createSelector(
  selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00130]),
  selectHasAnyBusinessCasePermission([
    BusinessCasePermission.BCP_00063,
    BusinessCasePermission.BCP_00140,
  ]),
  selectHasAnyBusinessCasePermission([
    BusinessCasePermission.BCP_00147,
    BusinessCasePermission.BCP_00148,
  ]),
  selectCurrentUserPartner,
  selectRawDynamicFieldsets,
  (
    manageFinancingBuildingBlocks,
    manageMyParticipationAmount,
    canSeeMyParticipationBreakdown,
    currentPartner,
    dynamicFieldsets,
  ) => {
    return !!(
      manageFinancingBuildingBlocks &&
      manageMyParticipationAmount &&
      canSeeMyParticipationBreakdown &&
      currentPartner.totalParticipationAmount &&
      dynamicFieldsets.length > 0
    );
  },
);

export const selectDerivativeVisibility = createSelector(
  selectBusinessCaseParticipationType,
  selectCustomerType,
  selectIsBusinessCaseRealEstate,
  (participationType, customerType, isRealEstateCase) => {
    return (
      isRealEstateCase &&
      CustomerType.BANK === customerType &&
      participationType !== ParticipationType.PARTICIPANT
    );
  },
);

export const selectBreakdownData = createSelector(
  selectRawDynamicFieldsets,
  (dynamicFieldsets: CustomFinStructureGroup[]) => {
    const groupedItems: {
      [key: string]: CustomGroupMetadata;
    } = {};

    dynamicFieldsets
      .map((dynamicGroup) => {
        const generalGroup = dynamicGroup?.subGroups?.find(
          (subGroup) => subGroup.key === GroupKey.GENERAL,
        );
        const foundField = generalGroup?.fields?.find(
          (field) =>
            field.key.endsWith(KEY_SUFFIX) &&
            field.fieldType === FieldTypeEnum.MONETARY,
        );

        return {
          ...(foundField || {}),
          label: foundField?.fieldSetName,
          group: dynamicGroup.group,
          id: foundField?.id,
        };
      })
      .forEach((field) => {
        if (groupedItems[field.group]) {
          groupedItems[field.group].amountFields.push(field);
        } else {
          groupedItems[field.group] = {
            group: field.group,
            amountFields: [field],
            id: field.id,
          };
        }
      });

    return Object.values(groupedItems);
  },
);

export const selectSharedFinancingStructureTabVisibility = createSelector(
  selectSharedWithMe,
  (sharedWithMe) => {
    return sharedWithMe.some((participant) => participant.sharedGroups.length);
  },
);

export const selectFinancingDetailsRealEstateView = createSelector(
  selectEditTemplateMode,
  selectSharedFinancingStructureTabVisibility,
  selectActiveIndexFinanceRouterPath,
  (editMode, sharedVisibility, activeIndex) => ({
    editMode,
    sharedVisibility,
    activeIndex,
  }),
);

export const selectChartDataRealEstate = createSelector(
  selectRawStaticGroups,
  selectBreakdownData,
  selectTotalParticipationAmount,
  (staticGroups, breakdownData, totalParticipationAmount) => {
    const chartData = collectRealEstateChartData(
      staticGroups,
      breakdownData,
      totalParticipationAmount,
    );
    return transformRealEstateChartData(chartData);
  },
);

export const selectSharedFinancingStructure = createSelector(
  selectFinStructureState,
  (state) => state.sharedFinancingStructure,
);

export const selectRawStaticGroupsFromCustomer = createSelector(
  selectSharedFinancingStructure,
  (sharedFinStructure) => {
    return sharedFinStructure.staticGroups;
  },
);

export const selectStaticGroupsFromCustomer = createSelector(
  selectRawStaticGroupsFromCustomer,
  selectSearchGroupIds,
  selectSearchFieldIds,
  (
    groups: CustomFinStructureGroup[],
    matchingGroupIds: string[],
    matchingFieldIds: string[],
  ) => {
    return [groups]
      .map((groups) => buildMultiSelectFields(groups, matchingFieldIds))
      .map((groups) => setVisibleGroups(groups, matchingGroupIds))
      .map((groups) => setVisibleFields(groups, matchingFieldIds))
      .map(filterOutCompositeDependentFields)[0];
  },
);

export const selectCatalogueData = createSelector(
  selectFinStructureState,
  (state) => {
    return state.catalogueData;
  },
);

export const selectCatalogueDataDynamicBlocks = createSelector(
  selectCatalogueData,
  (catalogueData) => {
    const dynamicFieldsets = catalogueData.find((entity) => entity.dynamic);
    if (isNil(dynamicFieldsets)) {
      return {};
    }
    const financingBuildingBlockGroup: CustomFinStructureGroup =
      dynamicFieldsets.categories.find(
        (category) => category.key === REFS_BLOCKS_GROUP_KEY,
      );
    return financingBuildingBlockGroup || {};
  },
);

export const selectSideNavigationGroups = createSelector(
  selectCatalogueDataDynamicBlocks,
  selectRawStaticGroups,
  selectRawStaticGroupsFromCustomer,
  selectActiveIndexFinanceRouterPath,
  (dynamicBlocks, groups, sharedGroups, activePath) => {
    let staticGroups: CustomFinStructureGroup[] = groups;
    if (
      Object.keys(FinancingDetailsSubPage)[activePath] ===
      FinancingDetailsSubPage.SHARED_FINANCING_STRUCTURE
    ) {
      staticGroups = sharedGroups;
    }

    return staticGroups
      .map((group) => {
        const { fields, isField } = mapGroupToSideNavigation(
          group,
          dynamicBlocks,
        );

        return {
          id: group.id,
          name: group.group,
          ordinal: group.ordinal,
          fields,
          key: group.key,
          hasAnyEnabledFields:
            fields?.length === 0 || fields?.some((v) => !!v.isEnabled),
          isField,
        };
      })
      .sort((a, b) => a.ordinal - b.ordinal);
  },
);

export const selectCatalogueDataDynamicBlocksData = createSelector(
  selectCatalogueDataDynamicBlocks,
  (dynamicCategory) => {
    return dynamicCategory.subGroups?.map((category) => {
      const generalGroup = category.subGroups.find(
        (subGroup) => subGroup.key === GroupKey.GENERAL,
      );
      const fieldMetaData: string[] = generalGroup?.fields.find(
        (field) => field.fieldMetaData,
      )?.fieldMetaData as string[];
      if (fieldMetaData) {
        return {
          name: category.group,
          metaData: fieldMetaData.map((name) => ({ label: name, value: name })),
          key: category.key,
        };
      }
      return {
        name: category.group,
        key: category.key,
      };
    });
  },
);

export const selectSharedFinancingStructureView = createSelector(
  selectStaticGroupsFromCustomer,
  selectOverviewActiveTab,
  selectSharedWithMeByCustomer,
  sideNavigationsFeature.selectPageNavigationIsOpen,
  selectSideNavigationGroups,
  selectBusinessCaseCompactHeaderHeightDelta,
  (
    staticGroups,
    overviewActiveTab,
    sharedWithMe,
    isPageNavigationOpen,
    navigaitonGroups,
    headerHeight,
  ) => {
    return {
      staticGroups,
      overviewActiveTab,
      sharedWithMe,
      isPageNavigationOpen,
      navigaitonGroups,
      iconName: isPageNavigationOpen
        ? 'keyboard_arrow_left'
        : 'keyboard_arrow_right',
      headerHeight,
    };
  },
);

export const selectParticipantsWithSharedFinStructure = createSelector(
  selectCustomerNamesByKey,
  selectSharedWithMe,
  (customers, sharedWithMe) => {
    const customerNamesByKey = Object.values(customers);
    const sharedWithMeCustomers = customerNamesByKey
      .map((customer) => ({
        label: customer.name,
        value: customer.key,
      }))
      .filter((customer) =>
        sharedWithMe.some(
          (entity) =>
            entity.fromCustomerKey === customer.value &&
            entity.sharedGroups.length > 0,
        ),
      );
    return sharedWithMeCustomers;
  },
);

export const selectParticipantsForExportFinancintStructure = createSelector(
  selectParticipantsWithSharedFinStructure,
  selectUserCustomerKey,
  selectCustomerNamesByKey,
  (participantsWithSharedFinStructure, userCustomerKey, customerNames) => {
    const customerNamesByKey = Object.values(customerNames);
    const currentCustomer = customerNamesByKey.find(
      (customer) => customer.key === userCustomerKey,
    );
    return [
      { value: currentCustomer?.key, label: currentCustomer?.name },
      ...participantsWithSharedFinStructure,
    ];
  },
);

export const selectParticipationTabVisibility = createSelector(
  selectStatisticsCorporateRealEstateVisibility,
  selectInvitationTabVisibility,
  selectShowApplicationView,
  (participationVisibility, invitationVisibility, applicationVisibility) =>
    participationVisibility || invitationVisibility || applicationVisibility,
);

export const selectSharedWithMeEntities = createSelector(
  selectFinStructureState,
  (state) => state.sharedWithMeEntities,
);

export const selectIsSharedRefsTabActive = createSelector(
  selectRouteDataParam('activePath'),
  (activePath) => {
    return activePath === FinancingDetailsSubPage.SHARED_FINANCING_STRUCTURE;
  },
);

export const selectAreLatestSharingEntitiesLoading = createSelector(
  selectFinStructureState,
  (state) => {
    return state.areLatestSharingEntitiesLoading;
  },
);

export const selectRefSideNavView = createSelector(
  selectEditTemplateMode,
  selectSideNavigationGroups,
  selectBusinessCaseId,
  (isEdit, sections, businessCaseId) => ({ isEdit, sections, businessCaseId }),
);

const selectBreakdownStatisticsCorporateRealEstate = createSelector(
  selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00063]),
  selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00140]),
  selectCustomerType,
  (viewPieChartBank, viewPieChartCorpFsp, customerType) => {
    if (customerType === CustomerType.REAL_ESTATE) {
      return false;
    }
    if (customerType === CustomerType.BANK) {
      return viewPieChartBank;
    }
    return viewPieChartCorpFsp;
  },
);

const selectBankFspParticipant = createSelector(
  selectCustomerType,
  selectBusinessCaseParticipationType,
  (customerType, participationType) =>
    [CustomerType.BANK, CustomerType.FSP].includes(customerType) &&
    participationType === ParticipationType.PARTICIPANT,
);

export const selectStatisticsVisibility = createSelector(
  selectBreakdownStatisticsCorporateRealEstate,
  selectHasAnyBusinessCasePermission([
    BusinessCasePermission.BCP_00063,
    BusinessCasePermission.BCP_00140,
  ]),
  selectBankFspParticipant,
  (breakdownVisibility, hasPermission, isBankFspParticipant) =>
    isBankFspParticipant ? hasPermission : breakdownVisibility && hasPermission,
);

export const selectLatestSharedEntities = createSelector(
  selectFinStructureState,
  (state) => state.latestSharingEntities,
);

export const selectGroupOfInformation = createSelector(
  selectFinStructureState,
  (state) => state.sharedGroupInformation,
);

export const selectSharedGroupInformationGroups = createSelector(
  selectGroupOfInformation,
  (groupOfInformation) => groupOfInformation.staticGroups,
);

export const selectSharedGroupInformationDynamicFieldsets = createSelector(
  selectGroupOfInformation,
  (groupOfInformation) => groupOfInformation.dynamicFieldsets,
);

export const selectSharedGroupsWithParticipant = createSelector(
  selectSharedGroupInformationGroups,
  selectSharedGroupInformationDynamicFieldsets,
  (staticGroups, dynamicFieldsets) => {
    return staticGroups.map((sg) =>
      sg.group === GroupKey.FINANCING_MODULES
        ? {
            ...sg,
            subGroups: sortBy(dynamicFieldsets, ['group']),
          }
        : sg,
    );
  },
);

export const selectSharedDynamicFieldsets = createSelector(
  selectSharedFinancingStructure,
  (sharedFinancingStructure) => {
    return sharedFinancingStructure.dynamicFieldsets;
  },
);

export const selectDynamicFieldsets = createSelector(
  selectIsSharedRefsTabActive,
  selectRawDynamicFieldsets,
  selectSharedDynamicFieldsets,
  (isSharedTabActive, ownDynamicFieldsets, sharedDynamicFieldsets) => {
    const dynamicFieldsets = isSharedTabActive
      ? sharedDynamicFieldsets
      : ownDynamicFieldsets;
    return dynamicFieldsets
      .slice()
      .sort(
        (a, b) =>
          a.group.localeCompare(b.group) ||
          a.fieldsetName.localeCompare(b.fieldsetName),
      );
  },
);

export const selectActiveBuildingBlock = createSelector(
  selectActiveBuildingBlockId,
  selectDynamicFieldsets,
  (activeBlockId, dynamicFieldsets) => {
    const blockWithActiveId = dynamicFieldsets.find(
      (block) => block.id === activeBlockId,
    );
    return blockWithActiveId || dynamicFieldsets[0];
  },
);

export const selectActiveBuildingBlockName = createSelector(
  selectDynamicFieldsets,
  selectActiveBuildingBlock,
  (dynamicFieldsets, activeBuildingBlock) => {
    return (
      dynamicFieldsets.find((dfs) => dfs.id === activeBuildingBlock)
        ?.fieldsetName || dynamicFieldsets[0]?.fieldsetName
    );
  },
);

export const selectDynamicFieldsetsNames = createSelector(
  selectDynamicFieldsets,
  (dynamicFieldsets) =>
    dynamicFieldsets.map((fieldset) =>
      fieldset.fieldsetName.toLocaleLowerCase(),
    ),
);

export const selectRefsFinancingTabsView = createSelector(
  selectBusinessCase,
  selectActiveBuildingBlock,
  selectIsSharedRefsTabActive,
  selectDynamicFieldsetsNames,
  selectEditTemplateMode,
  selectHighlightedId,
  (
    businessCase,
    activeBuildingBlock,
    isSharedTab,
    dynamicFieldsetsNames,
    editMode,
    highlightedId,
  ) => {
    return {
      businessCase,
      isSharedTab,
      activeBlockId: activeBuildingBlock?.id,
      dynamicFieldsetsNames,
      editMode,
      highlightedId,
    };
  },
);

export const selectFinancingBlocks = createSelector(
  selectDynamicFieldsets,
  (dynamicFieldsets: CustomFinStructureGroup[]) => {
    return dynamicFieldsets.map((dfs) => {
      return {
        amount: dynamicBuildingBlockAmount(dfs),
        id: dfs.id,
        title: dfs.group,
        subTitle: dfs.fieldsetName,
      };
    });
  },
);

export const selectRefContentView = createSelector(
  selectSideNavigationGroups,
  selectBusinessCaseId,
  selectBusinessCaseCompactHeaderHeightDelta,
  (sections, businessCaseId, headerHeight) => ({
    sections,
    businessCaseId,
    headerHeight,
  }),
);

export const selectChartData = createSelector(
  selectRawStaticGroups,
  selectRawDynamicFieldsets,
  (staticGroups, dynamicFieldsets) => {
    const gatheredAmount = dynamicFieldsets.reduce((acc, curr) => {
      acc += dynamicBuildingBlockAmount(curr);
      return acc;
    }, 0);

    const targetAmount = parseFloat(
      staticGroups
        ?.find(
          (group: CustomFinStructureGroup) =>
            group.key === REFS_BLOCKS_GROUP_KEY,
        )
        ?.fields?.find(
          (field: FinStructureField) =>
            field.key === SpecialFieldLabelOrKeyEnum.REQUESTED_FINANCING_VOLUME,
        )
        ?.value?.toString() || '0',
    );

    const dfsGroupId = staticGroups.find(
      (group) => group.key === REFS_BLOCKS_GROUP_KEY,
    )?.id;

    const chartData = [];

    chartData.push({
      name: GroupKey.FINANCING_MODULES,
      value: gatheredAmount,
      extra: dfsGroupId,
      clickable: true,
    });

    if (gatheredAmount < targetAmount || targetAmount === 0) {
      chartData.push({
        name: $localize`:@@dashboard.businessCase.charts.notStructuredText:Nicht strukturiert`,
        value: Math.max(targetAmount - gatheredAmount, 0),
        clickable: false,
      });
    }

    return {
      targetAmount,
      gatheredAmount,
      chartData: {
        series: chartData.filter((entity) => entity.value),
      },
    };
  },
);

export const selectStaticGroups = createSelector(
  selectRawStaticGroups,
  selectSearchGroupIds,
  selectSearchFieldIds,
  (
    groups: CustomFinStructureGroup[],
    matchingGroupIds: string[],
    matchingFieldIds: string[],
  ) => {
    // Best place to do it, UI for sure
    return [groups]
      .map((groups) => buildMultiSelectFields(groups, matchingFieldIds))
      .map((groups) => setVisibleGroups(groups, matchingGroupIds))
      .map((groups) => setVisibleFields(groups, matchingFieldIds))
      .map(filterOutCompositeDependentFields)
      .map(setWarningOnSpecialFields)[0];
  },
);
