import {
  StateLibBusinessCaseRealEstateApiActions,
  StateLibBusinessCaseRealEstatePageActions,
  StateLibFinancingStructureApiActions,
} from '@fincloud/state/business-case-real-estate';
import { FinancingDetailsState } from '@fincloud/types/models';
import { INITIAL_FINANCING_STRUCTURE } from '@fincloud/utils';
import { Action, ActionReducer, createReducer, on } from '@ngrx/store';
import { OverviewPageNav } from '../../enums/overview-page-navs';
import { RefsApiActions, RefsPageActions } from '../actions';

export const initialState: FinancingDetailsState = {
  catalogueData: [],
  activeBuildingBlockId: '',
  overviewActiveTab: OverviewPageNav.OVERVIEW,
  latestSharingEntities: [],
  areLatestSharingEntitiesLoading: false,
  sharedWithMeEntities: [],
  sharedFinancingStructure: INITIAL_FINANCING_STRUCTURE,
  sharedGroupInformation: INITIAL_FINANCING_STRUCTURE,
};

export const financialStructureReducer: ActionReducer<
  FinancingDetailsState,
  Action
> = createReducer(
  initialState,
  on(
    RefsApiActions.loadCatalogueDataSuccess,
    (state, action): FinancingDetailsState => {
      return {
        ...state,
        catalogueData: action.catalogueData,
      };
    },
  ),
  on(
    RefsPageActions.clearFinancialStructureCase,
    StateLibBusinessCaseRealEstatePageActions.clearFinancialStructureCase,
    (): FinancingDetailsState => {
      return {
        ...initialState,
      };
    },
  ),
  on(
    StateLibFinancingStructureApiActions.loadFinancingStructureSuccess,
    StateLibBusinessCaseRealEstateApiActions.removeDynamicFieldSetFromCaseSuccess,
    StateLibBusinessCaseRealEstateApiActions.addDynamycFieldsetToFinancingStructureSuccess,
    StateLibBusinessCaseRealEstateApiActions.updateRefsFieldDescriptionSuccess,
    (state, action): FinancingDetailsState => {
      const dynamicFieldsets = action.financingStructure.dynamicFieldsets
        .slice()
        .sort(
          (a, b) =>
            a.group.localeCompare(b.group) ||
            a.fieldsetName.localeCompare(b.fieldsetName),
        );
      return {
        ...state,
        activeBuildingBlockId:
          state.activeBuildingBlockId || dynamicFieldsets[0]?.id,
      };
    },
  ),
  on(
    RefsPageActions.clearCatalogueData,
    (state: FinancingDetailsState): FinancingDetailsState => {
      return {
        ...state,
        catalogueData: [],
      };
    },
  ),
  on(
    RefsPageActions.setActiveBuildingBlock,
    (state, action): FinancingDetailsState => {
      return {
        ...state,
        activeBuildingBlockId: action.id,
      };
    },
  ),
  on(
    RefsPageActions.setOverviewPageActiveTab,
    (state, action): FinancingDetailsState => {
      return {
        ...state,
        overviewActiveTab: action.payload,
      };
    },
  ),
  on(
    RefsApiActions.getLatestSharingGroupsSuccess,
    (state, action): FinancingDetailsState => {
      return {
        ...state,
        latestSharingEntities: action.payload,
      };
    },
  ),
  on(
    StateLibBusinessCaseRealEstatePageActions.getSharedEntitiesWithMe,
    (state): FinancingDetailsState => {
      return { ...state, areLatestSharingEntitiesLoading: true };
    },
  ),
  on(
    RefsApiActions.getSharedEntitiesWithMeSuccess,
    (state, action): FinancingDetailsState => {
      return {
        ...state,
        sharedWithMeEntities: action.payload,
        areLatestSharingEntitiesLoading: false,
      };
    },
  ),
  on(
    RefsApiActions.getSharedEntitiesWithMeFailure,
    (state): FinancingDetailsState => {
      return {
        ...state,
        areLatestSharingEntitiesLoading: false,
      };
    },
  ),
  on(
    StateLibBusinessCaseRealEstatePageActions.shareGroupWithParticipant,
    (state): FinancingDetailsState => {
      return {
        ...state,
        areLatestSharingEntitiesLoading: true,
      };
    },
  ),
  on(
    RefsApiActions.shareGroupWithParticipantSuccess,
    (state, action): FinancingDetailsState => {
      return {
        ...state,
        areLatestSharingEntitiesLoading: false,
        latestSharingEntities: state.latestSharingEntities.map((e) =>
          e.toCustomerKey === action.payload.toCustomerKey
            ? { ...e, sharedGroups: action.payload.groupIds }
            : e,
        ),
      };
    },
  ),
  on(
    RefsApiActions.shareGroupWithParticipantFailure,
    (state): FinancingDetailsState => {
      return {
        ...state,
        areLatestSharingEntitiesLoading: false,
      };
    },
  ),
  on(
    RefsApiActions.getSharedFinancingStructureSuccess,
    (state, action): FinancingDetailsState => {
      const dynamicFieldsets = action.payload.dynamicFieldsets
        .slice()
        .sort(
          (a, b) =>
            a.group.localeCompare(b.group) ||
            a.fieldsetName.localeCompare(b.fieldsetName),
        );
      return {
        ...state,
        sharedFinancingStructure: action.payload,
        activeBuildingBlockId:
          state.activeBuildingBlockId || dynamicFieldsets[0]?.id,
      };
    },
  ),
  on(
    RefsPageActions.clearSharedFinancingStructure,
    StateLibBusinessCaseRealEstatePageActions.getSharedFinancingStructure,
    (state): FinancingDetailsState => {
      return {
        ...state,
        sharedFinancingStructure: INITIAL_FINANCING_STRUCTURE,
      };
    },
  ),
  on(
    RefsApiActions.getSharedGroupInformationSuccess,
    (state, action): FinancingDetailsState => {
      return {
        ...state,
        sharedGroupInformation: action.payload,
      };
    },
  ),
  on(
    StateLibBusinessCaseRealEstatePageActions.clearSharedGroupInformation,
    (state): FinancingDetailsState => {
      return {
        ...state,
        sharedGroupInformation: INITIAL_FINANCING_STRUCTURE,
      };
    },
  ),
  on(
    StateLibBusinessCaseRealEstateApiActions.removeDynamicFieldSetFromCaseSuccess,
    (state, action): FinancingDetailsState => {
      const activeTab = action.financingStructure.dynamicFieldsets?.find(
        (fieldset) => fieldset.id === state.activeBuildingBlockId,
      );

      return {
        ...state,
        activeBuildingBlockId: activeTab
          ? state.activeBuildingBlockId
          : action.financingStructure.dynamicFieldsets[0]?.id,
      };
    },
  ),
  on(
    StateLibBusinessCaseRealEstateApiActions.addDynamycFieldsetToFinancingStructureSuccess,
    (state, action): FinancingDetailsState => {
      return {
        ...state,
        overviewActiveTab: OverviewPageNav.BUILDING_BLOCKS,
        activeBuildingBlockId: action.blockId,
      };
    },
  ),
);
