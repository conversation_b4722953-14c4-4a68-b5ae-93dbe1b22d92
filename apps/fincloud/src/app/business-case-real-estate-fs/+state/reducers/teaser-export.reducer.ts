import { BusinessCaseRealEstateTeaserExportState } from '@fincloud/types/models';
import { INITIAL_FINANCING_STRUCTURE } from '@fincloud/utils';
import { createFeature, createReducer, on } from '@ngrx/store';
import { TeaserExportApiActions, TeaserExportPageActions } from '../actions';
import { teaserExportSelectors } from '../selectors';

const initialState: BusinessCaseRealEstateTeaserExportState = {
  disabledGroups: [],
  financingStructure: INITIAL_FINANCING_STRUCTURE,
};

export const teaserExportFeature = createFeature({
  name: 'teaserExport',
  reducer: createReducer(
    initialState,
    on(
      TeaserExportPageActions.getMyFinStructureTeaser,
      TeaserExportPageActions.getCustomerFinStructureTeaser,
      TeaserExportPageActions.clearTeaserExport,
      (): BusinessCaseRealEstateTeaserExportState => initialState,
    ),
    on(
      TeaserExportApiActions.getMyFinStructureTeaserSuccess,
      TeaserExportApiActions.getCustomerFinStructureTeaserSuccess,
      (state, action): BusinessCaseRealEstateTeaserExportState => {
        const { disabledGroups, financingStructure } = action.response;
        return {
          ...state,
          financingStructure: {
            ...state.financingStructure,
            ...financingStructure,
          },
          disabledGroups: disabledGroups,
        };
      },
    ),
  ),
  extraSelectors: teaserExportSelectors,
});
