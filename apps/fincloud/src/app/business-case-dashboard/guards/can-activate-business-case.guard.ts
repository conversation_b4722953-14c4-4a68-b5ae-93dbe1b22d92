import { inject } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  GuardR<PERSON>ult,
  MaybeAsync,
  Router,
} from '@angular/router';
import {
  StateLibBusinessCasePageActions,
  StateLibContextPageActions,
  selectCustomerContextWithBusinessCase,
  selectLastVisitedUrl,
} from '@fincloud/state/business-case';
import {
  AdministrationPath,
  FinancingDetailsPath,
} from '@fincloud/types/enums';
import { Store } from '@ngrx/store';
import { isEmpty } from 'lodash-es';
import { filter, map, switchMap, tap } from 'rxjs';

export function canActivateBusinessCaseGuard(
  route: ActivatedRouteSnapshot,
): MaybeAsync<GuardResult> {
  const store = inject(Store);
  const businessCaseId = route.paramMap.get('id');
  const router = inject(Router);
  return store.select(selectLastVisitedUrl).pipe(
    tap((lastVisitedUrl) => {
      const isComingFromSameCaseChatPage =
        lastVisitedUrl.includes('chat') &&
        lastVisitedUrl.includes(businessCaseId);
      const isNotNavigatingToSameCaseChat =
        !route.routeConfig.path.includes('chat') ||
        (route.routeConfig.path.includes('chat') &&
          !lastVisitedUrl.includes(businessCaseId));
      if (
        !isComingFromSameCaseChatPage &&
        isNotNavigatingToSameCaseChat &&
        !route.routeConfig.path.includes('duplicate-case') &&
        lastVisitedUrl &&
        !lastVisitedUrl.includes(FinancingDetailsPath.FINANCING_DETAILS) &&
        !lastVisitedUrl.includes(AdministrationPath.MANAGEMENT)
      ) {
        store.dispatch(
          StateLibBusinessCasePageActions.clearBusinessCase({ businessCaseId }),
        );
      }
    }),
    switchMap((lastVisitedUrl) => {
      store.dispatch(
        StateLibContextPageActions.loadBusinessCaseContext({
          payload: businessCaseId,
        }),
      );

      return store.select(selectCustomerContextWithBusinessCase).pipe(
        filter(
          ({ customerCtx, businessCase }) =>
            !isEmpty(customerCtx) && !!businessCase,
        ),
        map(() => {
          if (lastVisitedUrl && lastVisitedUrl !== router.url) {
            return router.createUrlTree([lastVisitedUrl]);
          }
          return true;
        }),
      );
    }),
  );
}
