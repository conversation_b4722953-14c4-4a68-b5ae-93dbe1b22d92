import {
  selectBusinessCase,
  selectBusinessCaseCurrentStatus,
  selectBusinessCaseDefaultPerception,
  selectBusinessCaseStatusOptions,
  selectBusinessCaseType,
  selectBusinessCaseTypeLabel,
  selectCanShowHeaderInvitationNavigation,
  selectIsBusinessCaseHeaderInView,
  selectIsBusinessCaseRealEstate,
  selectShowApplicationInvitationButton,
  selectTotalInvestmentAmount,
} from '@fincloud/state/business-case';
import { selectExistingChats } from '@fincloud/state/chat';

import { SocketService, SocketType } from '@fincloud/core/socket';
import { Toast } from '@fincloud/core/toast';
import {
  AccessRights,
  AuthTokensState,
  BusinessCase,
  ChartData,
  UserToken,
} from '@fincloud/types/models';

import { ConnectedPosition } from '@angular/cdk/overlay';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';
import { ConfirmationModalComponent } from '@fincloud/components/modals';
import { RefsUtilsService } from '@fincloud/components/refs';
import { TokenManagementService } from '@fincloud/core/auth';
import { not } from '@fincloud/core/utils';
import { ConfirmationDialogSvgComponent } from '@fincloud/neoshare/business-case';
import {
  StateLibBusinessCasePageActions,
  selectCanPlatformManagerAddHimself,
  selectRefsCaseCommonFields,
} from '@fincloud/state/business-case';
import { StateLibChatPageActions } from '@fincloud/state/chat';
import { selectCustomer } from '@fincloud/state/customer';
import { StateLibInvitationPageActions } from '@fincloud/state/invitation';
import {
  selectHasUserPermission,
  selectUserCustomerKey,
  selectUserToken,
} from '@fincloud/state/user';
import { CustomerManagementControllerService } from '@fincloud/swagger-generator/authorization-server';
import {
  ContactPersonControllerService,
  TransferLeadershipControllerService,
} from '@fincloud/swagger-generator/business-case-manager';
import {
  Chat,
  ChatNotificationStatusControllerService,
} from '@fincloud/swagger-generator/communication';
import { FinStructureField } from '@fincloud/swagger-generator/demo';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import {
  BusinessCasePermission,
  BusinessCaseType,
  Permission,
} from '@fincloud/types/enums';
import { FinBadgeStatus } from '@fincloud/ui/badges';
import { FinButtonAppearance, FinButtonShape } from '@fincloud/ui/button';
import { FinModalService } from '@fincloud/ui/modal';
import { FinSeparator } from '@fincloud/ui/separators';
import { FinToastService } from '@fincloud/ui/toast';
import { FinSize } from '@fincloud/ui/types';
import { FinWarningMessageAppearance } from '@fincloud/ui/warning-message';
import {
  BUSINESS_CASE_TYPE_ICONS,
  BUSINESS_CASE_TYPE_LABELS,
  CHAT_SOCKET_RECEIVE_MESSAGE_DESTINATION,
} from '@fincloud/utils';
import { Store } from '@ngrx/store';
import { forkJoin, merge } from 'rxjs';
import {
  distinctUntilChanged,
  filter,
  map,
  shareReplay,
  switchMap,
  take,
  tap,
  withLatestFrom,
} from 'rxjs/operators';

@Component({
  selector: 'app-business-case-dashboard-header',
  templateUrl: './business-case-dashboard-header.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BusinessCaseDashboardHeaderComponent implements OnInit {
  @Input()
  access: AccessRights;

  @Input()
  businessCaseInfo: BusinessCase;

  @Input()
  ownCompanyId: string;

  defaultPerceptionValue$ = this.store
    .select(selectBusinessCaseDefaultPerception)
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  @Input()
  reasonForClosedCase: string;

  @Input()
  isCaseActive: boolean;

  @Input()
  isLead: boolean;

  @Input()
  canChangeBusinessCaseState: boolean;

  @Input()
  canChangeBusinessCaseStatus: boolean;

  @Input()
  participantHasBeenAccepted: boolean;

  @Input()
  isPartOfParticipants: boolean;

  @Input()
  receivedInvitation: boolean;

  @Output()
  statusChanged = new EventEmitter<string>();

  @Output()
  closeCase = new EventEmitter<void>();

  @Output()
  reactivateCase = new EventEmitter<void>();

  businessCase: ExchangeBusinessCase;
  readonlyMonetaryFormat = '1.0-2';
  finBadgeStatus = FinBadgeStatus;
  caseType: string;

  finWarningMessageAppearance = FinWarningMessageAppearance;

  getMainRefsCommonFields$ = this.store.select(selectRefsCaseCommonFields);

  totalInvestmentAmount$ = this.store.select(selectTotalInvestmentAmount);

  getCustomer$ = this.store.select(selectCustomer).pipe(filter(Boolean));

  isBusinessCaseHeaderInView$ = this.store
    .select(selectIsBusinessCaseHeaderInView)
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  getBusinessCaseType$ = this.store.select(selectBusinessCaseType).pipe(
    filter(Boolean),
    tap((type) => {
      this.caseType = BUSINESS_CASE_TYPE_LABELS[type];
    }),
    take(1),
  );

  showParticipationRole$ = this.getBusinessCaseType$.pipe(
    map((c) => c === BusinessCaseType.FINANCING_CASE),
  );

  nonRealEstateTotalVolumeText = $localize`:@@dashboard.businessCase.card.financingVolume:Finanzierungsvolumen`;
  realEstateTotalVolumeText = $localize`:@@dashboard.businessCase.card.totalInvestmentCosts:Gesamtinvestitionskosten`;

  readonly businessCasePermission = BusinessCasePermission;

  canAccessCompanyDetailsView$ = this.store.select(
    selectHasUserPermission(Permission.PERM_0058),
  );

  readonly conformationMessageFirstPart = $localize`:@@businessCaseDashboard.conformationMessageFirstPart:möchte Ihnen den Finanzierungsfall`;
  readonly conformationMessageSecondPart = $localize`:@@businessCaseDashboard.conformationMessageSecondPart:übergeben`;
  readonly joinCasePlatformManagerTranslation = $localize`:@@businessCaseDashboard.button.label.platformManagerJoin:Mich hinzufügen`;

  customerKey$ = this.store.select(selectUserCustomerKey);

  businessCaseTypeLabel$ = this.store.select(selectBusinessCaseTypeLabel);

  businessCaseType$ = this.store.select(selectBusinessCaseType);

  canShowHeaderInvitationNavigation$ = this.store.select(
    selectCanShowHeaderInvitationNavigation,
  );

  businessCaseStatuses$ = this.store.select(selectBusinessCaseStatusOptions);

  caseStatus$ = this.store.select(selectBusinessCaseCurrentStatus);

  businessCaseTypeIcons = BUSINESS_CASE_TYPE_ICONS;

  canPlatformManagerAddHimself$ = this.store.select(
    selectCanPlatformManagerAddHimself,
  );

  get caseId() {
    return this.route.snapshot.paramMap.get('id');
  }

  showApplicationInvitationButton$ = this.store.select(
    selectShowApplicationInvitationButton,
  );

  hasUnreadMessages: boolean;
  tokenParsed: UserToken;
  chats: Chat[];
  confirmationModalClass = 'confirmation-dialog';
  chatId: string;
  navigateParams: string[];
  realEstateTotalBreakdown: ChartData[];
  finSize = FinSize;
  buttonType = FinButtonShape;
  finButtonAppearance = FinButtonAppearance;
  finSeparatorType = FinSeparator;

  get canDisplaySelect() {
    if (this.isCaseActive && this.canChangeBusinessCaseStatus) {
      return true;
    }

    if (!this.isCaseActive) {
      if (this.isLead && this.canChangeBusinessCaseStatus) {
        return true;
      }
    }
    return false;
  }

  readonly isRealEstateCase$ = this.store.select(
    selectIsBusinessCaseRealEstate,
  );

  readonly financingBreakdownOverlayPositions: ConnectedPosition[] = [
    {
      originX: 'start',
      originY: 'bottom',
      overlayX: 'start',
      overlayY: 'top',
      offsetY: 5,
    },
    {
      originX: 'start',
      originY: 'bottom',
      overlayX: 'end',
      overlayY: 'top',
      offsetY: 5,
    },
  ];

  constructor(
    private destroyRef: DestroyRef,
    private transferLeadershipControllerService: TransferLeadershipControllerService,
    private socketService: SocketService,
    private chatNotificationControllerService: ChatNotificationStatusControllerService,
    private customerManagementControllerService: CustomerManagementControllerService,
    private cd: ChangeDetectorRef,
    private router: Router,
    private store: Store,
    private authTokensStore: Store<AuthTokensState>,
    private finToastService: FinToastService,
    private refsUtilService: RefsUtilsService,
    private contactPersonControllerService: ContactPersonControllerService,
    private route: ActivatedRoute,
    private tokenManagementService: TokenManagementService,
    private finModalService: FinModalService,
  ) {}

  ngOnInit(): void {
    this.store
      .select(selectBusinessCase)
      .pipe(filter(Boolean), takeUntilDestroyed(this.destroyRef))
      .subscribe((businessCase) => {
        this.businessCase = businessCase;
      });
    this.store
      .select(selectUserToken)
      .pipe(
        filter(Boolean),
        tap((userToken) => {
          this.tokenParsed = userToken;
          this.checkForUnreadMessages(userToken as UserToken);
          this.pathToChat(this.caseId, userToken.customer_key);
        }),
        switchMap((userToken) =>
          this.store.select(selectExistingChats).pipe(
            filter((chats) => chats.length > 0),
            switchMap((existingChats) => {
              const messageStreams = existingChats.map((chat: Chat) => {
                const destination = `${CHAT_SOCKET_RECEIVE_MESSAGE_DESTINATION}-${chat.id}`;
                return this.socketService.getMessagesByDestination$(
                  destination,
                  SocketType.CHAT,
                );
              });

              return merge(...messageStreams).pipe(
                tap(() => {
                  this.checkForUnreadMessages(userToken);
                }),
              );
            }),
          ),
        ),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();

    this.getMainRefsCommonFields$
      .pipe(
        filter((x) => !!x?.length),
        distinctUntilChanged(),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((fields: FinStructureField[]) => {
        this.realEstateTotalBreakdown =
          this.refsUtilService.getRefsBreakdownData(fields);
      });

    this.store
      .select(selectExistingChats)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((chats) => {
        this.chats = chats;
      });
  }

  acceptInvitation() {
    this.store.dispatch(StateLibInvitationPageActions.acceptInvitation());
  }

  rejctInvitation() {
    this.store.dispatch(StateLibInvitationPageActions.declineInvitation());
  }

  addPlatformManagerToCase() {
    this.finModalService
      .open(ConfirmationModalComponent, {
        data: {
          title: $localize`:@@businessCaseDashboard.addMeToCase.confirmationText:Sind Sie sicher, dass Sie am Fall ${this.businessCase.autoGeneratedBusinessCaseName} teilnehmen möchten?`,
          confirmButtonAppearance: FinButtonAppearance.PRIMARY,
          confirmLabel: $localize`:@@button.label.confirm:Ja, ich bin sicher`,
          cancelLabel: $localize`:@@button.label.cancel.no:Nein`,
          svgIcon: 'svgAddMeToCase',
          size: FinSize.L,
        },
        size: FinSize.S,
        disableClose: true,
      })
      .afterClosed()
      .pipe(
        filter(Boolean),
        tap(() =>
          this.store.dispatch(
            StateLibBusinessCasePageActions.addPlatformManagerToCase(),
          ),
        ),
      )
      .subscribe();
  }

  private checkForUnreadMessages(tokenParsed: UserToken) {
    //TODO nested subscribe
    if (!this.hasUnreadMessages) {
      this.chatNotificationControllerService
        .getAllUnreadMessagesOfUsersAllChats({
          userId: tokenParsed.sub,
          businessCaseId: this.businessCase?.id,
          customerKey: tokenParsed.customer_key,
        })
        .pipe(withLatestFrom(this.store.select(selectExistingChats)))
        .subscribe({
          next: ([response, chats]) => {
            const unreadMessagesPerChat = response.countOfUnreadMessagesPerChat;
            if (unreadMessagesPerChat) {
              const unreadChatsIds = Object.keys(unreadMessagesPerChat).filter(
                (chatKey) => !!unreadMessagesPerChat[chatKey],
              );

              const isPartOfUnreadChat = unreadChatsIds.some((chatId) => {
                const chat = chats.find((c) => c.id === chatId);
                if (
                  chat?.chatType === 'INTERNAL_BILATERAL' ||
                  chat?.chatType === 'INTERNAL_GROUP'
                ) {
                  return chat.chatUsers.some(
                    (cu) => cu.userId === this.tokenParsed.sub,
                  );
                }

                if (!chat) {
                  this.store.dispatch(
                    StateLibChatPageActions.refreshAllChats({
                      payload: this.businessCase.id,
                    }),
                  );
                }

                return true;
              });

              if (isPartOfUnreadChat) {
                this.hasUnreadMessages = true;
                this.cd.detectChanges();
              }
            }
          },
          error: (err) => console.error(err?.message),
        });
    }
  }

  pathToChat(businessCaseId: string, customerKey: string) {
    this.navigateParams = [
      customerKey,
      'business-case',
      businessCaseId,
      'chat',
    ];

    if (this?.access?.granular?.isRepresentingCurrentUserCustomer) {
      this.navigateParams.push('business-case');
      this.chatId = 'chat/business-case';
    } else {
      if (this?.access?.granular?.isRepresentingCurrentUserCustomer) {
        this.navigateParams.push('business-case');
        this.chatId = 'chat/business-case';
      } else {
        const internalChat = this.chats?.find(
          (c) =>
            c.status === 'ACTIVE' &&
            c.chatType === 'INTERNAL' &&
            c.customerKey === this.tokenParsed.customer_key,
        );
        if (internalChat) {
          this.navigateParams.push(...['internal', internalChat.id]);
          this.chatId = `chat/internal/${internalChat.id}`;
        }
      }
    }
  }

  // shouldExecuteOnClick: boolean,  - 1st arg
  navigateToChat(customerKey: string) {
    this.pathToChat(this.caseId, customerKey);

    // if (shouldExecuteOnClick) {
    void this.router.navigate(this.navigateParams);
    // }
  }

  openMyParticipation(customerKey: string) {
    void this.router.navigate(
      [
        customerKey,
        'business-case',
        this.businessCaseInfo?.id,
        'financing-details',
        'my-participation',
      ],
      {
        skipLocationChange: false,
      },
    );
  }

  openCaseTransferDialog(caseId: string) {
    forkJoin([
      this.customerManagementControllerService.getCustomerByKeyPublic({
        customerKey: this.businessCase.leadCustomerKey,
      }),
      this.customerKey$.pipe(take(1)),
    ])
      .pipe(
        switchMap(([leadCustomerInfo, userCustomerKey]) => {
          return this.finModalService
            .open(ConfirmationDialogSvgComponent, {
              data: {
                confirmationMessage: `${leadCustomerInfo.name} ${this.conformationMessageFirstPart} ${caseId} ${this.conformationMessageSecondPart}`,
                confirmationText: $localize`:@@businessCaseDashboard.transferCase.confirmationText:Möchten Sie diesen annehmen?`,
                svgIcon: 'svgTransferLeadership',
                confirmLabel: $localize`:@@businessCaseDashboard.transferCase.confirm:Annehmen`,
                cancelLabel: $localize`:@@businessCase.dashboard.button.label.reject:Ablehnen`,
                newCaseOwner: true,
              },
              size: FinSize.S,
              disableClose: true,
            })
            .afterClosed()
            .pipe(
              switchMap((result) => {
                const newLeaderCustomerParticipant =
                  this.businessCase.participants.find(
                    (p) => p.customerKey === userCustomerKey,
                  );

                if (result) {
                  return this.transferLeadershipControllerService
                    .acceptTransferLeadership({
                      businessCaseId: this.businessCaseInfo?.id,
                      newLeaderCustomerKey: this.tokenParsed.customer_key,
                      body: {
                        adminUserIds: [],
                      },
                    })
                    .pipe(
                      switchMap(() =>
                        this.contactPersonControllerService
                          .deleteAllContactPersons({
                            businessCaseId: this.businessCase.id,
                          })
                          .pipe(
                            switchMap(() =>
                              this.contactPersonControllerService.addContactPersons(
                                {
                                  businessCaseId: this.businessCase.id,
                                  body: newLeaderCustomerParticipant.users.map(
                                    (u) => u.userId,
                                  ),
                                },
                              ),
                            ),
                          ),
                      ),
                    );
                } else {
                  return this.transferLeadershipControllerService.rejectTransferLeadership(
                    {
                      businessCaseId: this.businessCaseInfo?.id,
                      newLeaderCustomerKey: this.tokenParsed.customer_key,
                    },
                  );
                }
              }),
            );
        }),
        tap({
          next: () => {
            this.store.dispatch(
              StateLibBusinessCasePageActions.refreshBusinessCaseContext(),
            );
            this.finToastService.show(Toast.success());
          },
          error: () => {
            this.finToastService.show(Toast.error());
          },
        }),
      )
      .subscribe();
  }

  combineInvitationLabel() {
    return this.access?.businessCase.canApplyForBusinessCase &&
      not(this.receivedInvitation)
      ? $localize`:@@sectionApplication.header.label.passingCase:Bewerbung`
      : $localize`:@@sectionInvitation.label:Einladung`;
  }

  onStatusChange(statusValue: string) {
    this.statusChanged.emit(statusValue);
  }

  onCloseCase() {
    this.closeCase.emit();
  }

  onReactivateCase() {
    this.reactivateCase.emit();
  }
}
