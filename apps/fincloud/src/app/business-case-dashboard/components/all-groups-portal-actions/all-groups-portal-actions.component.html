<ui-tooltip
  placement="bottom"
  padding="small"
  class="hoverable"
  [text]="
    isCustomerRealEstate
      ? portalActionsTitles.realEstate
      : portalActionsTitles.regular
  "
  [closeDelay]="0"
  [openDelay]="400"
>
  <ui-actions-menu
    [optionsTemplate]="options"
    [hasInteractionState]="true"
    [hideArrow]="true"
    [showMenuBottom]="true"
    iconColor="subtle"
    iconSize="medium-large"
    [hideDotsTrigger]="true"
    class="group-actions"
  >
    <ui-icon name="svgApartment" size="medium"></ui-icon>
    <ng-template #options>
      <ng-template [ngxPermissionsOnly]="[businessCasePermission.BCP_00020]">
        <div class="action-group-title">
          {{
            isCustomerRealEstate
              ? portalActionsOptionsMessages.realEstate
              : portalActionsOptionsMessages.regular
          }}
        </div>
        <ui-actions-menu-item
          class="action-group-menu-item"
          iconName="svgRequestFieldAction"
          iconSize="medium"
          (clicked)="onRequestGroupsFields()"
          [disabled]="!groupActionsAvailability.canRequestGroupFields"
          label="Informationen anfordern"
          i18n-label="@@allGroupsPortalActions.label.requestData"
        ></ui-actions-menu-item>
        <ui-actions-menu-item
          class="action-group-menu-item"
          iconName="svgDiscardFieldRequestAction"
          iconSize="medium"
          (clicked)="onWithdrawRequestGroupsFields()"
          [disabled]="!groupActionsAvailability.canWithdrawRequestsGroupFields"
          label="Zurückziehen"
          i18n-label="@@allGroupsPortalActions.label.withdraw"
        ></ui-actions-menu-item>
        <ui-actions-menu-item
          class="action-group-menu-item"
          iconName="svgReRequestFieldAction"
          iconSize="medium"
          (clicked)="onReRequestGroupsFields()"
          [disabled]="!groupActionsAvailability.canReRequestGroupFields"
          label="Informationen nochmals anfordern"
          i18n-label="@@allGroupsPortalActions.label.requestDataAgain"
        ></ui-actions-menu-item>
      </ng-template>
      <ng-template [ngxPermissionsOnly]="[businessCasePermission.BCP_00019]">
        <div class="action-group-title">
          {{
            isCustomerRealEstate
              ? portalGroupActionsOptionsMessages.realEstate
              : portalGroupActionsOptionsMessages.regular
          }}
        </div>
        <div class="visibility-actions">
          <div
            class="action"
            (click)="onTurnGroupsFieldsVisibilityOn()"
            [class.disabled]="!groupActionsAvailability.canTurnVisibilityOn"
          >
            <ui-icon
              name="svgVisibilityPortal"
              size="medium"
              color="dark"
            ></ui-icon>
            <div
              class="label"
              i18n="
                @@dashboard.businessCase.dataRoom.groupPortalActions.visibility"
            >
              Sichtbar
            </div>
          </div>
          <div
            class="action"
            (click)="onTurnGroupsFieldsVisibilityOff()"
            [class.disabled]="!groupActionsAvailability.canTurnVisibilityOff"
          >
            <ui-icon
              name="svgVisibilityPortalOff"
              size="medium"
              color="dark"
            ></ui-icon>
            <div
              class="label"
              i18n="
                @@dashboard.businessCase.dataRoom.groupPortalActions.visibility.notVisible"
            >
              Nicht sichtbar
            </div>
          </div>
        </div>
      </ng-template>
    </ng-template>
  </ui-actions-menu>
</ui-tooltip>
