import { Injectable } from '@angular/core';
import {
  CompanyControllerService,
  InformationControllerService,
} from '@fincloud/swagger-generator/company';
import {
  Company,
  CompanyInformation,
} from '@fincloud/swagger-generator/exchange';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import {
  Subscription,
  catchError,
  debounceTime,
  forkJoin,
  map,
  of,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs';

import { SocketService, SocketType } from '@fincloud/core/socket';
import {
  StateLibBusinessCaseApiActions,
  StateLibBusinessCasePageActions,
} from '@fincloud/state/business-case';
import { selectUserCustomerKey } from '@fincloud/state/user';
import { StateLibNoopPageActions } from '@fincloud/state/utils';
import { AppState } from '@fincloud/types/models';
import { PORTAL_SUBSCRIPTION_DESTINATION } from '@fincloud/utils';
import { concatLatestFrom } from '@ngrx/operators';
import { CompanyPageActions } from '../actions';

@Injectable()
export class CompanyEffects {
  cadrMessageSubscription: Subscription;

  hookupCompanySocket$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibBusinessCaseApiActions.loadBusinessCaseSuccess),
      // TODO: Move to a side-effect
      tap(() => {
        this.socketService.initializeSocket(SocketType.CADR);
      }),
      switchMap(({ payload }) =>
        this.socketService
          .getMessagesByDestination$<{
            body: string;
          }>(PORTAL_SUBSCRIPTION_DESTINATION, SocketType.PORTAL)
          .pipe(
            map((subscriptionMessage: { body: string }) => {
              if (subscriptionMessage.body === payload?.company?.id) {
                return CompanyPageActions.reloadBusinessCaseCompany({
                  payload: payload?.company?.id,
                });
              }
              return StateLibNoopPageActions.noop();
            }),
            takeUntil(
              this.actions$.pipe(
                ofType(StateLibBusinessCasePageActions.clearBusinessCase),
              ),
            ),
          ),
      ),
    ),
  );

  reloadCompany$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CompanyPageActions.reloadBusinessCaseCompany),
      debounceTime(1000),
      switchMap((action) => {
        return forkJoin([
          this.companyService.getCompanyById({ id: action.payload }),
          this.informationService.getAllInformation({
            companyId: action.payload,
            includeDeleted: false,
          }),
        ]).pipe(
          map(([company, informationRec]) => {
            const exchangeCompany = {
              ...company,
              information: Object.values(
                informationRec || {},
              ) as CompanyInformation[],
            } as Company;

            return CompanyPageActions.setBusinessCaseCompany({
              payload: exchangeCompany,
            });
          }),
        );
      }),
    ),
  );

  loadCorrespondingCompanyForCase$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibBusinessCaseApiActions.loadBusinessCaseSuccess),
      concatLatestFrom(() => this.store.select(selectUserCustomerKey)),
      switchMap(([action, customerKey]) => {
        return this.companyService
          .getCorrespondingCompanyForCustomer1({
            originCompanyId: action.payload.company?.id,
            customerKey,
          })
          .pipe(
            map((userCorrespondingCompany) => {
              return CompanyPageActions.setUserCorrespondingCompany({
                payload: userCorrespondingCompany?.id,
              });
            }),
            catchError(() => of(StateLibNoopPageActions.noop())),
          );
      }),
    ),
  );

  destroyCadrMessagesSub$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibBusinessCasePageActions.clearBusinessCase),
      switchMap((_) => {
        this.cadrMessageSubscription?.unsubscribe();
        return of(StateLibNoopPageActions.noop());
      }),
    ),
  );

  constructor(
    private actions$: Actions,
    private companyService: CompanyControllerService,
    private informationService: InformationControllerService,
    private store: Store<AppState>,
    private socketService: SocketService,
  ) {}
}
