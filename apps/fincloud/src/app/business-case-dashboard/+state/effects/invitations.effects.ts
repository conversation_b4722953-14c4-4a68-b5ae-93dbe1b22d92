import { Injectable } from '@angular/core';
import {
  Invitation,
  InvitationControllerService,
} from '@fincloud/swagger-generator/application';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { groupBy, isEqual } from 'lodash-es';
import { iif, interval, of } from 'rxjs';
import {
  catchError,
  distinctUntilChanged,
  filter,
  map,
  switchMap,
  takeWhile,
} from 'rxjs/operators';

import { Router } from '@angular/router';
import { StateTransfer } from '@fincloud/core/business-case';
import {
  StateLibBusinessCaseApiActions,
  StateLibBusinessCasePageActions,
  StateLibInvitationsApiActions,
  StateLibInvitationsPageActions,
  selectBusinessCase,
  selectPendingInvitationIds,
  selectSelectedInvitation,
} from '@fincloud/state/business-case';
import { selectAllCustomerInvitations } from '@fincloud/state/invitation';
import { selectUserCustomerKey } from '@fincloud/state/user';
import { StateLibNoopPageActions } from '@fincloud/state/utils';
import {
  BusinessCasePermission,
  InvitationStatus,
} from '@fincloud/types/enums';
import { AppState } from '@fincloud/types/models';
import { concatLatestFrom } from '@ngrx/operators';
import { NgxPermissionsService } from 'ngx-permissions';
import { POLLING_INVITATION_INTERVAL } from '../../utils/polling-invitation-interval';
import { InvitationsApiActions } from '../actions';

@Injectable()
export class InvitationsEffects {
  loadInvitationsOnBusinessCaseLoaded$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibBusinessCaseApiActions.loadBusinessCaseSuccess),
      concatLatestFrom(() => [this.store.select(selectAllCustomerInvitations)]),
      switchMap(([action, invitations]) => {
        return iif(
          () =>
            !this.ngxPermissionsService.hasPermission(
              BusinessCasePermission.BCP_00025,
            ),
          of(invitations).pipe(
            catchError(() => of([])),
            map((loadedInvitations) => {
              const invitationForCurrentBusinessCase = loadedInvitations.filter(
                (invitation) => invitation.businessCaseId === action.payload.id,
              );

              return StateLibInvitationsApiActions.loadInvitationsSuccess({
                payload: invitationForCurrentBusinessCase,
              });
            }),
          ),
          this.invitationControllerService
            .getAllInvitationsForBusinessCase({
              businessCaseId: action.payload.id,
            })
            .pipe(
              catchError(() => of([])),
              map((loadedInvitations) => {
                const sortInvitationsByCreationDateDesc = (
                  invitations: Invitation[],
                ) => {
                  return [...invitations].sort(
                    (i1: Invitation, i2: Invitation) =>
                      new Date(i2.creationDate).valueOf() -
                      new Date(i1.creationDate).valueOf(),
                  );
                };
                const latestInvitations = Object.values(
                  groupBy(loadedInvitations, 'invitedCustomerKey'),
                ).map((invitations) => ({
                  ...(sortInvitationsByCreationDateDesc(invitations)[0] ||
                    ({} as Invitation)),
                }));

                return StateLibInvitationsApiActions.loadInvitationsSuccess({
                  payload: latestInvitations,
                });
              }),
            ),
        );
      }),
    ),
  );

  fetchSelectedInvitation$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibInvitationsPageActions.startInvitationPolling),
      distinctUntilChanged(),
      switchMap((startInvitationPolling) => {
        return iif(
          () => !startInvitationPolling.payload,
          of(StateLibNoopPageActions.noop),
          interval(POLLING_INVITATION_INTERVAL).pipe(
            concatLatestFrom(() => [
              this.store.select(selectSelectedInvitation),
              this.store
                .select(selectBusinessCase)
                .pipe(filter((businessCase) => !!businessCase)),
              this.store.select(selectUserCustomerKey),
            ]),
            takeWhile(
              ([_, incomingInvitation]) =>
                !!incomingInvitation && !incomingInvitation.applicationId,
            ),
            switchMap(
              ([_, selectedInvitation, businessCase, userCustomerKey]) => {
                return this.invitationControllerService
                  .getInvitation({
                    id: selectedInvitation.id,
                    businessCaseId: businessCase?.id,
                  })
                  .pipe(
                    filter(
                      (incomingInvitation) =>
                        !isEqual(selectedInvitation, incomingInvitation),
                    ),
                    switchMap((invitation) => {
                      if (
                        (invitation.invitationStatus ===
                          InvitationStatus.CANCELED &&
                          businessCase.state === 'ACTIVE_PRIVATE') ||
                        invitation.invitationStatus ===
                          InvitationStatus.REMOVED_FROM_BUSINESS_CASE
                      ) {
                        this.router.navigate([
                          userCustomerKey,
                          'dashboard',
                          'applications-invitations',
                        ]);

                        return of(
                          StateLibBusinessCasePageActions.clearBusinessCase({
                            businessCaseId: businessCase.id,
                          }),
                        );
                      } else {
                        return of(
                          StateLibInvitationsPageActions.updateInvitation({
                            payload: invitation,
                          }),
                        );
                      }
                    }),
                    catchError(() => {
                      this.router.navigate([
                        userCustomerKey,
                        'dashboard',
                        'applications-invitations',
                      ]);
                      return of(
                        StateLibBusinessCasePageActions.clearBusinessCase({
                          businessCaseId: businessCase.id,
                        }),
                      );
                    }),
                  );
              },
            ),
          ),
        );
      }),
    ),
  );

  cancelPendingInvitations$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibBusinessCasePageActions.updateBusinessCaseState),
      concatLatestFrom(() => this.store.select(selectPendingInvitationIds)),
      filter(
        ([{ payload }, pendingInvitationsIds]) =>
          (payload.businessCaseState.includes(StateTransfer.CANCEL) ||
            payload.businessCaseState.includes(StateTransfer.COMPLETE)) &&
          pendingInvitationsIds.length > 0,
      ),
      switchMap(([action, pendingInvitationsIds]) =>
        this.invitationControllerService
          .cancelInvitations({
            body: pendingInvitationsIds,
            businessCaseId: action.payload.id,
          })
          .pipe(
            map((response) => {
              if (response.failed.length) {
                return InvitationsApiActions.clearPendingInvitationsFailure({
                  payload: response.failed,
                });
              }
              return InvitationsApiActions.clearPendingInvitationsSuccess({
                payload: response.successful,
              });
            }),
            catchError((err) =>
              of(
                InvitationsApiActions.clearPendingInvitationsFailure({
                  payload: err,
                }),
              ),
            ),
          ),
      ),
    ),
  );
  constructor(
    private actions$: Actions,
    private invitationControllerService: InvitationControllerService,
    private store: Store<AppState>,
    private router: Router,
    private ngxPermissionsService: NgxPermissionsService,
  ) {}
}
