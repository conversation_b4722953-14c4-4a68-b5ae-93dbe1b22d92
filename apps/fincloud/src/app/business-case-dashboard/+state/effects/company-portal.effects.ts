import { Injectable } from '@angular/core';
import {
  CaseFieldAccess,
  CaseFieldAccessControllerService,
  CaseFieldInputRequest,
  CaseFieldInputRequestControllerService,
} from '@fincloud/swagger-generator/portal';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { catchError, filter, map, switchMap } from 'rxjs/operators';

import { FieldInputRequestState } from '@fincloud/core/business-case';
import {
  PortalSocketMessage,
  PortalSocketMessageType,
  SocketService,
  SocketType,
} from '@fincloud/core/socket';
import { selectAccessRights } from '@fincloud/state/access';
import { selectCustomerBusinessCaseContext } from '@fincloud/state/business-case';

import { StateLibCompanyPortalPageActions } from '@fincloud/state/company-portal';
import { selectUserCustomerKey, selectUserId } from '@fincloud/state/user';
import { ParticipationType } from '@fincloud/types/enums';
import { AccessRights, AppState } from '@fincloud/types/models';
import { of } from 'rxjs';

import {
  StateLibBusinessCasePageActions,
  StateLibContextApiActions,
  selectCaseFieldsAccess,
  selectCaseFieldsInputsRequests,
} from '@fincloud/state/business-case';
import { StateLibNoopPageActions } from '@fincloud/state/utils';
import { PORTAL_SUBSCRIPTION_DESTINATION } from '@fincloud/utils';
import { concatLatestFrom } from '@ngrx/operators';
import { CompanyPortalApiActions, CompanyPortalPageActions } from '../actions';

@Injectable()
export class CompanyPortalEffects {
  loadCaseFieldsInputsRequests$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibContextApiActions.loadCustomerBusinessCaseContextSuccess),
      concatLatestFrom(() => [
        this.store.select(selectUserCustomerKey),
        this.store.select(selectUserId),
      ]),
      switchMap(([action, customerKey, userId]) => {
        const caseId = action.payload.businessCaseId;
        const isParticipantLead =
          action.payload.participationType === ParticipationType.LEADER;

        const req$ = isParticipantLead
          ? this.caseFieldInputRequestService
              .getAllByCaseId1({
                caseId,
              })
              .pipe(catchError(() => of([] as CaseFieldInputRequest[])))
          : this.getCaseFieldsInputRequests(caseId, customerKey);

        return req$.pipe(
          map((fieldsInputsRequests) => {
            if (isParticipantLead) {
              return CompanyPortalPageActions.setCaseFieldsInputsRequests({
                payload: fieldsInputsRequests,
              });
            }
            return CompanyPortalPageActions.setCaseFieldsInputsRequests({
              payload: fieldsInputsRequests.filter((fieldsInputsRequest) =>
                fieldsInputsRequest.participantUsersToBeNotified.includes(
                  userId,
                ),
              ),
            });
          }),
        );
      }),
    ),
  );

  loadCaseFieldsAccess$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CompanyPortalPageActions.loadCaseFieldsAccess),
      concatLatestFrom(() => [
        this.store.select(selectUserCustomerKey),
        this.store.select(selectCustomerBusinessCaseContext),
      ]),
      filter(([, , caseContext]) => !!caseContext),
      switchMap(([action, customerKey, caseContext]) => {
        const caseId = action.payload;
        const isParticipantLead =
          caseContext.participationType === ParticipationType.LEADER;
        const req$ = isParticipantLead
          ? this.caseFieldAccessService
              .getAllByCaseId2({
                caseId,
              })
              .pipe(catchError(() => of([])))
          : this.getCaseFieldsAccess(caseId, customerKey);

        return req$.pipe(
          map((fieldsAccess) => {
            return CompanyPortalPageActions.setCaseFieldsAccess({
              payload: fieldsAccess,
            });
          }),
        );
      }),
      catchError(() => {
        return of(CompanyPortalApiActions.loadCaseFieldsAccessFailure());
      }),
    ),
  );

  manageFieldAccess$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CompanyPortalPageActions.manageCaseFieldAccess),
      concatLatestFrom(() => [
        this.store.select(selectCaseFieldsAccess),
        this.store.select(selectAccessRights),
        this.store.select(selectUserCustomerKey),
      ]),
      map(([action, fieldsAccess, accessRights, userCustomerKey]) => {
        if (
          this.isPortalActionNotForYou(
            accessRights,
            action.payload.customerKey,
            userCustomerKey,
          )
        ) {
          return StateLibNoopPageActions.noop();
        }

        const existingFieldAccess = fieldsAccess.find(
          (fa) => fa.fieldKey === action.payload.fieldKey,
        );

        return existingFieldAccess
          ? CompanyPortalPageActions.updateCaseFieldAccess({
              payload: action.payload,
            })
          : CompanyPortalPageActions.addCaseFieldAccess({
              payload: action.payload,
            });
      }),
    ),
  );

  fieldAccessAdded$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        CompanyPortalPageActions.addCaseFieldAccess,
        CompanyPortalPageActions.updateCaseFieldAccess,
      ),
      map((caseFieldAccess) =>
        StateLibBusinessCasePageActions.loadBusinessCase({
          payload: caseFieldAccess.payload.caseId,
        }),
      ),
    ),
  );

  manageFieldInputRequest$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CompanyPortalPageActions.manageCaseFieldInputRequest),
      concatLatestFrom(() => [
        this.store.select(selectCaseFieldsInputsRequests),
        this.store.select(selectUserCustomerKey),
        this.store.select(selectAccessRights),
      ]),
      map(([action, inputsRequests, userCustomerKey, accessRights]) => {
        if (
          this.isPortalActionNotForYou(
            accessRights,
            action.payload.customerKey,
            userCustomerKey,
          )
        ) {
          return StateLibNoopPageActions.noop();
        }

        if (
          action.payload.state === FieldInputRequestState.CLOSED &&
          action.payload.requestType === 'CUSTOMER'
        ) {
          // Return visibility state back to not visible
          const fieldAccess = {
            caseId: action.payload.caseId,
            requestType: action.payload.requestType,
            fieldKey: action.payload.fieldKey,
            customerKey: action.payload.customerKey,
            active: false,
          } as CaseFieldAccess;
          this.store.dispatch(
            StateLibCompanyPortalPageActions.updateCaseFieldAccessSocket({
              payload: fieldAccess,
            }),
          );

          return CompanyPortalPageActions.updateCaseFieldAccess({
            payload: fieldAccess,
          });
        }

        const existingFieldInputRequest = inputsRequests.find(
          (ir) => ir.fieldKey === action.payload.fieldKey,
        );

        return existingFieldInputRequest
          ? CompanyPortalPageActions.updateCaseFieldInputRequest({
              payload: action.payload,
            })
          : CompanyPortalPageActions.addCaseFieldInputRequest({
              payload: action.payload,
            });
      }),
    ),
  );

  manageSocketFieldAccess$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibCompanyPortalPageActions.updateCaseFieldAccessSocket),
      concatLatestFrom(() => this.store.select(selectUserId)),
      map(([action, userIdMakingTheChange]) => {
        const message = {
          type: PortalSocketMessageType.FIELD_ACCESS,
          payload: action.payload as CaseFieldAccess,
          userIdMakingTheChange,
        } as PortalSocketMessage;

        this.socketService.sendMessage(
          message,
          PORTAL_SUBSCRIPTION_DESTINATION,
          SocketType.PORTAL,
        );

        return CompanyPortalApiActions.companyPortalFieldManagedSuccess();
      }),
    ),
  );

  manageSocketFieldInputRequest$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibBusinessCasePageActions.updateCaseFieldInputRequestSocket),
      concatLatestFrom(() => this.store.select(selectUserId)),
      map(([action, creatorUserId]) => {
        const message = {
          type: PortalSocketMessageType.REQUEST_INFORMATION,
          payload: {
            ...action.payload,
            creatorUserId,
          } as CaseFieldInputRequest,
          userIdMakingTheChange: creatorUserId,
        } as PortalSocketMessage;

        this.socketService.sendMessage(
          message,
          PORTAL_SUBSCRIPTION_DESTINATION,
          SocketType.PORTAL,
        );

        return CompanyPortalApiActions.companyPortalFieldManagedSuccess();
      }),
    ),
  );

  private getCaseFieldsAccess(caseId: string, customerKey: string) {
    return this.caseFieldAccessService
      .getAllByCaseIdAndRequestType1({
        caseId,
        requestType: 'CUSTOMER',
        customerKey: customerKey,
      })
      .pipe(
        catchError(() => {
          return of([]);
        }),
      );
  }

  private getCaseFieldsInputRequests(caseId: string, customerKey: string) {
    return this.caseFieldInputRequestService
      .getAllByCaseIdAndRequestType({
        caseId,
        requestType: 'CUSTOMER',
        customerKey: customerKey,
      })
      .pipe(catchError(() => of([] as CaseFieldInputRequest[])));
  }

  private isPortalActionNotForYou(
    accessRights: AccessRights,
    requestCustomerKey: string,
    customerKey: string,
  ) {
    return (
      requestCustomerKey !== customerKey &&
      !accessRights.granular.isEmployeeOfLeadCustomer
    );
  }

  constructor(
    private actions$: Actions,
    private caseFieldAccessService: CaseFieldAccessControllerService,
    private caseFieldInputRequestService: CaseFieldInputRequestControllerService,
    private store: Store<AppState>,
    private socketService: SocketService,
  ) {}
}
