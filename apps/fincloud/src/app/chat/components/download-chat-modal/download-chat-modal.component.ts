import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { DateService } from '@fincloud/core/date';
import { StateLibBusinessCaseChatPageActions } from '@fincloud/state/business-case';
import { StateLibChatHistoryRangeApiActions } from '@fincloud/state/chat';
import { NgbActiveModal, NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { selectCustomDateHasError } from '../../+state/selectors/chat.selectors';
import { ExportedChatSelection } from '../../enums/download-chat';
import { DownloadChatForm } from '../../models/download-chat-selection-form';
import { SelectedDownloadValue } from '../../models/download-chat-selection-value';
import { EXPORTED_CHAT_SELECTION_OPTIONS } from '../../utils/donwload-chat-options';
import { DOWNLOAD_CHAT_REQUEST_MAP } from '../../utils/download-chat-request-map';

@Component({
  selector: 'app-download-chat-modal',
  templateUrl: './download-chat-modal.component.html',
  styleUrls: ['./download-chat-modal.component.scss'],
})
export class DownloadChatModalComponent implements OnInit {
  @Input() minAllowedDateInput: NgbDateStruct;
  @Input() maxAllowedDateInput: NgbDateStruct;
  @Input() locale: string;
  cancelLabel = $localize`:@@button.label.cancel:Abbrechen`;
  confirmLabel = $localize`:@@chat.exportChat.modal.downloadPdf:PDF herunterladen`;
  optionLabel = $localize`:@@contractManagement.exportChat:Chat exportieren`;
  fromDateLabel = $localize`:@@dateRangeField.label.from:Von`;
  toDateLabel = $localize`:@@chat.exportChat.modal.to:Bis`;
  headerTitle = $localize`:@@contractManagement.exportChat:Chat exportieren`;
  customDateError = $localize`:@@chat.exportChat.modal.warning:Für den ausgewählten Zeitrahmen gibt es keine Nachrichten.`;
  formGroup: FormGroup<DownloadChatForm>;
  hasError$ = this.store.select(selectCustomDateHasError);
  isDateReadOnly = true;
  selectionOptions = [
    {
      value: EXPORTED_CHAT_SELECTION_OPTIONS.FromTheBeginning,
      disabled: false,
    },
    { value: EXPORTED_CHAT_SELECTION_OPTIONS.LastMonth, disabled: false },
    { value: EXPORTED_CHAT_SELECTION_OPTIONS.LastYear, disabled: false },
    { value: EXPORTED_CHAT_SELECTION_OPTIONS.Custom, disabled: false },
  ];

  get isCustomTemplate(): boolean {
    return (
      this.currentSelectionField.value ===
      EXPORTED_CHAT_SELECTION_OPTIONS.Custom
    );
  }

  get currentSelectionField() {
    return this.formGroup.controls.selectedOption;
  }

  get fromDateFormField() {
    return this.formGroup.controls.fromDate;
  }

  get toDateFormField() {
    return this.formGroup.controls.toDate;
  }

  get fromDateFieldHasError(): boolean {
    return this.datePickerValidation(this.fromDateFormField);
  }

  get toDateFieldHasError(): boolean {
    return this.datePickerValidation(this.toDateFormField);
  }

  get minToDateField(): NgbDateStruct {
    if (this.fromDateFormField.valid) {
      return this.dateService.getFormattedTimeNgbDateStruct(
        this.fromDateFormField.value,
      );
    }

    return this.minAllowedDateInput;
  }

  get minDateFromField() {
    return this.dateService.formatNgbDateStruct(this.minAllowedDateInput);
  }
  get maxDateFromField() {
    if (this.toDateFormField.value && this.toDateFormField.valid) {
      return this.toDateFormField.value;
    }
    return this.dateService.formatNgbDateStruct(this.maxAllowedDateInput);
  }

  get minDateToField() {
    if (this.fromDateFormField.value && this.fromDateFormField.valid) {
      return this.fromDateFormField.value;
    }

    return this.dateService.formatNgbDateStruct(this.minAllowedDateInput);
  }
  get maxDateToField() {
    return this.dateService.formatNgbDateStruct(this.maxAllowedDateInput);
  }

  constructor(
    public activeModal: NgbActiveModal,
    private dateService: DateService,
    private store: Store,
  ) {}

  ngOnInit(): void {
    this.formGroup = new FormGroup({
      selectedOption: new FormControl(
        EXPORTED_CHAT_SELECTION_OPTIONS.FromTheBeginning,
      ),
      fromDate: new FormControl('', Validators.required),
      toDate: new FormControl('', Validators.required),
    });

    this.checkIfLastMonthAndYearAreDisable();
  }

  onConfirm() {
    const selectedValue =
      DOWNLOAD_CHAT_REQUEST_MAP[this.currentSelectionField.value];
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    const data = {
      selectedValue,
      fromDate: this.fromDateFormField.value,
      toDate: this.toDateFormField.value,
      timezone,
    } as SelectedDownloadValue;

    if (selectedValue === ExportedChatSelection.CUSTOM) {
      this.formGroup.markAllAsTouched();
      if (this.formGroup.valid) {
        const payload = {
          selectedEndDate: this.dateService.getCalendarEndDate(data.toDate),
          selectedStartDate: this.dateService.getCalendarStartDate(
            data.fromDate,
          ),
          locale: this.locale,
          timeZone: data.timezone,
        };

        this.store.dispatch(
          StateLibBusinessCaseChatPageActions.setChatExportCustom({
            payload,
          }),
        );
      }
      return;
    }

    this.removeFromAndToValidation();
    this.formGroup.markAllAsTouched();
    this.handleDownloadChat(data);
  }

  selectionChange(selectionLabel: string) {
    this.isDateReadOnly =
      selectionLabel !== EXPORTED_CHAT_SELECTION_OPTIONS.Custom;

    this.fromDateFormField.reset();
    this.toDateFormField.reset();
    this.clearCustomError();
  }

  private datePickerValidation(formControl: FormControl) {
    return !formControl.valid && this.isCustomTemplate && formControl.touched;
  }

  private checkIfLastMonthAndYearAreDisable() {
    const today = this.dateService.getFormattedTimeNgbDateStruct(
      new Date().toISOString(),
    );
    const isLastMonthDisable = !this.dateService.isDateInRange(
      today,
      this.maxAllowedDateInput,
      1,
    );
    const isLastYearDisable = !this.dateService.isDateInRange(
      today,
      this.maxAllowedDateInput,
      12,
    );
    this.selectionOptions[1] = {
      ...this.selectionOptions[1],
      disabled: isLastMonthDisable,
    };
    this.selectionOptions[2] = {
      ...this.selectionOptions[2],
      disabled: isLastYearDisable,
    };
  }

  closeDialog() {
    this.activeModal.close({
      success: false,
    });
  }

  clearCustomError() {
    this.store.dispatch(
      StateLibChatHistoryRangeApiActions.clearCustomSelectedChatFailure(),
    );
  }

  removeFromAndToValidation() {
    this.fromDateFormField.clearValidators();
    this.fromDateFormField.updateValueAndValidity();
    this.toDateFormField.clearValidators();
    this.toDateFormField.updateValueAndValidity();
  }

  handleDownloadChat(selection: SelectedDownloadValue): void {
    const todayDate = encodeURIComponent(
      this.dateService.getTodayDateISO8601(),
    );
    const dateRange = this.getDateRange(selection, todayDate);
    const basicDownloadParams = {
      locale: this.locale,
      timeZone: selection.timezone,
      ...dateRange,
    };
    this.store.dispatch(
      StateLibBusinessCaseChatPageActions.setChatExport({
        payload: basicDownloadParams,
      }),
    );
  }

  private getDateRange(
    selection: SelectedDownloadValue,
    todayDate: string,
  ): { selectedStartDate: string; selectedEndDate: string } {
    const dateRangeMapper = {
      [ExportedChatSelection.TAKE_ALL]: () => ({
        selectedStartDate: '',
        selectedEndDate: '',
      }),
      [ExportedChatSelection.LAST_MONTH]: () => ({
        selectedStartDate: encodeURIComponent(
          this.dateService.getLastMonthDateISO8601(),
        ),
        selectedEndDate: todayDate,
      }),
      [ExportedChatSelection.LAST_YEAR]: () => ({
        selectedStartDate: encodeURIComponent(
          this.dateService.getLastYearDateISO8601(),
        ),
        selectedEndDate: todayDate,
      }),
      [ExportedChatSelection.CUSTOM]: () => ({
        selectedStartDate: encodeURIComponent(
          this.dateService.getCalendarStartDate(selection.fromDate),
        ),
        selectedEndDate: encodeURIComponent(
          this.dateService.getCalendarEndDate(selection.toDate),
        ),
      }),
    };

    return (
      dateRangeMapper[selection.selectedValue]() ?? {
        selectedStartDate: '',
        selectedEndDate: '',
      }
    );
  }
}
