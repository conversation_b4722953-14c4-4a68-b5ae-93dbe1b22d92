import { Location } from '@angular/common';
import {
  Component,
  DestroyRef,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import {
  Chat,
  ChatAccessPromptControllerService,
  ChatManagementControllerService,
  ChatMessage,
  ChatNotificationStatusControllerService,
} from '@fincloud/swagger-generator/communication';
import {
  BusinessCaseInformation,
  ExchangeBusinessCase,
  TemplateField,
} from '@fincloud/swagger-generator/exchange';
import {
  UserCase,
  UserCaseControllerService,
} from '@fincloud/swagger-generator/portal';

import { ModalService } from '@fincloud/core/modal';
import { SocketService, SocketType } from '@fincloud/core/socket';
import { Toast } from '@fincloud/core/toast';
import { TabNavigationService } from '@fincloud/neoshare/services';
import { selectAccessRights } from '@fincloud/state/access';
import {
  selectActiveParticipants,
  selectBusinessCase,
  selectBusinessCaseParticipantsPermissions,
  selectCustomerBusinessCaseContext,
  selectCustomerNamesByKey,
  selectFacilities,
  selectHasAnyBusinessCasePermission,
  selectHasBusinessCasePermission,
  selectUsersById,
} from '@fincloud/state/business-case';
import {
  StateLibChatPageActions,
  selectExistingChats,
} from '@fincloud/state/chat';
import {
  Customer,
  User,
  UserManagementControllerService,
} from '@fincloud/swagger-generator/authorization-server';
import {
  ChatTab,
  ChatType,
  ParticipationType,
  UserRole,
  UserState,
} from '@fincloud/types/enums';
import {
  AccessRights,
  AppState,
  Dictionary,
  FacilityFieldViewModel,
  UserToken,
} from '@fincloud/types/models';
import { NgbNav, NgbNavChangeEvent } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { isEmpty, isEqual, uniq } from 'lodash-es';
import { NgxPermissionsService } from 'ngx-permissions';
import {
  BehaviorSubject,
  Subject,
  catchError,
  combineLatest,
  concat,
  filter,
  forkJoin,
  map,
  merge,
  of,
  switchMap,
  take,
  takeLast,
  withLatestFrom,
} from 'rxjs';
import { v4 as uuidv4 } from 'uuid';
import { selectMutedChatIds } from '../../+state/selectors/chat.selectors';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  AuthenticationService,
  TokenManagementService,
} from '@fincloud/core/auth';
import {
  BILATERAL_CHAT_GROUP_LABEL,
  CATEGORY_CHAT_GROUP_LABEL,
  INTERNAL_CHAT_GROUP_LABEL,
  SELECTION_CHAT_LABEL,
  TOPIC_CHAT_GROUP_LABEL,
} from '@fincloud/neoshare/chat';
import { selectRouteCustomerKey } from '@fincloud/state/router';
import { BusinessCasePermission } from '@fincloud/types/enums';
import { FinToastService } from '@fincloud/ui/toast';
import { CHAT_SOCKET_RECEIVE_MESSAGE_DESTINATION } from '@fincloud/utils';
import { ChatRepresentation } from '../../models/chat-representation';
import { CustomerUserDtoExtended } from '../../models/customer-user-dto-extended';
import { ThemedChatGroup } from '../../models/themed-chat-group';
import { BUSINESS_CASE_INACTIVE_COMPLETED_STATES } from '../../utils/business-case-inactive-completed-states';
import { AddNewCustomerToChatsComponent } from '../add-new-customer-to-chats/add-new-customer-to-chats.component';
import { BilateralModalComponent } from '../bilateral-modal/bilateral-modal.component';
import { InternalBilateralModalComponent } from '../internal-bilateral-modal/internal-bilateral-modal.component';
import { ThemedModalComponent } from '../themed-modal/themed-modal.component';

@Component({
  selector: 'app-chat-dashboard',
  templateUrl: './chat-dashboard.component.html',
  styleUrls: ['./chat-dashboard.component.scss'],
})
export class ChatDashboardComponent implements OnInit, OnDestroy {
  @ViewChild('nav') nav: NgbNav;

  user: UserToken;
  businessCase: ExchangeBusinessCase;
  activeId: ChatTab;
  internalChatGroupLabel = INTERNAL_CHAT_GROUP_LABEL;
  selectionChatLabel = SELECTION_CHAT_LABEL;
  bilateralChatGroupLabel = BILATERAL_CHAT_GROUP_LABEL;
  topicChatGroupLabel = TOPIC_CHAT_GROUP_LABEL;
  categoryChatGroupLabel = CATEGORY_CHAT_GROUP_LABEL;

  chatType: ChatTab;
  chatId: string;

  customerName: string;

  private _userCustomerKey: string;
  userId: string;
  customerNamesByKey: Dictionary<Customer>;

  access: AccessRights;

  bilateralChats: ChatRepresentation[];
  topicChats: ChatRepresentation[];
  businessCaseChat: ChatRepresentation;
  customerInternalChat: ChatRepresentation;
  internalChats: ChatRepresentation[];

  archivedChats: Chat[] = [];
  allActiveChats: Chat[] = [];
  chatsInitial: Chat[];
  chatConstructingFinished: boolean;

  mutedChatIds: string[] = [];
  _hasChatWithAllCustomers: BehaviorSubject<boolean> = new BehaviorSubject(
    true,
  );
  isConstructingInProgress: boolean;

  usersById: Dictionary<User>;

  usersLoaded: boolean;
  _isSingleCustomerUser: BehaviorSubject<boolean> = new BehaviorSubject(true);
  usersAvailable: CustomerUserDtoExtended[];

  caseAssignedUsers: User[];
  assignedUsersCases: UserCase[];

  readonly businessCasePermission = BusinessCasePermission;

  isParticipantVisibilityOn$ = combineLatest([
    this.store.select(selectBusinessCaseParticipantsPermissions),
    this.store.select(
      selectHasBusinessCasePermission(BusinessCasePermission.BCP_00131),
    ),
  ]).pipe(
    takeUntilDestroyed(this.destroyRef),
    map(([participantsPermissions, hasPermission]) => {
      return (
        hasPermission &&
        (Object.values(participantsPermissions || {}).filter((permEntity) =>
          permEntity.permissions?.includes(BusinessCasePermission.BCP_00131),
        )?.length >= 1 ||
          !Object.values(participantsPermissions || {}).length)
      );
    }),
  );

  canCreateInternalChat$ = combineLatest([
    this._isSingleCustomerUser.asObservable(),
    this.store.select(
      selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00041]),
    ),
  ]).pipe(
    map(([isSingleCustomerUser, hasPermission]) => {
      return (
        hasPermission &&
        !isSingleCustomerUser &&
        !this.isBusinessCaseInactiveOrCompleted
      );
    }),
  );

  canCreateBilateralChat$ = combineLatest([
    this._hasChatWithAllCustomers.asObservable(),
    this.store.select(
      selectHasBusinessCasePermission(BusinessCasePermission.BCP_00046),
    ),
    this.store.select(
      selectHasBusinessCasePermission(BusinessCasePermission.BCP_00045),
    ),
  ]).pipe(
    takeUntilDestroyed(this.destroyRef),
    map(
      ([hasChatWithAllCustomers, hasPermission, hasPermissionCollaborator]) => {
        return (
          (hasPermission || hasPermissionCollaborator) &&
          !hasChatWithAllCustomers &&
          !this.isBusinessCaseInactiveOrCompleted
        );
      },
    ),
  );

  canCreateTopicChat$ = combineLatest([
    this.store.select(
      selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00052]),
    ),
    this.isParticipantVisibilityOn$,
  ]).pipe(
    takeUntilDestroyed(this.destroyRef),
    map(([hasPermission, isParticipantVisibilityOn]) => {
      return (
        hasPermission &&
        !this.isBusinessCaseInactiveOrCompleted &&
        isParticipantVisibilityOn
      );
    }),
  );

  canSeeMainChat$ = combineLatest([
    this.store.select(
      selectHasBusinessCasePermission(BusinessCasePermission.BCP_00049),
    ),
    this.store.select(
      selectHasBusinessCasePermission(BusinessCasePermission.BCP_00131),
    ),
  ]).pipe(
    takeUntilDestroyed(this.destroyRef),
    map(([hasPermission, hasVisibilityPermission]) => {
      return hasPermission && hasVisibilityPermission;
    }),
  );

  private destroyReceiveMessages$$ = new Subject<boolean>();
  private themedModalOpened: boolean;

  constructor(
    private destroyRef: DestroyRef,
    private store: Store<AppState>,
    private route: ActivatedRoute,
    private navService: TabNavigationService,
    private modalService: ModalService,
    private chatManagementController: ChatManagementControllerService,
    private location: Location,
    private router: Router,
    private finToastService: FinToastService,
    private chatAccessPromptService: ChatAccessPromptControllerService,
    private socketService: SocketService,
    private chatNotificationControllerService: ChatNotificationStatusControllerService,
    private userManagementControllerService: UserManagementControllerService,
    private userCaseControllerService: UserCaseControllerService,
    private ngxPermissionsService: NgxPermissionsService,
    private tokenManagementService: TokenManagementService,
    private authService: AuthenticationService,
  ) {}

  get disableSendMessages() {
    return (
      this.isBusinessCaseInactiveOrCompleted ||
      (this.chatType === 'business-case' &&
        !this.ngxPermissionsService.getPermission(
          BusinessCasePermission.BCP_00050,
        )) ||
      (this.currentChatSelected?.chatType === ChatType.INTERNAL &&
        !this.ngxPermissionsService.getPermission(
          BusinessCasePermission.BCP_00040,
        )) ||
      ((this.currentChatSelected?.chatType === ChatType.INTERNAL_BILATERAL ||
        this.currentChatSelected?.chatType === ChatType.INTERNAL_GROUP) &&
        !this.ngxPermissionsService.getPermission(
          BusinessCasePermission.BCP_00042,
        )) ||
      // Bilateral
      (this.currentChatSelected?.chatType === ChatType.ONE_ON_ONE &&
        (this.currentChatSelected.chatCustomers?.some(
          (customer) =>
            customer.customerKey === this.businessCase.leadCustomerKey,
        )
          ? !this.ngxPermissionsService.getPermission(
              BusinessCasePermission.BCP_00047,
            )
          : !this.ngxPermissionsService.getPermission(
              BusinessCasePermission.BCP_00048,
            ))) ||
      (this.currentChatSelected?.chatType === ChatType.ON_TOPIC &&
        !this.ngxPermissionsService.getPermission(
          BusinessCasePermission.BCP_00054,
        )) ||
      (this.currentChatSelected?.chatType === ChatType.CONSORTIUM &&
        !this.ngxPermissionsService.getPermission(
          BusinessCasePermission.BCP_00050,
        ))
    );
  }

  get isBusinessCaseInactiveOrCompleted() {
    return BUSINESS_CASE_INACTIVE_COMPLETED_STATES.includes(
      this.businessCase?.state,
    );
  }

  get currentChatSelected() {
    return [...this.allActiveChats, ...this.archivedChats]?.find(
      (chat) => chat.id === this.chatId,
    );
  }

  ngOnInit(): void {
    this.receiveMessages();
    this.router.events
      .pipe(
        filter((e) => e instanceof NavigationEnd),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((ev) => {
        const urlFragments = (ev as NavigationEnd).urlAfterRedirects.split('/');
        this.chatId = urlFragments.pop();
      });

    this.route.firstChild?.url
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((url) => {
        const lastUrlSegment = this.location.path().split('/').pop();
        // TODO: Refactor; it works for now
        this.chatType = url[0].path as ChatTab;

        const componentId = this.route.snapshot.queryParams
          .createComponentChat as string;
        if (!componentId) {
          this.chatId = lastUrlSegment !== this.chatType && lastUrlSegment;
          this.activeId = (
            this.chatId ? `${this.chatType}/${this.chatId}` : this.chatType
          ) as ChatTab;
        }
      });

    this.route.paramMap
      .pipe(
        switchMap((params) => {
          const businessCaseId = params.get('id');
          this.store.dispatch(
            StateLibChatPageActions.setInitialSelectedChatId({
              payload: { chatId: this.chatId },
            }),
          );
          return this.userCaseControllerService
            .getAllByCaseId({
              caseId: businessCaseId,
            })
            .pipe(
              catchError((err) => {
                console.error(err?.message);
                return of([]);
              }),
            );
        }),
      )
      .subscribe((usersCases) => (this.assignedUsersCases = usersCases));

    combineLatest([
      this.store.select(selectBusinessCase),
      this.store.select(selectCustomerNamesByKey),
      this.store.select(selectAccessRights),
      this.store.select(selectExistingChats).pipe(filter((chats) => !!chats)),
      this.store.select(selectMutedChatIds),
      this.store.select(selectUsersById),
      this.store.select(selectFacilities),
      this.store.select(selectRouteCustomerKey),
    ])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(
        ([
          businessCase,
          customers,
          accessRights,
          chats,
          mutedChatIds,
          usersById,
          facilities,
          customerKey,
        ]) => {
          const token =
            this.tokenManagementService.getToken(customerKey)?.decodedToken;
          const accessToken =
            this.tokenManagementService.getToken(customerKey)?.tokenRaw
              ?.accessToken;
          this.user = token;
          this.businessCase = businessCase;
          this._userCustomerKey = token.customer_key;
          this.userId = token.sub;
          this.access = accessRights;
          const componentId = this.route.snapshot.queryParams
            .createComponentChat as string;

          if (
            !isEqual(this.chatsInitial, chats) ||
            !isEqual(this.mutedChatIds, mutedChatIds) ||
            !isEqual(this.customerNamesByKey, customers) ||
            !isEqual(this.usersById, usersById)
          ) {
            this.chatsInitial = chats;
            this.mutedChatIds = mutedChatIds;
            this.customerNamesByKey = customers;
            this.usersById = usersById;
            this.caseAssignedUsers = this.assignedUsersCases?.map(
              (uc) => this.usersById[uc.userId],
            );

            this.constructTabChats(chats);
          }

          if (
            chats &&
            token &&
            !isEmpty(this.usersById) &&
            !this.usersLoaded &&
            this.businessCase
          ) {
            this.loadUsers(token.customer_key, token.sub);
          }

          const customerKeysFromBusinessCase = this.businessCase?.participants
            .map((p) => p.customerKey)
            .filter((key) => key !== this._userCustomerKey);

          this._hasChatWithAllCustomers.next(
            customerKeysFromBusinessCase?.every((key) =>
              this.oneOnOneChatAlreadyExists(this._userCustomerKey, key),
            ) ||
              (!this.access?.granular?.isEmployeeOfParticipatingCustomer &&
                this.oneOnOneChatAlreadyExists(
                  this._userCustomerKey,
                  this.businessCase?.leadCustomerKey,
                )),
          );

          if (this.chatType === 'business-case') {
            this.chatId = this.businessCaseChat?.id;
          }

          if (componentId && businessCase && !this.themedModalOpened) {
            const allInformations = Object.values(
              this.businessCase.information,
            ) as BusinessCaseInformation[];
            const information = allInformations.find(
              (i) => i.id === componentId,
            );
            if (information) {
              this.openThemedModal(false, information?.field, information);
            } else {
              const facilityInformation = componentId.split('|');
              const facilityName = facilityInformation[0];
              const facilityGroupName = facilityInformation[1];
              const faclitityFieldKey = facilityInformation[2];

              const facilityField = facilities
                .find((f) => f.name === facilityName)
                ?.facilityFields?.find(
                  (ff) =>
                    ff.key === faclitityFieldKey &&
                    ff.group === facilityGroupName,
                );

              this.openThemedModal(false, facilityField);
            }
          }
          this.customerName = customers[token.customer_key]?.name;
        },
      );

    this.promptLeadToAddNewCustomersIntoTopicChats();
  }

  private receiveMessages() {
    this.store
      .select(selectExistingChats)

      .pipe(
        filter(
          (chats) =>
            this.socketService.checkIsConnected(SocketType.CHAT) &&
            chats.length > 0,
        ),
        switchMap((existingChats) => {
          const messageStreams = existingChats.map((chat: Chat) => {
            const destination = `${CHAT_SOCKET_RECEIVE_MESSAGE_DESTINATION}-${chat.id}`;
            return this.socketService.getMessagesByDestination$(
              destination,
              SocketType.CHAT,
            );
          });
          return merge(...messageStreams);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((subscriptionMessage: ChatMessage) => {
        const message = subscriptionMessage;
        if (message.userId === this._userCustomerKey) {
          return;
        }

        const isNewChat = ![...this.allActiveChats, ...this.archivedChats]
          ?.map((c) => c.id)
          .includes(message.chatId);
        if (isNewChat) {
          this.chatManagementController
            .getChat({
              chatId: message.chatId,
              businessCaseId: this.businessCase?.id,
            })
            .subscribe((chat) => {
              this.store.dispatch(
                StateLibChatPageActions.addToExistingChats({
                  payload: chat,
                }),
              );
            });
        }

        if (this.chatId !== message.chatId) {
          this.onSocketReceivedMessage(message);
        }
      });
  }

  changeTab(event: NgbNavChangeEvent) {
    if (!event.nextId) {
      return;
    }

    this.activeId = event.nextId as ChatTab;
    const [tab, chatTabId] = (event.nextId as ChatTab).split('/');

    this.chatType = tab as ChatTab;
    this.chatId = chatTabId;

    if (!chatTabId && this.chatType === 'business-case') {
      this.chatId = this.businessCaseChat?.id;
    }

    // always refresh the chat to get latest participants and metadata
    if (this.chatId) {
      this.store.dispatch(
        StateLibChatPageActions.refreshChat({ payload: this.chatId }),
      );
    }
    this.navService.addTabFragmentToURL(
      this._userCustomerKey,
      this.businessCase.id,
      tab,
      chatTabId,
    );

    this.store.dispatch(
      StateLibChatPageActions.setCurrentChat({
        payload: {
          ...this.currentChatSelected,
        },
      }),
    );
  }

  openBilateralModal() {
    this.canCreateBilateralChat$.pipe(take(1)).subscribe((canCreate) => {
      if (!canCreate) {
        return;
      }

      this.modalService.openComponent(
        BilateralModalComponent,
        {
          businessCase: this.businessCase,
          userCustomerKey: this._userCustomerKey,
          existingChats: [...this.allActiveChats, ...this.archivedChats],
          access: this.access,
        },
        { size: 'lg' },
        (res) => {
          if (res.success) {
            const customerSelected = res.data as string;

            this.chatManagementController
              .createChat({
                body: {
                  customerKey: this._userCustomerKey,
                  chatType: ChatType.ONE_ON_ONE,
                  toCustomer: customerSelected,
                  userId: this.userId,
                  businessCaseId: this.businessCase.id,
                },
                businessCaseId: this.businessCase.id,
              })
              .pipe(takeUntilDestroyed(this.destroyRef))
              .subscribe((chat) => {
                this.store.dispatch(
                  StateLibChatPageActions.addToExistingChats({ payload: chat }),
                );
                this.navService.addTabFragmentToURL(
                  this._userCustomerKey,
                  this.businessCase.id,
                  ChatTab.BILATERAL,
                  chat.id,
                );
                this.chatId = chat.id;
                this.activeId = ('bilateral/' + chat.id) as ChatTab;
                this.changeTabWithManualEvent(this.activeId);
                this.finToastService.show(Toast.success());
              });
          }
        },
      );
    });
  }

  openThemedModal(
    isEdit: boolean,
    field?: TemplateField | FacilityFieldViewModel,
    information?: BusinessCaseInformation,
  ) {
    this.themedModalOpened = true;

    const modalParams = {
      field: !isEdit && field,
      businessCase: this.businessCase,
      userCustomerKey: this._userCustomerKey,
      existingChat: isEdit && this.currentChatSelected,
      editTopicDisabled:
        !!field || (isEdit && !this.currentChatSelected?.createdFromButton),
      information,
    };

    this.modalService.openComponent(
      ThemedModalComponent,
      {
        ...modalParams,
      },
      { size: 'lg' },
      (res) => {
        if (res.success) {
          const chatData = res.data as ThemedChatGroup;

          if (isEdit) {
            this.handleEditTopicChat(chatData);
          } else {
            this.handleCreateTopicChat(
              chatData,
              information,
              field as FacilityFieldViewModel,
            );
          }
        }
      },
    );
  }

  createInternalChat() {
    this.modalService.openComponent(
      InternalBilateralModalComponent,
      {
        usersAvailable: this.usersAvailable,
      },
      { size: 'lg' },
      (res) => {
        if (res.success) {
          const usersSelectedIds = res.data as string[];
          const chatType =
            usersSelectedIds?.length > 1
              ? ChatType.INTERNAL_GROUP
              : ChatType.INTERNAL_BILATERAL;

          let existingInternalChat: Chat;

          if (chatType === ChatType.INTERNAL_GROUP) {
            usersSelectedIds.push(this.userId);

            existingInternalChat = this.chatsInitial
              .filter(
                (chat) =>
                  chat.status === 'ACTIVE' &&
                  chat.chatType === ChatType.INTERNAL_GROUP,
              )
              .find((chat) => {
                const existingChatUsers = uniq(
                  chat.chatUsers?.map((cu) => cu.userId),
                );
                return (
                  existingChatUsers.length === usersSelectedIds.length &&
                  existingChatUsers.every((uid) =>
                    usersSelectedIds.includes(uid),
                  )
                );
              });
          } else {
            existingInternalChat = this.chatsInitial
              .filter(
                (chat) =>
                  chat.status === 'ACTIVE' &&
                  chat.chatType === ChatType.INTERNAL_BILATERAL,
              )
              .find((chat) => {
                const toUserId = usersSelectedIds[0];
                return (
                  (chat.userId === toUserId || chat.toUserId === toUserId) &&
                  (chat.userId === this.userId || chat.toUserId === this.userId)
                );
              });
          }

          if (existingInternalChat) {
            this.chatId = existingInternalChat.id;
            this.activeId = ('internal/' + existingInternalChat.id) as ChatTab;
            this.navService.addTabFragmentToURL(
              this._userCustomerKey,
              this.businessCase.id,
              ChatTab.INTERNAL,
              existingInternalChat.id,
            );
            this.changeTabWithManualEvent(this.activeId);

            return;
          }

          this.chatManagementController
            .createChat({
              body: {
                customerKey: this._userCustomerKey,
                chatType,
                component: uuidv4(),
                participantCustomerKeys: [this._userCustomerKey],
                userId: this.userId,
                participantUserIds: usersSelectedIds,
                toUserId: usersSelectedIds[0],
                businessCaseId: this.businessCase.id,
              },
              businessCaseId: this.businessCase.id,
            })
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe({
              next: (chat) => {
                this.store.dispatch(
                  StateLibChatPageActions.addToExistingChats({ payload: chat }),
                );

                this.chatId = chat.id;
                this.activeId = ('internal/' + chat.id) as ChatTab;

                this.navService.addTabFragmentToURL(
                  this._userCustomerKey,
                  this.businessCase.id,
                  ChatTab.INTERNAL,
                  chat.id,
                );
                this.changeTabWithManualEvent(this.activeId);

                this.finToastService.show(Toast.success());
              },
            });
        }
      },
    );
  }

  onSocketReceivedMessage(message: ChatMessage) {
    const chat = this.getChatRepresentationModel(message?.chatId);
    chat && chat.unreadMessagesCount++;
  }

  changeTabWithManualEvent(event: string) {
    const tabEvent: NgbNavChangeEvent = {
      nextId: event,
      activeId: this.activeId,
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      preventDefault: () => {},
    };

    this.changeTab(tabEvent);
  }

  isChatMuted(chatId: string) {
    return this.mutedChatIds?.includes(chatId);
  }

  onAllMessagesRead(chatId: string) {
    const chat = this.getChatRepresentationModel(chatId);
    if (chat) {
      chat.unreadMessagesCount = 0;
    }
  }

  private getChatRepresentationModel(chatId: string) {
    const allActiveChatsRepresentation = [
      ...this.bilateralChats,
      ...this.topicChats,
      this.customerInternalChat,
      ...this.internalChats,
      this.businessCaseChat,
    ];
    return allActiveChatsRepresentation
      .filter((chat) => !chat?.isChatOnMute)
      .find((chat) => chat?.id === chatId);
  }

  private handleCreateTopicChat(
    chatData: ThemedChatGroup,
    information: BusinessCaseInformation,
    field: FacilityFieldViewModel,
  ) {
    const chatParticipants =
      this.businessCase?.leadCustomerKey === this._userCustomerKey
        ? [...chatData.participants, this._userCustomerKey]
        : this.businessCase.participants.map((p) => p.customerKey);

    const component = information
      ? information.id
      : field
        ? field.id
        : uuidv4();

    this.chatManagementController
      .createChat({
        body: {
          customerKey: this._userCustomerKey,
          chatType: ChatType.ON_TOPIC,
          component,
          createdFromButton: !information && !field,
          topic: chatData.theme,
          description: chatData.description,
          participantCustomerKeys: chatParticipants,
          userId: this.userId,
          businessCaseId: this.businessCase.id,
        },
        businessCaseId: this.businessCase.id,
      })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (chat) => {
          this.store.dispatch(
            StateLibChatPageActions.addToExistingChats({ payload: chat }),
          );
          this.navService.addTabFragmentToURL(
            this._userCustomerKey,
            this.businessCase.id,
            ChatTab.TOPIC,
            chat.id,
          );

          this.activeId = ('topic/' + chat.id) as ChatTab;
          this.changeTabWithManualEvent(this.activeId);
          this.accessPromptExistingCustomers();

          this.themedModalOpened = false;
        },
      });
  }

  private accessPromptExistingCustomers() {
    if (this.businessCase.leadCustomerKey === this._userCustomerKey) {
      this.chatAccessPromptService
        .getAllForBusinessCase({ businessCaseId: this.businessCase.id })
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe((promptedCustomers) => {
          const promptedCustomerKeys = promptedCustomers.map(
            (pb) => pb.customerKey,
          );
          const customerKeysToPrompt = this.businessCase.participants
            .map((p) => p.customerKey)
            .filter(
              (promptedCustomerKey) =>
                !promptedCustomerKeys.includes(promptedCustomerKey),
            );
          if (customerKeysToPrompt?.length) {
            customerKeysToPrompt.forEach((pbk) => {
              this.chatAccessPromptService
                .createAccessPrompt({
                  isAccessPrompt: true,
                  businessCaseId: this.businessCase.id,
                  customerKey: pbk,
                })
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe();
            });
          }
        });
    }
  }

  private oneOnOneChatAlreadyExists(
    customerOneKey: string,
    customerTwoKey: string,
  ) {
    // TODO: Duplicate function -> exists in BilateralModalComponent
    const customerKeysToCheck = [customerOneKey, customerTwoKey];
    const chatsToCheck = [...this.allActiveChats, ...this.archivedChats];

    return !!chatsToCheck.find(
      (chat) =>
        chat?.chatType === ChatType.ONE_ON_ONE &&
        customerKeysToCheck.includes(chat.customerKey) &&
        customerKeysToCheck.includes(chat.toCustomer),
    );
  }

  private handleEditTopicChat(chatData: ThemedChatGroup) {
    const currentChatParticipants =
      this.currentChatSelected.chatCustomers?.map((cb) => cb.customerKey) || [];
    const chatId = this.currentChatSelected.id;

    const isTopicNameChanged =
      chatData.theme !== this.currentChatSelected.topic;
    const isDescriptionChanged =
      chatData.description !== this.currentChatSelected.description;
    const isCustomerParticipantChanged = !isEqual(
      chatData.participants,
      currentChatParticipants,
    );
    const participants =
      isCustomerParticipantChanged &&
      uniq([...chatData.participants, this._userCustomerKey]);

    const requests = [];

    if (isTopicNameChanged) {
      requests.push(
        this.chatManagementController.changeChatTopic({
          newTopic: chatData.theme,
          chatId,
          businessCaseId: this.businessCase?.id,
        }),
      );
    }
    if (isDescriptionChanged) {
      requests.push(
        this.chatManagementController.changeChatDescription({
          newDescription: chatData.description,
          chatId,
          businessCaseId: this.businessCase?.id,
        }),
      );
    }
    if (isCustomerParticipantChanged) {
      requests.push(
        this.chatManagementController.replaceChatParticipantCustomers({
          body: {
            customerKeys: participants,
          },
          chatId,
          businessCaseId: this.businessCase?.id,
        }),
      );
    }

    if (requests.length) {
      concat(...requests)
        .pipe(takeLast(1))
        .subscribe({
          next: (chatUpdated) => {
            this.store.dispatch(
              StateLibChatPageActions.updateChat({ payload: chatUpdated }),
            );
            this.themedModalOpened = false;
          },
        });
    }
  }

  private constructTabChats(chats: Chat[]) {
    if (
      (chats?.length &&
        this.chatId &&
        !chats.find((chat) => chat.id === this.chatId)) ||
      !this.canAccessChat(chats)
    ) {
      this.navService.navigateToChatDefault(
        this.businessCase.id,
        this._userCustomerKey,
      );
      this.chatId = '';
      this.activeId = '' as ChatTab;
    }

    if (
      !this.userId ||
      !this.businessCase?.id ||
      !this._userCustomerKey ||
      this.isConstructingInProgress ||
      !chats?.length
    ) {
      return;
    }

    this.allActiveChats = chats.filter((c) => c.status === 'ACTIVE');
    this.archivedChats = chats.filter(
      (c) =>
        c.status === 'ARCHIVED' &&
        (c.customerKey === this._userCustomerKey ||
          c.toCustomer === this._userCustomerKey ||
          c.chatCustomers
            .map((cb) => cb.customerKey)
            .includes(this._userCustomerKey)),
    );
    this.isConstructingInProgress = true;

    this.chatNotificationControllerService
      .getAllUnreadMessagesOfUsersAllChats({
        userId: this.userId,
        businessCaseId: this.businessCase.id,
        customerKey: this._userCustomerKey,
      })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((response) => {
        const unreadMessagesPerChat = response.countOfUnreadMessagesPerChat;

        const businessCaseChatModel = chats.find(
          (c) => c?.chatType === ChatType.CONSORTIUM,
        );
        if (businessCaseChatModel) {
          this.businessCaseChat = {
            dto: businessCaseChatModel,
            id: businessCaseChatModel.id,
            name: 'Chat',
            unreadMessagesCount:
              (unreadMessagesPerChat &&
                unreadMessagesPerChat[businessCaseChatModel.id]) ||
              0,
            isChatOnMute: this.isChatMuted(businessCaseChatModel.id),
          };
        }

        const customerInternalChatModel = chats.find(
          (chat) =>
            chat?.chatType === ChatType.INTERNAL &&
            chat.customerKey === this._userCustomerKey,
        );
        if (customerInternalChatModel) {
          this.customerInternalChat = {
            dto: customerInternalChatModel,
            id: customerInternalChatModel?.id,
            name: 'Interner Chat',
            unreadMessagesCount:
              (unreadMessagesPerChat &&
                unreadMessagesPerChat[customerInternalChatModel?.id]) ||
              0,
            isChatOnMute: this.isChatMuted(customerInternalChatModel?.id),
          };
        }

        const internalChatsRaw = this.allActiveChats
          .filter(
            (c) =>
              (c.chatType === 'INTERNAL_BILATERAL' ||
                c.chatType === 'INTERNAL_GROUP') &&
              c.status === 'ACTIVE',
          )
          .filter(
            (c) =>
              c.userId === this.userId ||
              c.toUserId === this.userId ||
              c.chatUsers?.some((u) => u.userId === this.userId),
          );

        this.internalChats = internalChatsRaw
          .filter((c) => c.topic !== 'INTERNAL_GROUP_COMPANY_CHAT') // TODO: To be removed when BE ready company contact stuff
          .map((c) => {
            return {
              dto: c,
              id: c.id,
              name: this.getInternalChatName(c),
              unreadMessagesCount:
                (unreadMessagesPerChat && unreadMessagesPerChat[c.id]) || 0,
              isChatOnMute: this.isChatMuted(c.id),
            };
          });

        this.topicChats = this.allActiveChats
          .filter(
            (c) => c?.chatType === ChatType.ON_TOPIC && c?.status === 'ACTIVE',
          )
          .filter((c) => {
            return (
              this.businessCase.leadCustomerKey === this._userCustomerKey ||
              c.chatCustomers?.some(
                (cb) => cb.customerKey === this._userCustomerKey,
              ) ||
              c.customerKey === this._userCustomerKey
            );
          })
          .map((c) => {
            return {
              dto: c,
              id: c.id,
              name: c.topic,
              unreadMessagesCount:
                (unreadMessagesPerChat && unreadMessagesPerChat[c.id]) || 0,
              isChatOnMute: this.isChatMuted(c.id),
            };
          });

        this.bilateralChats = this.allActiveChats
          .filter((c) => c.chatType === 'ONE_ON_ONE' && c.status === 'ACTIVE')
          .filter((c) => {
            return (
              c.customerKey === this._userCustomerKey ||
              c.toCustomer === this._userCustomerKey
            );
          })
          .map((c) => {
            return {
              dto: c,
              id: c.id,
              name: this.getChatCustomerName(c),
              unreadMessagesCount:
                (unreadMessagesPerChat && unreadMessagesPerChat[c.id]) || 0,
              isChatOnMute: this.isChatMuted(c.id),
            };
          });

        this.isConstructingInProgress = false;
        this.chatConstructingFinished = true;

        // stupid workaround when new business case - to be refactored
        if (!this.chatId) {
          this.changeTabWithManualEvent(this.activeId);
        }
      });
  }

  private canAccessChat(chats: Chat[]) {
    const chat = chats?.find((c) => c.id === this.chatId);
    const isTopicChat = chat?.chatType === ChatType.ON_TOPIC;

    if (!isTopicChat) {
      return true;
    }

    return (
      chat?.chatCustomers.some(
        (b) => b.customerKey === this._userCustomerKey,
      ) || this.businessCase.leadCustomerKey === this._userCustomerKey
    );
  }

  private getChatCustomerName(chat: Chat) {
    const customerKey =
      chat.customerKey === this._userCustomerKey
        ? chat.toCustomer
        : chat.customerKey;
    return this.customerNamesByKey[customerKey]?.name;
  }

  private getInternalChatName(chat: Chat) {
    const usersToShow = chat.chatUsers?.filter(
      (cu) => cu.userId !== this.userId,
    );
    return usersToShow?.map((u) => `${u?.firstName} ${u?.lastName}`).join(', ');
  }

  private promptLeadToAddNewCustomersIntoTopicChats() {
    setTimeout(() => {
      const isLeadCustomer =
        this.businessCase?.leadCustomerKey === this._userCustomerKey;
      if (isLeadCustomer && this.topicChats?.length) {
        forkJoin([
          this.store.select(selectActiveParticipants).pipe(take(1)),
          this.chatAccessPromptService.getAllForBusinessCase({
            businessCaseId: this.businessCase.id,
          }),
        ])
          .pipe(
            withLatestFrom(
              this.store.select(selectBusinessCaseParticipantsPermissions),
            ),
          )
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe(
            ([[allParticipants, prompted], participantsPermissions]) => {
              const promptedCustomers = prompted.map((p) => p.customerKey);
              const customersToPrompt = allParticipants.filter(
                (p) => !promptedCustomers.includes(p.customerKey),
              );
              customersToPrompt
                .filter((p) =>
                  participantsPermissions[p.customerKey].permissions.includes(
                    BusinessCasePermission.BCP_00131,
                  ),
                )
                .forEach((participantCustomer) => {
                  const topicChatsToAddTo = this.topicChats.filter(
                    (chat) =>
                      !chat.dto.chatCustomers
                        ?.map((cb) => cb.customerKey)
                        .includes(participantCustomer.customerKey) &&
                      chat.dto.customerKey === this._userCustomerKey,
                  );

                  if (!topicChatsToAddTo?.length) {
                    return;
                  }
                  // TODO: ???? Prompt only 131
                  this.modalService.openComponent(
                    AddNewCustomerToChatsComponent,
                    {
                      topicChats: topicChatsToAddTo.map((ch) => ch.dto),
                      customerName:
                        this.customerNamesByKey[participantCustomer.customerKey]
                          ?.name,
                    },
                    {},
                    (res) => {
                      this.chatAccessPromptService
                        .createAccessPrompt({
                          isAccessPrompt: true,
                          businessCaseId: this.businessCase.id,
                          customerKey: participantCustomer.customerKey,
                        })
                        .pipe(takeUntilDestroyed(this.destroyRef))
                        .subscribe();

                      if (!isEmpty(res.data)) {
                        const chatsIds = (
                          res.data as { chatsToAddTo: string[] }
                        ).chatsToAddTo;
                        if (chatsIds?.length) {
                          chatsIds.forEach((chatId) => {
                            const chatParticipants =
                              this.allActiveChats
                                .find((ch) => ch.id === chatId)
                                ?.chatCustomers?.map((cb) => cb.customerKey) ||
                              [];
                            chatParticipants.push(
                              participantCustomer.customerKey,
                            );

                            this.chatManagementController
                              .replaceChatParticipantCustomers({
                                body: {
                                  customerKeys: chatParticipants,
                                },
                                chatId,
                                businessCaseId: this.businessCase.id,
                              })
                              .pipe(takeUntilDestroyed(this.destroyRef))
                              .subscribe({
                                next: (chatUpdated) => {
                                  this.store.dispatch(
                                    StateLibChatPageActions.updateChat({
                                      payload: chatUpdated,
                                    }),
                                  );
                                },
                              });
                          });
                        }
                      }
                    },
                  );
                });
            },
          );
      }
    }, 500);
  }

  private loadUsers(customerKey: string, userId: string) {
    const customerCaseUsersIds = this.businessCase?.participants
      .find((p) => p.customerKey === customerKey)
      ?.users?.map((u) => u.userId);
    forkJoin([
      this.store
        .select(selectCustomerBusinessCaseContext)
        .pipe(filter(Boolean), take(1)),
      this.userManagementControllerService.getAllUsersForCustomer({
        customerKey: customerKey,
      }),
    ])
      .pipe(
        map(([ctx, users]) => {
          return users
            .filter((u) => {
              const notSameUserFilter = u.id !== userId;
              if (ctx.participationType === ParticipationType.COLLABORATOR) {
                return notSameUserFilter;
              }
              const isUserPlatformManager =
                u.userState === UserState.ACTIVE &&
                u.userRoles.some(
                  (role) => role.name === UserRole.PLATFORM_MANAGER,
                );

              const isUserPartOfCase = customerCaseUsersIds.some(
                (userId) => userId === u.id,
              );

              return (
                notSameUserFilter && (isUserPlatformManager || isUserPartOfCase)
              );
            })
            .map(
              (u) =>
                ({
                  ...u,
                  dto: u,
                  fullName:
                    [u.firstName, u.lastName].filter(Boolean).join(' ') || u.id,
                }) as CustomerUserDtoExtended,
            );
        }),
        catchError(() => of([])),
      )
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((usersMapped) => {
        this._isSingleCustomerUser.next(!usersMapped.length);
        this.usersAvailable = usersMapped;
      });

    this.usersLoaded = true;
  }

  ngOnDestroy(): void {
    this.destroyReceiveMessages$$.next(true);
  }
}
