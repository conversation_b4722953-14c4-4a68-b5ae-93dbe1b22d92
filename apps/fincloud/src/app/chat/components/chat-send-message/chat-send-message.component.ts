import { DOCUMENT } from '@angular/common';
import {
  AfterViewInit,
  Component,
  DestroyRef,
  ElementRef,
  EventEmitter,
  Inject,
  Input,
  OnChanges,
  OnInit,
  Output,
  SecurityContext,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { HorizontalCarouselSliderComponent } from '@fincloud/components/navigation';
import { AuthenticationService } from '@fincloud/core/auth';
import { SocketService, SocketType } from '@fincloud/core/socket';
import { selectAccessRights } from '@fincloud/state/access';
import { selectBusinessCase } from '@fincloud/state/business-case';
import {
  StateLibChatPageActions,
  selectAllChatUploadedFiles,
  selectIsChatFileUploading,
} from '@fincloud/state/chat';
import {
  AccessRights,
  AppState,
  ChatAttachment,
  ChatSendMessageDto,
} from '@fincloud/types/models';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { selectUser, selectUserCustomerKey } from '@fincloud/state/user';
import { InterestedCustomersControllerService } from '@fincloud/swagger-generator/business-case-manager';
import { Chat } from '@fincloud/swagger-generator/communication';
import { DocumentEntity } from '@fincloud/swagger-generator/document';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import { CHAT_SOCKET_SEND_MESSAGE_DESTINATION } from '@fincloud/utils';
import { Store } from '@ngrx/store';
import { isEqual, uniq } from 'lodash-es';
import {
  catchError,
  combineLatest,
  distinctUntilChanged,
  merge,
  of,
  tap,
} from 'rxjs';
import { CustomerUserSuggestionDto } from '../../models/customer-user-suggestion-dto';
import { BUSINESS_CASE_CANCELLED_STATES } from '../../utils/business-case-cancelled-states';
import { BUSINESS_CASE_COMPLETED_STATES } from '../../utils/business-case-completed-states';
import { restoreUmlautsAfterSanitization } from '../../utils/restore-umlauts-after-sanitization';

@Component({
  selector: 'app-chat-send-message',
  templateUrl: './chat-send-message.component.html',
  styleUrls: ['./chat-send-message.component.scss'],
})
export class ChatSendMessageComponent
  implements OnChanges, OnInit, AfterViewInit
{
  @Input() channelId: string;
  @Input() disabled: boolean;
  @Input() chatParticipantsSuggestions: CustomerUserSuggestionDto[];
  @Input() chat: Chat;
  @Input() isFirstMessage: boolean;

  @Output() messageSent = new EventEmitter();

  @ViewChild('selectFileElement') selectFileElement: ElementRef;
  @ViewChild('carouselSlider')
  carouselSlider: HorizontalCarouselSliderComponent;
  @ViewChild('chatMessageArea') chatMessageArea: ElementRef<HTMLElement>;

  singleDocumentCardWidth = 240;

  public message: string;
  preventSendingMessage: boolean;

  currentFileBeingUploaded: ChatAttachment;

  public selectedFiles: ChatAttachment[] = [];
  public isFileUploading = false;
  public isMessageSending = false;

  private messageSenderData: {
    token: string;
    id: string;
    customerKey: string;
    name: string;
  };

  tagCounter = 0;
  taggedUsers: CustomerUserSuggestionDto[] = [];

  access: AccessRights;
  businessCase: ExchangeBusinessCase;
  userId: string;
  isChatWithLeadAsNonParticipant: boolean;

  get allElements() {
    return [this.currentFileBeingUploaded, ...this.selectedFiles];
  }

  get isSendDisabled() {
    return (
      this.isFileUploading ||
      this.isMessageSending ||
      this.disabled ||
      (!this.message &&
        (!this.selectedFiles || this.selectedFiles.length === 0)) ||
      (!this.message
        ?.replace(/\s/g, '')
        ?.replace(/&nbsp;/g, '')
        ?.replace(/<br>/g, '')
        ?.replace(/<div><\/div>/g, '')?.length &&
        !this.selectedFiles.length)
    );
  }

  constructor(
    @Inject(DOCUMENT) private document: Document,
    private socketService: SocketService,
    private store: Store<AppState>,
    private authService: AuthenticationService,
    private interestedCustomersService: InterestedCustomersControllerService,
    private sanitizer: DomSanitizer,
    private destroyRef: DestroyRef,
  ) {}

  ngOnInit() {
    merge(
      this.store.select(selectAllChatUploadedFiles).pipe(
        distinctUntilChanged(),
        tap((allFiles) => {
          const newSelectedFiles = allFiles.map((file: DocumentEntity) =>
            this.mapToChatAttachmentDto(file),
          );
          this.selectedFiles = [...newSelectedFiles];
        }),
      ),
      this.store.select(selectIsChatFileUploading).pipe(
        distinctUntilChanged(isEqual),
        tap((isUploadingRes) => {
          this.isFileUploading = isUploadingRes;
        }),
      ),
      combineLatest([
        this.store.select(selectUser),
        this.store.select(selectUserCustomerKey),
        this.store.select(selectAccessRights),
        this.store.select(selectBusinessCase),
      ]).pipe(
        distinctUntilChanged(),
        tap(([user, userCustomerKey, access, businessCase]) => {
          this.messageSenderData = {
            token: this.authService.getToken(),
            id: user.id,
            customerKey: userCustomerKey,
            name: `${user.firstName} ${user.lastName}`,
          };
          this.access = access;
          this.businessCase = businessCase;
          this.userId = user.id;
          this.isChatWithLeadAsNonParticipant =
            !this.businessCase?.participants.find(
              (p) => p.customerKey === userCustomerKey,
            ) &&
            this.chat?.chatType === 'ONE_ON_ONE' &&
            (this.chat?.customerKey === this.businessCase?.leadCustomerKey ||
              this.chat?.toCustomer === this.businessCase?.leadCustomerKey);

          this.showCantWriteMessageIfDisabledAndBusinessCasesChat();
        }),
      ),
    )
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.disabled?.previousValue && !changes.disabled?.currentValue) {
      this.chatMessageArea.nativeElement.innerHTML = '';
    }
    this.showCantWriteMessageIfDisabledAndBusinessCasesChat();
  }

  ngAfterViewInit(): void {
    this.chatMessageArea.nativeElement.addEventListener(
      'paste',
      (event: ClipboardEvent) => {
        event.preventDefault();
        const textStripped = event.clipboardData.getData('text/plain');
        const sanitizedValue =
          this.sanitizer
            .sanitize(SecurityContext.HTML, textStripped)
            .replace(/&#10;/g, '<br>') || '';
        this.chatMessageArea.nativeElement.innerHTML = sanitizedValue
          ? restoreUmlautsAfterSanitization(sanitizedValue)
          : sanitizedValue;
      },
    );
  }

  browseFiles() {
    (this.selectFileElement.nativeElement as HTMLInputElement).click();
  }

  sendMessage() {
    if (this.isSendDisabled) {
      return;
    }
    this.isMessageSending = true;

    const uniqueTaggedUserIds = uniq(
      this.taggedUsers
        .filter((user) =>
          this.message.includes(
            user.messageTagged.replace(' contenteditable="false"', ''),
          ),
        )
        .map((user) => user.id),
    );
    const sendMessage = {
      chatId: this.channelId,
      content: this.message,
      attachments: this.selectedFiles,
      customerKey: this.messageSenderData.customerKey,
      userId: this.messageSenderData.id,
      jwtToken: this.messageSenderData.token,
      senderName: this.messageSenderData.name,
      taggedUserIds: uniqueTaggedUserIds,
    } as ChatSendMessageDto;

    this.socketService.sendMessage(
      {
        message: sendMessage,
        businessCaseId: this.businessCase.id,
      },
      CHAT_SOCKET_SEND_MESSAGE_DESTINATION,
      SocketType.CHAT,
    );
    this.store.dispatch(StateLibChatPageActions.clearAllUploadedFiles());

    if (this.isChatWithLeadAsNonParticipant && this.isFirstMessage) {
      this.interestedCustomersService
        .addCommunicatorsToInterestedCustomer({
          businessCaseId: this.businessCase.id,
          communicators: [this.messageSenderData.customerKey],
        })
        .pipe(
          catchError((err) => {
            console.error(err?.message);
            return of(null);
          }),
        )
        .subscribe();
    }

    this.messageSent.emit();
    this.message = null;
    this.selectedFiles = [];
    this.taggedUsers = [];

    this.isMessageSending = false;
  }

  onRemoveSelectedFile(file: ChatAttachment) {
    this.store.dispatch(
      StateLibChatPageActions.removeSingleFile({ fileName: file.fileName }),
    );
  }

  private mapToChatAttachmentDto(file: DocumentEntity) {
    return {
      customerKey: file.userCustomerKey,
      documentId: file.id,
      fileName: file.contentReference?.fileName,
      mimeType: file.contentReference?.contentType,
    } as ChatAttachment;
  }

  selectFile(event: Event) {
    const input = event.target as HTMLInputElement;
    const file = input.files[0];
    if (file) {
      this.store.dispatch(
        StateLibChatPageActions.initiateFileUpload({
          file: file,
          businessCaseId: this.businessCase.id,
        }),
      );
      input.value = '';
      this.currentFileBeingUploaded = {
        fileName: file.name,
      };
    }
  }

  suggestionSelected(itemSelected: CustomerUserSuggestionDto) {
    this.preventSendingMessage = true;

    setTimeout(() => {
      const suggestionFullString = `@${itemSelected.name}`;
      const currentMessage = this.chatMessageArea.nativeElement.textContent;
      const modifiedMessage = currentMessage
        .replace(
          suggestionFullString,
          `<span class="suggestion-selected ${
            this.tagCounter
          }" contenteditable="false">${suggestionFullString.replace(
            '@',
            '',
          )}</span>`,
        )
        .replace('</span><span', '</span>&nbsp;<span');

      this.chatMessageArea.nativeElement.innerHTML = modifiedMessage;
      this.chatMessageArea.nativeElement.dispatchEvent(
        new KeyboardEvent('keyup'),
      );
      itemSelected.messageTagged = modifiedMessage;
      this.taggedUsers.push(itemSelected);

      // put the cursor to the end of field again...
      this.putCaretAfterSuggestion(this.tagCounter);
      // make send message available | valid for mouse click
      this.preventSendingMessage = false;
      this.tagCounter++;
    }, 0);
  }

  private showCantWriteMessageIfDisabledAndBusinessCasesChat() {
    const isBusinessCaseCanceled = BUSINESS_CASE_CANCELLED_STATES.includes(
      this.businessCase?.state,
    );
    const isBusinessCaseCompleted = BUSINESS_CASE_COMPLETED_STATES.includes(
      this.businessCase?.state,
    );

    let cantTypeMessage = '';
    if (isBusinessCaseCanceled) {
      cantTypeMessage = $localize`:@@chat.send.message.businessCaseCanceled.cantTypeMessage:Der Chat steht Ihnen derzeit nicht zur Verfügung, da Ihr Finanzierungsfall storniert wurde.`;
    } else if (isBusinessCaseCompleted) {
      cantTypeMessage = $localize`:@@chat.send.message.businessCaseCompleted.cantTypeMessage:Der Chat steht Ihnen nicht zur Verfügung, da Ihr Finanzierungsfall abgeschlossen wurde.`;
    } else if (this.disabled) {
      cantTypeMessage = $localize`:@@chat.send.message.isTypeInBusinessCaseDisabled.cantTypeMessage:Der Chat steht Ihnen aktuell noch nicht zur Verfügung`;
    }

    if (this.chatMessageArea && cantTypeMessage) {
      this.chatMessageArea.nativeElement.innerHTML = cantTypeMessage;
    }
  }

  private putCaretAfterSuggestion(tagCount: number) {
    const range = this.document.createRange();
    const arrayNodes = Array.from(
      this.chatMessageArea.nativeElement.childNodes,
    );

    const caretSuggestionIndex = arrayNodes.findIndex((node) =>
      (node as HTMLElement).className?.includes(tagCount.toString()),
    );
    range.setEndAfter(
      this.chatMessageArea.nativeElement.childNodes[caretSuggestionIndex],
    );
    range.collapse(false);

    const selection = this.document.getSelection();
    selection.removeAllRanges();
    selection.addRange(range);
  }

  private setCaretEnd() {
    // TODO: Combine with top method
    const range = this.document.createRange();
    const length = this.chatMessageArea.nativeElement.childNodes.length;
    range.setEnd(this.chatMessageArea.nativeElement, length - 1);
    range.collapse(false);

    const selection = this.document.getSelection();
    selection.removeAllRanges();
    selection.addRange(range);
  }

  onKeydown(event: KeyboardEvent) {
    if (this.preventSendingMessage) {
      this.preventSendingMessage = false;
      return;
    }

    if (event.key === 'Enter' && event.shiftKey) {
      if (!this.message) {
        return;
      }
      event.preventDefault();
      const newRow = this.message?.endsWith('<br>') ? '<br>' : '<br><br>';
      const modifiedMessage = `${this.message}${newRow}`;
      this.chatMessageArea.nativeElement.innerHTML = modifiedMessage;
      this.setCaretEnd();
    } else if (event.key === 'Enter') {
      event.preventDefault();
      this.sendMessage();
    }
  }
}
