import { Component, Input } from '@angular/core';
import { StateLibAccountManagementPageActions } from '@fincloud/state/account-management';
import {
  BusinessCase,
  Template,
  TemplateDto,
} from '@fincloud/swagger-generator/business-case-manager';
import { CadrTemplate } from '@fincloud/swagger-generator/company';
import { AppState } from '@fincloud/types/models';
import { BLANK_TEMPLATE, BLANK_TEMPLATE_CADR } from '@fincloud/utils';
import { Store } from '@ngrx/store';
import { BusinessCaseTemplateManagementPageActions } from '../../+state/actions';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss'],
})
export class SidebarComponent {
  @Input() businessCaseTemplates: Template[];
  @Input() cadrTemplate: CadrTemplate | undefined;
  @Input() customerKey: string;

  @Input() isAccountManager: boolean;

  @Input() isNewTemplateMode: boolean;

  @Input() hasCadrTemplate: boolean;

  @Input() selectedBusinessCase: CadrTemplate | Template;

  constructor(private store: Store<AppState>) {}

  addTemplate(isCadr: boolean) {
    const templateToBeSet = isCadr ? BLANK_TEMPLATE_CADR : BLANK_TEMPLATE;

    this.store.dispatch(
      BusinessCaseTemplateManagementPageActions.setCaseTemplate({
        template: templateToBeSet,
        isCadrSelected: isCadr,
        isNewTemplateMode: true,
      }),
    );
  }

  saveSelectedBusinessCaseIdInStore(
    template: TemplateDto | CadrTemplate,
    isCadr: boolean,
  ) {
    if (this.isAccountManager) {
      return this.store.dispatch(
        StateLibAccountManagementPageActions.setBusinessCaseTemplate({
          template: template,
          isCadrSelected: isCadr,
          isNewTemplateMode: false,
        }),
      );
    }

    return this.store.dispatch(
      BusinessCaseTemplateManagementPageActions.setCaseTemplate({
        template,
        isCadrSelected: isCadr,
        isNewTemplateMode: false,
      }),
    );
  }

  trackById(_: number, businessCase: BusinessCase) {
    return businessCase.id;
  }
}
