import { FormControl, FormGroup } from '@angular/forms';
import { InvitationApplicationFiltersForm } from '../models/invitation-application-filters-form';
import { InvitationApplicationInitialData } from '../models/invitation-application-initial-data';

export const createInvitationApplicationFiltersGroup = (
  initialData: InvitationApplicationInitialData,
): FormGroup<InvitationApplicationFiltersForm> => {
  return new FormGroup<InvitationApplicationFiltersForm>({
    caseType: new FormGroup({
      financing: new FormControl(initialData.caseType.financing),
    }),
    invitationsAndApplications: new FormControl(
      initialData.invitationsApplications,
    ),
  });
};
