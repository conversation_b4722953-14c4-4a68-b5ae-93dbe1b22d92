import { FormControl, FormGroup } from '@angular/forms';
import { CaseFiltersForm } from '../models/case-filters-form';
import { CASE_FILTERS_FORMINITIAL_DATA } from './case-filters-form-initial-data';

export const createFiltersGroup = (): FormGroup<CaseFiltersForm> => {
  return new FormGroup<CaseFiltersForm>({
    financingType: new FormGroup({
      realEstate: new FormControl(
        CASE_FILTERS_FORMINITIAL_DATA.financingType.realEstate,
      ),
      corporate: new FormControl(
        CASE_FILTERS_FORMINITIAL_DATA.financingType.corporate,
      ),
      miscellaneous: new FormControl(
        CASE_FILTERS_FORMINITIAL_DATA.financingType.miscellaneous,
      ),
    }),
    caseType: new FormGroup({
      financing: new FormControl(
        CASE_FILTERS_FORMINITIAL_DATA.caseType.financing,
      ),
    }),
    caseStatus: new FormControl(CASE_FILTERS_FORMINITIAL_DATA.caseStatus.value),
    caseStatusTags: new FormControl(
      CASE_FILTERS_FORMINITIAL_DATA.caseStatusTags,
    ),
  });
};
