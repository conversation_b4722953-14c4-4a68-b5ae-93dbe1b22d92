import {
  Component,
  EventEmitter,
  Inject,
  Input,
  LOCALE_ID,
  OnChanges,
  Output,
  SimpleChanges,
} from '@angular/core';
import { Router } from '@angular/router';
import { BusinessCaseFacilityHelperService } from '@fincloud/core/business-case';
import { MathUtils } from '@fincloud/core/math';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { Toast } from '@fincloud/core/toast';
import { Customer } from '@fincloud/swagger-generator/authorization-server';
import {
  ApplicationOrInvitationType,
  ApplicationState,
  InvitationStatus,
  Locale,
} from '@fincloud/types/enums';
import { Dictionary, TableRow } from '@fincloud/types/models';
import { FinToastService } from '@fincloud/ui/toast';
import { BUSINESS_CASE_TYPE_LABELS } from '@fincloud/utils';
import { orderBy, uniqBy } from 'lodash-es';
import { of } from 'rxjs';
import { ApplicationAndInvitationBusinessCase } from '../../models/application-and-invitation-business-case';
import { ApplicationAndInvitationTableRow } from '../../models/application-and-invitation-table-row';
import { getColumnsConfigForInvitationTable } from '../../utils/get-columns-config-for-invitation-table';
import { getDefaultSort } from '../../utils/get-default-sort';

@Component({
  selector: 'app-applications-and-invitations-list',
  templateUrl: './applications-and-invitations-list.component.html',
  styleUrls: ['./applications-and-invitations-list.component.scss'],
})
export class ApplicationsAndInvitationsListComponent implements OnChanges {
  @Input()
  applicationsAndInvitationsBusinessCases: ApplicationAndInvitationBusinessCase[];
  @Input() customerKeyNames: Dictionary<Customer>;
  @Input() customerKey: string;
  @Input() customer: Customer;
  @Input() isPlatformManager: boolean;

  @Output() rowSelected = new EventEmitter<{ status: string }>();

  selectedBusinessCaseId: string;
  selectedBusinessCase: TableRow;
  rows: ApplicationAndInvitationTableRow[];
  columns = getColumnsConfigForInvitationTable(
    this.locale,
    this.regionalSettings,
  );
  defaultSort = getDefaultSort();

  emptyStateMessage$ = of(
    $localize`:@@cases.filters.noResults:Keine darstellbaren Informationen vorhanden`,
  );

  constructor(
    private router: Router,
    private businessCaseHelperService: BusinessCaseFacilityHelperService,
    private finToastService: FinToastService,
    @Inject(LOCALE_ID) private locale: Locale,
    private regionalSettings: RegionalSettingsService,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (
      (changes?.applicationsAndInvitationsBusinessCases?.currentValue ||
        changes?.customerKeyNames?.currentValue) &&
      this.applicationsAndInvitationsBusinessCases
    ) {
      this.generateRows();
    }
  }

  generateRows() {
    const dedupedInvitationsAndApplications = uniqBy(
      orderBy(
        this.applicationsAndInvitationsBusinessCases ?? [],
        (a) => {
          if (a.application) {
            return a.application.startedOn;
          }
          return a.invitation.creationDate;
        },
        'desc',
      ),
      (a) => a.businessCase.id,
    );

    const applications = dedupedInvitationsAndApplications
      .filter((res) => res.application)
      .map((ac) => {
        let customerName =
          this.customerKeyNames[
            ac.businessCase.participants?.find((p) => p.lead)?.customerKey
          ]?.name ?? '';

        if (ac.businessCase.leadCustomerKey === this.customer.key) {
          customerName = this.customer.name;
        }

        return {
          id: ac.businessCase.id,
          autoGeneratedBusinessCaseName:
            ac.businessCase.autoGeneratedBusinessCaseName,
          customerName: customerName,
          financingProduct:
            ac.businessCase.information?.financingProduct?.value,
          companyName: ac.businessCase.company?.companyInfo?.legalName,
          financingVolume: MathUtils.getSafeValue(
            this.businessCaseHelperService.getFinancingVolume(ac.businessCase),
          ),
          date: ac.businessCase.lastModifiedDate,
          status: ac.application.state,
          caseType: ac.businessCase?.businessCaseType,
          applicationOrInvitationType: ApplicationOrInvitationType.APPLICATION,
        };
      });

    const invitations = dedupedInvitationsAndApplications
      .filter((res) => res.invitation)
      .map((ic) => {
        return {
          applicationId: ic.invitation.applicationId,
          id: ic.businessCase?.id,
          autoGeneratedBusinessCaseName:
            ic.businessCase?.autoGeneratedBusinessCaseName,
          customerName:
            this.customerKeyNames?.[
              ic.businessCase?.participants?.find((p) => p.lead)?.customerKey
            ]?.name ?? '',
          financingProduct:
            ic.businessCase?.information?.financingProduct?.value,
          companyName: ic.businessCase?.company?.companyInfo?.legalName,
          financingVolume: MathUtils.getSafeValue(
            this.businessCaseHelperService.getFinancingVolume(ic.businessCase),
          ),
          date: ic.businessCase?.lastModifiedDate,
          status: ic.invitation?.invitationStatus,
          caseType: ic.businessCase?.businessCaseType,
          applicationOrInvitationType: ApplicationOrInvitationType.INVITATION,
        };
      });

    this.rows = [...applications, ...invitations];
  }

  onRowSelected(row: TableRow) {
    this.selectedBusinessCase = row;
    this.selectedBusinessCaseId = row.id as string;
  }

  onRowClicked(shouldExecuteOnClick: boolean) {
    if (!this.selectedBusinessCase && !this.selectedBusinessCaseId) {
      return;
    }

    if (
      this.selectedBusinessCase.status ===
        ApplicationState.REMOVED_FROM_BUSINESS_CASE ||
      (!this.isPlatformManager &&
        this.selectedBusinessCase.status === ApplicationState.ACCEPTED)
    ) {
      this.finToastService.show(
        Toast.error(
          $localize`:@@privateBusinessCase.toast.genericError:Vorgang kann wegen fehlender Informationen oder Berechtigungen nicht fortgesetzt werden.`,
        ),
      );

      this.resetBusinessCaseSelection();

      return;
    }

    if (this.selectedBusinessCase.status === InvitationStatus.DECLINED) {
      this.finToastService.show(
        Toast.error(
          $localize`:@@privateBusinessCase.toast.genericError:Vorgang kann wegen fehlender Informationen oder Berechtigungen nicht fortgesetzt werden.`,
        ),
      );

      this.resetBusinessCaseSelection();
    } else if (shouldExecuteOnClick) {
      void this.router.navigate([
        this.customerKey,
        'business-case',
        this.selectedBusinessCaseId,
      ]);

      this.resetBusinessCaseSelection();
    }
  }

  resetBusinessCaseSelection(): void {
    this.selectedBusinessCase = null;
    this.selectedBusinessCaseId = null;
  }

  getCaseTypeLabel(caseType: 'FINANCING_CASE') {
    return BUSINESS_CASE_TYPE_LABELS[caseType];
  }
}
