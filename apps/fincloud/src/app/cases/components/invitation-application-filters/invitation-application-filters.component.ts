import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';

import { BusinessCaseType } from '@fincloud/types/enums';
import { FinButtonAppearance } from '@fincloud/ui/button';
import { FinSize } from '@fincloud/ui/types';
import { isEqual } from 'lodash-es';
import {
  BehaviorSubject,
  defer,
  distinctUntilChanged,
  filter,
  map,
  startWith,
  switchMap,
} from 'rxjs';
import { InviationApplicationFilters } from '../../models/invitation-application-case-filters';
import { InvitationApplicationFiltersForm } from '../../models/invitation-application-filters-form';
import { APPLICATION_INVITATION_STATUS_OPTIONS } from '../../utils/application-invitation-status-options';
import { createInvitationApplicationFiltersGroup } from '../../utils/create-invitation-application-filters-group';
import { INVITATION_APPLICATION_FORM_INITIAL_DATA } from '../../utils/invitation-application-filters-form-initial-data';
import { INVITATION_FORM_INITIAL_DATA } from '../../utils/invitation-filters-form-initial-data';
import { INVITATION_STATUS_OPTIONS } from '../../utils/invitation-status-options';

@Component({
  selector: 'app-invitation-application-filters',
  templateUrl: './invitation-application-filters.component.html',
  styleUrls: ['./invitation-application-filters.component.scss'],
})
export class InvitationApplicationFiltersComponent implements OnInit {
  @Input() isInvitations: boolean;
  @Input() isRealEstateCorporate = false;

  @Output() closeFilters = new EventEmitter();

  @Output()
  filtersChange = defer(() =>
    this.initiated$.pipe(
      filter(Boolean),
      switchMap(() =>
        this.filtersGroup.valueChanges.pipe(startWith(this.filtersGroup.value)),
      ),
      map((f) => {
        const caseTypes = [];
        caseTypes.push(BusinessCaseType.FINANCING_CASE);

        return <InviationApplicationFilters>{
          businessCaseTypes: caseTypes,
          applicationsAndInvitations: f.invitationsAndApplications,
        };
      }),
      distinctUntilChanged(isEqual),
    ),
  );

  filtersGroup: FormGroup<InvitationApplicationFiltersForm>;
  initiated$ = new BehaviorSubject<boolean>(false);

  invitationStatusOptions = INVITATION_STATUS_OPTIONS;
  applicationInvitationStatusOptions = APPLICATION_INVITATION_STATUS_OPTIONS;
  finSize = FinSize;
  finButtonAppearance = FinButtonAppearance;

  ngOnInit(): void {
    this.filtersGroup = this.isInvitations
      ? createInvitationApplicationFiltersGroup(INVITATION_FORM_INITIAL_DATA)
      : createInvitationApplicationFiltersGroup(
          INVITATION_APPLICATION_FORM_INITIAL_DATA,
        );
    this.initiated$.next(true);
  }

  close() {
    this.closeFilters.emit();
  }
}
