import {
  Component,
  EventEmitter,
  Inject,
  Input,
  LOCALE_ID,
  OnChanges,
  Output,
  SimpleChanges,
} from '@angular/core';
import { Router } from '@angular/router';
import { FluidTableColumn, Page } from '@fincloud/components/lists';
import { BusinessCaseModelService } from '@fincloud/core/business-case';
import { MathUtils } from '@fincloud/core/math';
import { Customer } from '@fincloud/swagger-generator/authorization-server';
import { SearchBusinessCaseResultDto } from '@fincloud/swagger-generator/exchange';
import {
  BusinessCaseState,
  CustomerType,
  FinancingRepaidState,
  Locale,
} from '@fincloud/types/enums';
import {
  BusinessCaseTableRow,
  Dictionary,
  FluidTableSorting,
  TableRow,
} from '@fincloud/types/models';
import {
  BUSINESS_CASE_TYPE_LABELS,
  PERCEPTION_TRANSLATIONS_MAP,
} from '@fincloud/utils';
import { FINANCING_TYPES } from '../../utils/financing-types';
import { getBankAndFspBusinessCaseTableColumnsConfig } from '../../utils/get-bank-and-fsp-business-case-table-columns-config';
import { getDefaultSort } from '../../utils/get-default-sort';
import { getRealEstateAndCorporateBusinessCaseTableColumnsConfig } from '../../utils/get-real-estate-and-corporate-business-case-table-columns-config';

@Component({
  selector: 'app-business-case-list',
  templateUrl: './business-case-list.component.html',
  styleUrls: ['./business-case-list.component.scss'],
})
export class BusinessCaseListComponent implements OnChanges {
  @Input() businessCases: SearchBusinessCaseResultDto[];
  @Input() customerKeyNames: Dictionary<Customer>;
  @Input() customerType: CustomerType;
  @Input() customerKey: string;
  @Input() customer: Customer;
  @Input() defaultSort = getDefaultSort();

  @Output() sortChange = new EventEmitter();
  @Output() pageChange = new EventEmitter();

  rows: BusinessCaseTableRow[];
  columns: FluidTableColumn[];

  @Input() limit = 1;
  @Input() totalResults = 0;
  @Input() offset = 0;

  tableOffset = this.offset;
  selectedBusinessCaseId: string;
  caseLabels: any = BUSINESS_CASE_TYPE_LABELS;
  financingTypes = FINANCING_TYPES;
  caseStatusTranslations = PERCEPTION_TRANSLATIONS_MAP;

  constructor(
    private router: Router,
    private businessCaseModelService: BusinessCaseModelService,
    @Inject(LOCALE_ID) private locale: Locale,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (this.businessCases && this.customerKeyNames && this.customerType) {
      this.buildColumns();
      this.generateRows();
    }

    if (changes?.offset?.currentValue !== changes?.offset?.previousValue) {
      this.tableOffset = Math.floor(this.offset / this.limit);
    }
  }

  isActiveCaseStatus(row: BusinessCaseTableRow): boolean {
    return (
      row.state !== BusinessCaseState.INACTIVE_COMPLETED &&
      row.state !== BusinessCaseState.INACTIVE_CANCELLED
    );
  }

  remapCaseStatus(row: SearchBusinessCaseResultDto): string {
    if (
      row.state === BusinessCaseState.INACTIVE_COMPLETED &&
      row.caseParticipantPerception === FinancingRepaidState.FINANCING_REPAID
    ) {
      return this.caseStatusTranslations.get(
        FinancingRepaidState.FINANCING_REPAID_INACTIVE,
      );
    } else if (
      row.state !== BusinessCaseState.INACTIVE_COMPLETED &&
      row.caseParticipantPerception === FinancingRepaidState.FINANCING_REPAID
    ) {
      return this.caseStatusTranslations.get(
        FinancingRepaidState.FINANCING_REPAID_ACTIVE,
      );
    }

    return this.caseStatusTranslations.get(row.caseParticipantPerception);
  }

  buildColumns() {
    if (
      this.customerType === CustomerType.BANK ||
      this.customerType === CustomerType.FSP
    ) {
      this.columns = getBankAndFspBusinessCaseTableColumnsConfig(this.locale);
    }

    if (
      this.customerType === CustomerType.REAL_ESTATE ||
      this.customerType === CustomerType.CORPORATE
    ) {
      this.columns = getRealEstateAndCorporateBusinessCaseTableColumnsConfig(
        this.locale,
      );
    }
  }

  generateRows() {
    this.rows = this.businessCases.map((c) => {
      let customerName =
        this.customerKeyNames[c.participants?.find((p) => p.lead)?.customerKey]
          ?.name ?? '';

      if (c.leadCustomerKey === this.customer.key) {
        customerName = this.customer.name;
      }

      return {
        id: c.id,
        autoGeneratedBusinessCaseName: c.autoGeneratedBusinessCaseName,
        customerName: customerName,
        financingProduct: c?.information?.financingProduct?.value,
        companyName: c?.company?.companyInfo?.legalName,
        financingVolume: MathUtils.getSafeValue(
          this.businessCaseModelService.getFinancingVolume(c),
        ),
        lastModifiedDate: c?.lastModifiedDate,
        caseType: c?.businessCaseType,
        state: c?.state,
        caseStatus: this.remapCaseStatus(c),
        financingStructureType:
          c?.structuredFinancingConfiguration.financingStructureType,
      };
    });
  }

  onRowSelected(row: TableRow) {
    this.selectedBusinessCaseId = row.id as string;
  }

  onRowClicked(shouldExecuteOnClick: boolean) {
    if (shouldExecuteOnClick) {
      void this.router.navigate([
        this.customerKey,
        'business-case',
        this.selectedBusinessCaseId,
      ]);
    }
  }

  onSort(event: FluidTableSorting) {
    const sortParams = { ...event };
    sortParams.prop = this.columns.find(
      (c) => c.prop.toLowerCase() === event?.prop.toLocaleLowerCase(),
    )?.prop;

    this.sortChange.emit(sortParams);
  }

  onPage(event: Page) {
    this.pageChange.emit(event);
  }
}
