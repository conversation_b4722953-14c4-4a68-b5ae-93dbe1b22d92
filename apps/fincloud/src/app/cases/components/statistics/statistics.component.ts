import { DecimalPipe, KeyValue } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnChanges,
} from '@angular/core';
import { CustomerBusinessCasesStatisticsDto } from '@fincloud/swagger-generator/exchange';
import { IconName } from '@fincloud/types/models';
import {
  BUSINESS_CASE_TYPE_INVERSE_ICONS,
  BUSINESS_CASE_TYPE_LABELS,
} from '@fincloud/utils';

@Component({
  selector: 'app-statistics',
  templateUrl: './statistics.component.html',
  styleUrls: ['./statistics.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StatisticsComponent implements OnChanges {
  @Input() stats: CustomerBusinessCasesStatisticsDto;
  activeCasesIcons: IconName[] = [];
  closedCasesIcons: IconName[] = [];
  caseLabels: Record<string, string> = BUSINESS_CASE_TYPE_LABELS;

  constructor(private decimalPipe: DecimalPipe) {}

  ngOnChanges(): void {
    this.activeCasesIcons = Object.keys(
      this.stats?.totalActiveCases?.distribution ?? [],
    )
      .map((key) => BUSINESS_CASE_TYPE_INVERSE_ICONS[key])
      .filter(Boolean);
    this.closedCasesIcons = Object.keys(
      this.stats?.totalClosedCases?.distribution ?? [],
    )
      .map((key) => BUSINESS_CASE_TYPE_INVERSE_ICONS[key])
      .filter(Boolean);
  }

  internationalizationTooltip(statistic: KeyValue<string, never>) {
    if (statistic.value > 1) {
      return (
        this.caseLabels[statistic.key] +
        ' - ' +
        this.decimalPipe.transform(statistic.value) +
        ' ' +
        $localize`:@@statistic.tooltip.case:  Fälle`
      );
    } else {
      return this.caseLabels[statistic.key];
    }
  }
}
