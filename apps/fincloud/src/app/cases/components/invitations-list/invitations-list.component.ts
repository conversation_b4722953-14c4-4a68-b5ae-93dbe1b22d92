import {
  Component,
  EventEmitter,
  Inject,
  Input,
  LOCALE_ID,
  OnChanges,
  Output,
  SimpleChanges,
} from '@angular/core';
import { Router } from '@angular/router';
import { BusinessCaseFacilityHelperService } from '@fincloud/core/business-case';
import { MathUtils } from '@fincloud/core/math';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { Toast } from '@fincloud/core/toast';
import { Customer } from '@fincloud/swagger-generator/authorization-server';
import {
  ApplicationOrInvitationType,
  ApplicationState,
  Locale,
} from '@fincloud/types/enums';
import {
  Dictionary,
  InvitationBusinessCase,
  TableRow,
} from '@fincloud/types/models';
import { FinToastService } from '@fincloud/ui/toast';
import { BUSINESS_CASE_TYPE_LABELS } from '@fincloud/utils';
import { of } from 'rxjs';
import { InvitationTableRow } from '../../models/invitation-table-row';
import { getColumnsConfig } from '../../utils/get-columns-config';
import { getDefaultSort } from '../../utils/get-default-sort';
import { getRealEstateAndCorporateInvitationsTableColumnsConfig } from '../../utils/get-real-estate-and-corporate-invitations-table-columns-config';

@Component({
  selector: 'app-invitations-list',
  templateUrl: './invitations-list.component.html',
  styleUrls: ['./invitations-list.component.scss'],
})
export class InvitationsListComponent implements OnChanges {
  @Input() invitationBusinessCases: InvitationBusinessCase[];
  @Input() customerKeyNames: Dictionary<Customer>;
  @Input() customerKey: string;
  @Input() customer: Customer;
  @Input() isRealEstateCorporate: boolean;

  @Output() rowSelected = new EventEmitter<{ status: string }>();

  rows: InvitationTableRow[];
  columns = getColumnsConfig(this.locale, this.regionalSettings);
  defaultSort = getDefaultSort();
  toastErrorText = $localize`:@@privateBusinessCase.toast.genericError:Vorgang kann wegen fehlender Informationen oder Berechtigungen nicht fortgesetzt werden.`;

  selectedBusinessCase: TableRow;
  selectedBusinessCaseId: string;

  emptyStateMessage$ = of(
    $localize`:@@cases.filters.noResults:Keine darstellbaren Informationen vorhanden`,
  );

  constructor(
    private router: Router,
    private businessCaseHelperService: BusinessCaseFacilityHelperService,
    private finToastService: FinToastService,
    @Inject(LOCALE_ID) private locale: Locale,
    private regionalSettings: RegionalSettingsService,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (
      (changes?.invitationBusinessCases?.currentValue ||
        changes?.customerKeyNames?.currentValue) &&
      this.invitationBusinessCases
    ) {
      this.generateRows();
    }

    if (changes.isRealEstateCorporate?.currentValue) {
      this.columns = getRealEstateAndCorporateInvitationsTableColumnsConfig(
        this.locale,
        this.regionalSettings,
      );
    }
  }

  generateRows() {
    this.rows = this.invitationBusinessCases.map((ic) => {
      let customerName =
        this.customerKeyNames[
          ic.businessCase.participants?.find((p) => p.lead)?.customerKey
        ]?.name ?? '';

      if (ic.businessCase.leadCustomerKey === this.customer.key) {
        customerName = this.customer.name;
      }

      return {
        id: ic.businessCase?.id,
        autoGeneratedBusinessCaseName:
          ic.businessCase?.autoGeneratedBusinessCaseName,
        customerName: customerName,
        financingProduct: ic.businessCase?.information?.financingProduct?.value,
        companyName: ic.businessCase?.company?.companyInfo?.legalName,
        financingVolume: MathUtils.getSafeValue(
          this.businessCaseHelperService.getFinancingVolume(ic.businessCase),
        ),
        creationDate: ic.invitation?.creationDate,
        status: ic.invitation?.invitationStatus,
        caseType: ic.businessCase?.businessCaseType,
        applicationOrInvitationType: ApplicationOrInvitationType.INVITATION,
        date: new Date(ic.invitation.creationDate).getTime(),
      };
    });
  }

  handleEventEmmision(status: string) {
    if (['CANCELED', 'DECLINED', 'ACCEPTED'].includes(status)) {
      this.finToastService.show(Toast.error(this.toastErrorText));

      return setTimeout(() => this.rowSelected.emit({ status }), 2000);
    }

    return this.rowSelected.emit({ status });
  }

  onRowSelected(row: TableRow) {
    this.selectedBusinessCase = row;
    this.selectedBusinessCaseId = row.id as string;
  }

  onRowClicked(shouldExecuteOnClick: boolean) {
    if (!this.selectedBusinessCase && !this.selectedBusinessCaseId) {
      return;
    }

    if (
      this.selectedBusinessCase.status ===
      ApplicationState.REMOVED_FROM_BUSINESS_CASE
    ) {
      this.finToastService.show(
        Toast.error(
          $localize`:@@privateBusinessCase.toast.genericError:Vorgang kann wegen fehlender Informationen oder Berechtigungen nicht fortgesetzt werden.`,
        ),
      );

      this.resetBusinessCaseSelection();

      return;
    }

    if (
      ['CANCELED', 'DECLINED', 'EXPIRED'].includes(
        this.selectedBusinessCase.status as string,
      )
    ) {
      this.handleEventEmmision(this.selectedBusinessCase.status as string);
      this.resetBusinessCaseSelection();
    } else if (shouldExecuteOnClick) {
      this.router.navigate([
        this.customerKey,
        'business-case',
        this.selectedBusinessCaseId,
      ]);

      this.resetBusinessCaseSelection();
    }
  }

  resetBusinessCaseSelection(): void {
    this.selectedBusinessCase = null;
    this.selectedBusinessCaseId = null;
  }

  getCaseTypeLabel(caseType: 'FINANCING_CASE') {
    return BUSINESS_CASE_TYPE_LABELS[caseType];
  }
}
