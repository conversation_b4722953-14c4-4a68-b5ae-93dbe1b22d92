import { Component, DestroyRef, Input, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { ApplicationInvitationStatus } from '@fincloud/core/business-case';
import { ValueOf } from '@fincloud/core/types';
import { APPLICATION_STATUS_NAME } from '@fincloud/neoshare/business-case';
import { INVITATION_STATUS_NAME } from '@fincloud/neoshare/business-case-collaboration';
import { selectCustomer } from '@fincloud/state/customer';
import {
  BusinessCaseType,
  CustomerStatus,
  CustomerType,
} from '@fincloud/types/enums';
import { AppState, MultiselectOption } from '@fincloud/types/models';
import {
  BUSINESS_CASE_TYPE_ICONS,
  BUSINESS_CASE_TYPE_LABELS,
} from '@fincloud/utils';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { isEqual } from 'lodash-es';
import { of } from 'rxjs';
import { FiltersModalResult } from '../../models/filters-modal-result';
import { StatusGroups } from '../../models/status-groups';

@Component({
  selector: 'app-filters-modal',
  templateUrl: './filters-modal.component.html',
  styleUrls: ['./filters-modal.component.scss'],
})
export class FiltersModalComponent implements OnInit {
  @Input()
  activeTab: string;

  @Input()
  currentStatus: StatusGroups;

  @Input()
  currentCases: BusinessCaseType[];

  @Input()
  currentInvitationFilters: MultiselectOption<ApplicationInvitationStatus>[];

  @Input()
  currentApplicationFilters: MultiselectOption<ApplicationInvitationStatus>[];

  @Input()
  currentApplicationAndInvitationFilters: MultiselectOption<ApplicationInvitationStatus>[];

  BusinessCaseLabels: Record<string, string> = BUSINESS_CASE_TYPE_LABELS;
  BusinessCaseIcons = BUSINESS_CASE_TYPE_ICONS;

  selectedCases: BusinessCaseType[] = [];
  selectedStatus: StatusGroups;
  selectedInvitationStatuses: MultiselectOption<ApplicationInvitationStatus>[] =
    [];
  selectedApplicationStatuses: MultiselectOption<ApplicationInvitationStatus>[] =
    [];
  selectedApplicationAndInvitationStatuses: MultiselectOption<ApplicationInvitationStatus>[] =
    [];
  filtersEmptyState: unknown = {
    status: null,
    cases: [],
    invitations: [],
    applications: [],
    applicationAndInvitations: [],
  };

  customerType: CustomerType;
  customerStatus: CustomerStatus;
  focused$ = of(true);
  statusOptions: Record<'label' | 'value', string>[] = [
    {
      label: $localize`:@@cases.filter.modal.options.active:Aktiv`,
      value: 'active',
    },
    {
      label: $localize`:@@cases.filter.modal.options.completed:Abgeschlossen`,
      value: 'completed',
    },
  ];

  private caseOptionsCache: {
    label: ValueOf<typeof BUSINESS_CASE_TYPE_LABELS>;
    value: BusinessCaseType;
  }[] = [
    {
      label: BUSINESS_CASE_TYPE_LABELS[BusinessCaseType.FINANCING_CASE],
      value: BusinessCaseType.FINANCING_CASE,
    },
  ];
  caseOptions = this.caseOptionsCache;

  invitationStatusFilters: MultiselectOption<ApplicationInvitationStatus>[] = [
    { label: INVITATION_STATUS_NAME.PENDING, value: 'PENDING' },
    { label: INVITATION_STATUS_NAME.ACCEPTED, value: 'ACCEPTED' },
    { label: INVITATION_STATUS_NAME.CANCELED, value: 'CANCELED' },
    { label: INVITATION_STATUS_NAME.DECLINED, value: 'DECLINED' },
  ];

  applicationAndInvitationStatusFilters: MultiselectOption<ApplicationInvitationStatus>[] =
    [
      { label: INVITATION_STATUS_NAME.RECEIVE, value: 'PENDING' },
      { label: INVITATION_STATUS_NAME.CANCELED, value: 'CANCELED' },
      { label: INVITATION_STATUS_NAME.DECLINED, value: 'DECLINED' },
      { label: APPLICATION_STATUS_NAME.SUBMITTED, value: 'SUBMITTED' },
      { label: APPLICATION_STATUS_NAME.CANCELLED, value: 'CANCELLED' },
      { label: APPLICATION_STATUS_NAME.REJECTED, value: 'REJECTED' },
      { label: APPLICATION_STATUS_NAME.ACCEPTED, value: 'ACCEPTED' },
    ];

  get showCaseFilter(): boolean {
    const isGuestCustomerOnInvitationsTab =
      this.customerStatus === CustomerStatus.GUEST &&
      this.customerType === CustomerType.BANK &&
      this.activeTab === 'invitations';
    const isRegularCustomerUser =
      this.customerType === CustomerType.BANK &&
      this.customerStatus === CustomerStatus.REGULAR;
    return isRegularCustomerUser || isGuestCustomerOnInvitationsTab;
  }

  constructor(
    private destroyRef: DestroyRef,
    private activeModal: NgbActiveModal,
    private store: Store<AppState>,
  ) {}

  ngOnInit(): void {
    this.selectedStatus = this.currentStatus;
    this.selectedCases = this.currentCases;
    this.selectedInvitationStatuses = this.currentInvitationFilters;
    this.selectedApplicationStatuses = this.currentApplicationFilters;
    this.selectedApplicationAndInvitationStatuses =
      this.currentApplicationAndInvitationFilters;
    this.resetSelection();

    this.store
      .select(selectCustomer)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((usr) => {
        this.customerType = usr.customerType as CustomerType;
        this.customerStatus = usr.customerStatus as CustomerStatus;
      });
  }

  deselectCase(event: string) {
    this.selectedCases = this.selectedCases.filter((i) => i !== event);
    this.resetSelection();
  }

  caseSelectionChange(event: BusinessCaseType) {
    if (event) {
      this.selectedCases.push(event);
      this.resetSelection();
    }
  }

  statusSelectionChange(event: StatusGroups) {
    this.selectedStatus = event;
  }

  invitationSelectionChange(
    event: MultiselectOption<ApplicationInvitationStatus>[],
  ) {
    this.selectedInvitationStatuses = event;
  }

  applicationSelectionChange(
    event: MultiselectOption<ApplicationInvitationStatus>[],
  ) {
    this.selectedApplicationStatuses = event;
  }

  applicationAndInvitationSelectionChange(
    event: MultiselectOption<ApplicationInvitationStatus>[],
  ) {
    this.selectedApplicationAndInvitationStatuses = event;
  }

  clear(): void {
    this.activeModal.close({
      success: true,
      data: {
        filters: this.filtersEmptyState,
        filtersApplied: false,
      },
    });
  }

  save(): void {
    const filters = {
      status: this.selectedStatus ?? null,
      cases: this.selectedCases,
      invitations: this.selectedInvitationStatuses,
      applications: this.selectedApplicationStatuses,
      applicationsAndInvitations: this.selectedApplicationAndInvitationStatuses,
    };

    const filtersResult: FiltersModalResult = {
      filters,
      filtersApplied: !isEqual(filters, this.filtersEmptyState),
    };

    this.activeModal.close({
      success: true,
      data: filtersResult,
    });
  }

  private resetSelection() {
    this.caseOptions = this.caseOptionsCache.filter(
      (i) => !this.selectedCases.includes(i.value),
    );
  }
}
