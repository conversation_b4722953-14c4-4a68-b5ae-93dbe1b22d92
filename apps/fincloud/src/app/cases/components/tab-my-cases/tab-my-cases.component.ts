import { PercentPipe } from '@angular/common';
import {
  Component,
  DestroyRef,
  Input,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Page } from '@fincloud/components/lists';
import { SearchFilterComponent } from '@fincloud/components/search-filter';
import { BusinessCaseModelService } from '@fincloud/core/business-case';
import { LayoutCommunicationService } from '@fincloud/core/layout';
import { selectCustomer, selectCustomerType } from '@fincloud/state/customer';
import { selectUserCustomerKey } from '@fincloud/state/user';
import { AppState } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { isEqual, pick } from 'lodash-es';
import { BehaviorSubject, filter, map, of, shareReplay, tap } from 'rxjs';
import {
  selectCustomerKeyNames,
  selectIsMyAllCasesEmpty,
  selectMyQueryCasesData,
} from '../../+state';
import { CasesPageActions } from '../../+state/actions';
import { CaseFilters } from '../../models/case-filters';
import { CaseSort } from '../../models/case-sort';
import { DEFAULT_CASE_FILTERS } from '../../utils/default-case-filters';
import { DEFAULT_CASE_QUERY } from '../../utils/default-case-query';

@Component({
  selector: 'app-tab-my-cases',
  templateUrl: './tab-my-cases.component.html',
  styleUrls: ['./tab-my-cases.component.scss'],
  providers: [BusinessCaseModelService, PercentPipe],
})
export class TabMyCasesComponent implements OnInit {
  @Input() isRealEstateCorporate = false;

  @ViewChild('searchFilterComponent', { static: false })
  searchFilter: SearchFilterComponent;

  @ViewChild('filtersTemplate')
  set filtersTemplate(template: TemplateRef<unknown>) {
    if (template) {
      this.layoutCommunicationService.setRightOverlayPanelTemplate(template);
    }
  }

  searchFilterFocused$ = of(true);

  /* - - - - - - Customer   - - - - - - */

  customer$ = this.store
    .select(selectCustomer)
    .pipe(filter(Boolean), shareReplay(1));

  customerKey$ = this.store.select(selectUserCustomerKey);

  customerType$ = this.store.select(selectCustomerType);

  customerKeyNames$ = this.store.select(selectCustomerKeyNames);

  /* - - - - - - My Cases initialization   - - - - - - */

  myCases$ = this.store.select(selectMyQueryCasesData);
  isMyAllCasesEmpty$ = this.store.select(selectIsMyAllCasesEmpty);
  myCasesQuery$ = new BehaviorSubject<CaseFilters>(DEFAULT_CASE_QUERY);
  isMyBusinessCasesInitialized$ = this.myCases$.pipe(map((cases) => !!cases));

  /* - - - - - - Empty state   - - - - - - */

  // My business case empty is when the user has no cases
  isMyQueryCasesEmpty$ = this.myCases$.pipe(
    map((cases) => !cases?.results || cases?.results.length === 0),
  );

  // Full empty state is when user has no cases without having any filters applied
  isFullEmptyState$ = this.isMyAllCasesEmpty$.pipe(
    map(
      (isMyAllCasesEmpty) =>
        isMyAllCasesEmpty &&
        !this.hasFiltersApplied &&
        !this.hasAnySearchApplied,
    ),
  );

  emptyStateMessage$ = this.isFullEmptyState$.pipe(
    map((isFullEmptyState) =>
      isFullEmptyState
        ? $localize`:@@cases.my.fullEmptyState:Sie sind bisher an keinem Finanzierungsfall beteiligt. Sobald Sie einem Fall beitreten, wird es hier erscheinen.`
        : $localize`:@@cases.filters.noResults:Keine darstellbaren Informationen vorhanden`,
    ),
  );

  /* - - - - - - Filters   - - - - - - */

  get hasFiltersApplied() {
    const emptyFilters = Object.keys(DEFAULT_CASE_FILTERS);
    const picked = pick(this.myCasesQuery$.getValue(), emptyFilters);

    return !isEqual(picked, DEFAULT_CASE_FILTERS);
  }

  get hasAnySearchApplied() {
    return !!this.myCasesQuery$.getValue()?.searchTerm;
  }

  constructor(
    private destroyRef: DestroyRef,
    private store: Store<AppState>,
    private layoutCommunicationService: LayoutCommunicationService,
  ) {}

  ngOnInit() {
    this.myCasesQuery$
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        tap((filters) => {
          this.loadMyQueryCases(filters);
        }),
      )
      .subscribe();

    this.loadMyAllCases();
  }

  onSearchTermSelected(searchTerm: string) {
    // Reset page to 1 when search applied

    this.updateMyCasesQuery({
      searchTerm,
      ...{
        offset: 0,
      },
    });
  }

  onCaseFiltersChange(caseFilters: CaseFilters) {
    // Reset page to 1 when any filters applied

    this.updateMyCasesQuery({
      ...caseFilters,
      ...{
        offset: 0,
      },
    });
  }

  onToggleFilters() {
    this.layoutCommunicationService.toggleRightSideOverlayPanel();
  }

  onCasesSortChange(sort: CaseSort) {
    const sortMap = {
      sortBy: sort.prop,
      sortOrder: sort.dir,
    };

    this.updateMyCasesQuery({ ...sortMap });
  }

  onCasesPageChange(page: Page) {
    this.updateMyCasesQuery({ ...page });
  }

  loadMyQueryCases(filters: CaseFilters) {
    this.store.dispatch(
      CasesPageActions.loadMyQueryCases({ payload: filters }),
    );
  }

  loadMyAllCases() {
    this.store.dispatch(CasesPageActions.loadMyAllCases());
  }

  private updateMyCasesQuery(query: Partial<CaseFilters>) {
    this.myCasesQuery$.next({
      ...this.myCasesQuery$.getValue(),
      ...query,
    });
  }
}
