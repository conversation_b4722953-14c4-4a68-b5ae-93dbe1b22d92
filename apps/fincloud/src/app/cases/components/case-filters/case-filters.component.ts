import {
  Component,
  DestroyRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { FormGroup } from '@angular/forms';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  CASE_ACTIVE_PERCEPTION_TAGS,
  CASE_CLOSED_PERCEPTION_TAGS,
} from '@fincloud/neoshare/business-case';
import {
  BusinessCaseType,
  FinancingStructureType,
} from '@fincloud/types/enums';
import { AppState } from '@fincloud/types/models';
import { FinButtonAppearance } from '@fincloud/ui/button';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';
import { isEqual } from 'lodash-es';
import {
  BehaviorSubject,
  defer,
  distinctUntilChanged,
  filter,
  map,
  startWith,
  switchMap,
  tap,
} from 'rxjs';
import { CaseFilters } from '../../models/case-filters';
import { CaseFiltersForm } from '../../models/case-filters-form';
import { CASE_STATUSES } from '../../utils/case-statuses';
import { createFiltersGroup } from '../../utils/create-filters-group';

@Component({
  selector: 'app-case-filters',
  templateUrl: './case-filters.component.html',
  styleUrls: ['./case-filters.component.scss'],
})
export class CaseFiltersComponent implements OnInit {
  @Input() isRealEstateCorporate = false;

  filtersGroup: FormGroup<CaseFiltersForm>;

  @Output() closeFilters = new EventEmitter();

  @Output()
  filtersChange = defer(() =>
    this.initiated$.pipe(
      filter(Boolean),
      switchMap(() =>
        this.filtersGroup.valueChanges.pipe(startWith(this.filtersGroup.value)),
      ),
      map((f) => {
        const financingStructureTypes = [];
        const businessCaseTypes = [];

        f.financingType.corporate === true
          ? financingStructureTypes.push(FinancingStructureType.CORPORATE)
          : null;
        f.financingType.realEstate === true
          ? financingStructureTypes.push(FinancingStructureType.REAL_ESTATE)
          : null;
        f.financingType.miscellaneous === true
          ? financingStructureTypes.push(FinancingStructureType.MISCELLANEOUS)
          : null;

        businessCaseTypes.push(BusinessCaseType.FINANCING_CASE);

        return <CaseFilters>{
          caseState: f.caseStatus,
          financingStructureTypes: financingStructureTypes,
          businessCaseTypes: businessCaseTypes,
          participantCasePerceptions: f.caseStatusTags,
        };
      }),
      distinctUntilChanged(isEqual),
    ),
  );

  initiated$ = new BehaviorSubject<boolean>(false);

  caseStatuses = CASE_STATUSES;
  caseActiveStatusTags = CASE_ACTIVE_PERCEPTION_TAGS;
  caseClosedStatusTags = CASE_CLOSED_PERCEPTION_TAGS;
  finSize = FinSize;
  finButtonAppearance = FinButtonAppearance;

  constructor(
    private destroyRef: DestroyRef,
    private store: Store<AppState>,
  ) {}

  ngOnInit(): void {
    this.filtersGroup = createFiltersGroup();
    this.initiated$.next(true);

    this.filtersGroup.controls.caseStatus.valueChanges
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        tap(() => {
          this.filtersGroup.controls.caseStatusTags.setValue([]);
        }),
      )
      .subscribe();
  }

  close() {
    this.closeFilters.emit();
  }
}
