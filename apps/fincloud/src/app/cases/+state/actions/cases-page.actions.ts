import { createAction, props } from '@ngrx/store';
import { CaseFilters } from '../../models/case-filters';

export const loadMyQueryCases = createAction(
  '[Cases Page] Load my query cases',
  props<{
    payload: CaseFilters;
  }>(),
);
export const loadMyAllCases = createAction('[Cases Page] Load my all cases');
export const loadMyQueryOrganizationCases = createAction(
  '[Cases Page] Load my query organization cases',
  props<{
    payload: CaseFilters;
  }>(),
);
export const loadMyAllOrganizationCases = createAction(
  '[Cases Page] Load my all organization cases',
);
export const loadInvitations = createAction('[Cases Page] Load invitations');
export const loadApplications = createAction('[Cases Page] Load applications');
export const loadCustomerKeyNames = createAction(
  '[Cases Page] Load customer key names',
);
export const fetchNewInvitationsAndApplications = createAction(
  '[Cases Page] Fetch new invitations and applications',
);
