import { Component, DestroyRef, OnInit, ViewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { TableColumn } from '@fincloud/components/lists';
import { ConfirmationDialogComponent } from '@fincloud/components/modals';
import { SelectComponent } from '@fincloud/components/selects';
import { ModalService } from '@fincloud/core/modal';
import { selectUserCustomerKey } from '@fincloud/state/user';
import {
  CollaborationBlacklistSettingsControllerService,
  Customer,
  CustomerManagementControllerService,
  PublicCustomerResponse,
} from '@fincloud/swagger-generator/authorization-server';
import { AppState, TableRow } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { Subject, from, of } from 'rxjs';
import {
  catchError,
  debounceTime,
  filter,
  map,
  mergeMap,
  switchMap,
  take,
  tap,
  toArray,
} from 'rxjs/operators';
import { BLOCK_DIALOG_INPUTS } from '../../utils/block-dialog-inputs';
import { BLOCK_MESSAGE_PREFIX } from '../../utils/block-message-prefix';
import { BLOCK_MESSAGE_SUFFIX } from '../../utils/block-message-suffix';
import { NO_DATA_MESSAGE } from '../../utils/no-data-message';
import { COLLABORATION_TABLE_COLUMNS_CONFIG } from '../../utils/table-columns';
import { UNBLOCK_DIALOG_INPUTS } from '../../utils/unblock-dialog-inputs';
import { UNBLOCK_MESSAGE_PREFIX } from '../../utils/unblock-message-prefix';
import { UNBLOCK_MESSAGE_SUFFIX } from '../../utils/unblock-message-suffix';

@Component({
  selector: 'app-customer-collaboration',
  templateUrl: './customer-collaboration.component.html',
  styleUrls: ['./customer-collaboration.component.scss'],
})
export class CustomerCollaborationComponent implements OnInit {
  @ViewChild(SelectComponent) private selectComponent: SelectComponent;
  public columns: TableColumn[] = COLLABORATION_TABLE_COLUMNS_CONFIG;
  public rows: TableRow[] = [];
  public searchSelectFocused$ = of(true);
  public loading: boolean;
  public customerOptions: Customer[];
  public noDataMessage = NO_DATA_MESSAGE;
  public searchInput$ = new Subject<string>();
  private currentCustomerKey: string;
  private allBlockedCustomers: Customer[] = [];
  private allEffectiveCollaborationCustomers: PublicCustomerResponse[] = [];

  constructor(
    private destroyRef: DestroyRef,
    private customerManagementControllerService: CustomerManagementControllerService,
    private modalService: ModalService,
    private store: Store<AppState>,
    private collaborationBlacklistSettingsControllerService: CollaborationBlacklistSettingsControllerService,
  ) {}

  ngOnInit() {
    this.setInitRows();
    this.onSearch();
  }

  getRowClass(row: TableRow) {
    return {
      'transparent-bg': !!row.groupName,
    };
  }

  setInitRows() {
    // blocked customers
    this.store
      .select(selectUserCustomerKey)
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        tap(() => (this.loading = true)),
        tap((customerKey) => (this.currentCustomerKey = customerKey)),
        switchMap((currentKey) =>
          this.collaborationBlacklistSettingsControllerService
            .getCollaborationBlacklistedCustomersForGivenCustomer({
              customerKey: currentKey,
            })
            .pipe(
              mergeMap((customerKeys) => {
                return from(customerKeys).pipe(
                  mergeMap((customerKey) =>
                    this.customerManagementControllerService.getCustomerByKeyPublic(
                      {
                        customerKey,
                      },
                    ),
                  ),
                  toArray(),
                );
              }),
              catchError(() => {
                return of();
              }),
            ),
        ),
      )
      .subscribe((customers) => {
        this.rows = customers as unknown as TableRow[];
        this.allBlockedCustomers = customers;
        this.loading = false;
      });
    // effective collaboration customers
    this.store
      .select(selectUserCustomerKey)
      .pipe(
        filter(Boolean),
        take(1),
        switchMap((userCustomerKey) => {
          return this.collaborationBlacklistSettingsControllerService
            .getEffectiveCustomers({
              customerKey: userCustomerKey,
            })
            .pipe(
              catchError((err) => {
                console.error(err?.message);
                return of(null);
              }),
            );
        }),
      )
      .subscribe((effectiveCustomers) => {
        this.allEffectiveCollaborationCustomers = effectiveCustomers;
      });
  }

  onUnblockCustomer(customer: Customer) {
    const title = `${UNBLOCK_MESSAGE_PREFIX} ${customer.name} ${UNBLOCK_MESSAGE_SUFFIX}`;

    this.modalService.openComponent(
      ConfirmationDialogComponent,
      {
        ...UNBLOCK_DIALOG_INPUTS,
        title,
      },
      {},
      (res) => {
        if (res.success) {
          //TODO nested subscribe
          this.collaborationBlacklistSettingsControllerService
            .removeCustomerFromBlacklistOfGivenCustomer({
              customerKey: this.currentCustomerKey,
              blacklistedCustomerKey: customer.key,
            })
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe({
              next: () => {
                const isContainCustomer =
                  this.allEffectiveCollaborationCustomers.some(
                    (e) => e.name === customer.name,
                  );
                if (!isContainCustomer) {
                  this.allEffectiveCollaborationCustomers.push(customer);
                }
                const index = this.allBlockedCustomers.indexOf(customer);
                this.allBlockedCustomers.splice(index, 1);
                this.rows = [
                  ...(this.allBlockedCustomers as unknown as TableRow[]),
                ];
              },
              error: (err) => console.error(err?.message),
            });
        }
      },
    );
  }

  onBlockCustomer(customer: Customer) {
    const title = `${BLOCK_MESSAGE_PREFIX} ${customer.name} ${BLOCK_MESSAGE_SUFFIX}`;

    this.modalService.openComponent(
      ConfirmationDialogComponent,
      {
        ...BLOCK_DIALOG_INPUTS,
        title,
      },
      {},
      (res) => {
        if (res.success) {
          this.collaborationBlacklistSettingsControllerService
            .addCustomerToBlacklistOfGivenCustomer({
              customerKey: this.currentCustomerKey,
              blacklistedCustomerKey: customer.key,
            })
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe({
              next: () => {
                this.allBlockedCustomers.push(customer);
                this.rows = [
                  ...(this.allBlockedCustomers as unknown as TableRow[]),
                ];
              },
              error: (err) => console.error(err?.message),
            });
          this.selectComponent.clearSelect();
        }
      },
    );
  }

  onCustomerSelected() {
    this.customerOptions = [];
  }

  onSelectionChanged(searchedCustomer: string) {
    if (searchedCustomer) {
      this.onBlockCustomer(
        this.customerOptions.find(
          (customer) => customer?.key === searchedCustomer,
        ),
      );
    }
  }

  onSearch() {
    this.searchInput$
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        filter((e) => !!e && e.length > 0),
        debounceTime(500),
        map((searchTemp) =>
          this.allEffectiveCollaborationCustomers.filter((customer) =>
            customer.name
              .toLocaleLowerCase()
              .includes(searchTemp.toLocaleLowerCase()),
          ),
        ),
      )
      .subscribe((customers) => {
        this.customerOptions = this.removeBlockedCustomers(
          customers,
          this.allBlockedCustomers,
        );
      });
  }

  removeBlockedCustomers(
    customers: Customer[],
    allBlockedCustomers: Customer[],
  ): Customer[] {
    return customers.filter(
      (customer) =>
        !allBlockedCustomers.some(
          (blockedCustomer) => blockedCustomer.id === customer.id,
        ),
    );
  }
}
