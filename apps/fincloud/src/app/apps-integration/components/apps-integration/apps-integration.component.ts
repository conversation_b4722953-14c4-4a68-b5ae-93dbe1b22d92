import { ChangeDetectionStrategy, Component } from '@angular/core';
import { Router } from '@angular/router';
import { selectIsUserAdmin, selectUserCustomerKey } from '@fincloud/state/user';
import { AppState } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { BehaviorSubject } from 'rxjs';
import { AppIntegrationStep } from '../../enums/app-integration-step';
import { AppIntegrationUrls } from '../../enums/app-integration-url';
import { APP_CARDS_CONFIG } from '../../utils/cards';

@Component({
  selector: 'app-apps-integration',
  templateUrl: './apps-integration.component.html',
  styleUrls: ['./apps-integration.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppsIntegrationComponent {
  isUserAdmin$ = this.store.select(selectIsUserAdmin);

  appCardsConfig = APP_CARDS_CONFIG;

  appIntegrationStep = AppIntegrationStep;

  customerKey$ = this.store.select(selectUserCustomerKey);

  step$ = new BehaviorSubject<AppIntegrationStep>(
    AppIntegrationStep.APP_CARDS_LAYLOUT,
  );

  constructor(
    private store: Store<AppState>,
    private router: Router,
  ) {}

  selectApp(data: { step: number; urlKey: string }, customerKey: string) {
    let url: string;

    switch (data.urlKey) {
      case 'nextfolder':
        url = AppIntegrationUrls.NEXTFOLDER;
        break;
      case 'dracoon':
        url = AppIntegrationUrls.DRACOON;
        break;
      case 'coreBanking':
        url = AppIntegrationUrls.CORE_BANKING;
        break;
    }

    this.router.navigate([customerKey, 'apps-integration', url], {
      skipLocationChange: false,
    });

    this.step$.next(data.step);
  }
}
