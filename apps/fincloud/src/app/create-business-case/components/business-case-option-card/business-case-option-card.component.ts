import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { BusinessCaseType } from '@fincloud/types/enums';
import { IconName } from '@fincloud/types/models';
import {
  BUSINESS_CASE_TYPE_ICONS,
  BUSINESS_CASE_TYPE_LABELS,
} from '@fincloud/utils';

@Component({
  selector: 'app-business-case-option-card',
  templateUrl: './business-case-option-card.component.html',
  styleUrls: ['./business-case-option-card.component.scss'],
})
export class BusinessCaseOptionCardComponent implements OnInit {
  @Input()
  type: BusinessCaseType;

  @Input()
  selected = false;

  @Input()
  disabled = false;

  @Output()
  cardSelected = new EventEmitter<BusinessCaseType>();

  iconName: IconName;
  caseLabel: string;
  cardContent: string;

  ngOnInit(): void {
    this.cardContent = $localize`:@@businessCase.optionCard.financingCase.cardContent:<PERSON>e können neue als auch bestehende Finanzierungsfälle anlegen.`;

    this.caseLabel = BUSINESS_CASE_TYPE_LABELS[this.type];
    this.iconName = BUSINESS_CASE_TYPE_ICONS[this.type];
  }

  onCardSelect() {
    this.cardSelected.emit(this.type);
  }
}
