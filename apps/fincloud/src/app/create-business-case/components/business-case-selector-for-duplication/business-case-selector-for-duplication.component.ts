import {
  Component,
  EventEmitter,
  Inject,
  Input,
  LOCALE_ID,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { Router } from '@angular/router';
import { FluidTableComponent } from '@fincloud/components/lists';
import { ConfirmationDialogComponent } from '@fincloud/components/modals';
import { BusinessCaseFacilityHelperService } from '@fincloud/core/business-case';
import { ModalService } from '@fincloud/core/modal';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { Toast } from '@fincloud/core/toast';
import { LIMITLESS_PAGING } from '@fincloud/core/utils';
import { CreationStep } from '@fincloud/neoshare/base-create-update-business-case';
import { StateLibBusinessCaseRealEstatePageActions } from '@fincloud/state/business-case-real-estate';
import { selectIsPlatformManager, selectUserId } from '@fincloud/state/user';
import { ParticipantControllerService } from '@fincloud/swagger-generator/business-case-manager';
import {
  BusinessCaseResultDto,
  ExchangeService,
} from '@fincloud/swagger-generator/exchange';
import { Locale } from '@fincloud/types/enums';
import { AppState, BusinessCase, TableRow } from '@fincloud/types/models';
import { FinToastService } from '@fincloud/ui/toast';
import { BUSINESS_CASE_TYPE_ICONS_ON_WHITE_BACKGROUND } from '@fincloud/utils';
import { Store } from '@ngrx/store';
import { flatten, pick } from 'lodash-es';
import {
  BehaviorSubject,
  EMPTY,
  Observable,
  catchError,
  map,
  switchMap,
  take,
  withLatestFrom,
} from 'rxjs';
import { DuplicateBusinessCaseTableRow } from '../../models/duplicate-business-case-table-row';
import { ALLOWED_FIELDS_TO_SEARCH } from '../../utils/allowed-fields-to-search';
import { getBusinessCaseSelectorForDuplicationTableColumnsConfig } from '../../utils/get-business-case-selector-for-duplication-table-columns-config';
import { getDefaultSort } from '../../utils/get-default-sort';
import { CancelBusinessCaseCreationModalComponent } from '../cancel-business-case-creation-modal/cancel-business-case-creation-modal.component';

@Component({
  selector: 'app-business-case-selector-for-duplication',
  templateUrl: './business-case-selector-for-duplication.component.html',
  styleUrls: ['./business-case-selector-for-duplication.component.scss'],
})
export class BusinessCaseSelectorForDuplicationComponent
  implements OnInit, OnChanges
{
  @ViewChild('table', { static: true })
  fluidTableComponent: FluidTableComponent;

  @Input() focusFilter = false;

  @Input() customerKey: string;

  @Output() sendCreationStep = new EventEmitter<CreationStep.TYPE_SELECTION>();

  get noResultsFoundAfterSearch() {
    return (
      this.rowsGenerated &&
      !this.rowsToShow?.length &&
      ((!this.myCases && this.allRows?.length) ||
        (this.myCases && this.allMyRows?.length))
    );
  }

  get noCases() {
    return (
      !this.rowsGenerated ||
      !this.allRows?.length ||
      (!this.allMyRows?.length && this.myCases)
    );
  }

  searchFilterFocused$: Observable<boolean>;
  columns = getBusinessCaseSelectorForDuplicationTableColumnsConfig(
    this.locale,
    this.regionalSettings,
  );
  defaultSort = getDefaultSort();
  rowsToShow: DuplicateBusinessCaseTableRow[];
  allRows: DuplicateBusinessCaseTableRow[] = [];
  allMyRows: DuplicateBusinessCaseTableRow[] = [];
  rowsGenerated = false;
  caseIcons = BUSINESS_CASE_TYPE_ICONS_ON_WHITE_BACKGROUND;
  selectedBusinessCase: BusinessCase;
  isBusinessCaseSelected = false;
  myCases = false;
  isPlatformManager$ = this.store.select(selectIsPlatformManager);
  private searchFilterFocused$$ = new BehaviorSubject(false);

  constructor(
    protected modalService: ModalService,
    protected router: Router,
    private exchangeService: ExchangeService,
    private businessCaseFacilityHelperService: BusinessCaseFacilityHelperService,
    private store: Store<AppState>,
    private finToastService: FinToastService,
    private participantControllerService: ParticipantControllerService,
    @Inject(LOCALE_ID) private locale: Locale,
    private regionalSettings: RegionalSettingsService,
  ) {
    this.searchFilterFocused$ = this.searchFilterFocused$$.asObservable();
  }

  ngOnChanges(data: SimpleChanges) {
    this.searchFilterFocused$$.next(!!data.focusFilter);
  }

  ngOnInit(): void {
    this.store.dispatch(
      StateLibBusinessCaseRealEstatePageActions.clearFinancialStructureCase(),
    );

    this.isPlatformManager$
      .pipe(
        switchMap((isPlatformManager) => {
          return isPlatformManager
            ? this.exchangeService
                .exchangeControllerLoadMyOrganizationBusinessCases(
                  LIMITLESS_PAGING,
                )
                .pipe(map((data) => data.results))
            : this.exchangeService
                .exchangeControllerLoadMyBusinessCases(LIMITLESS_PAGING)
                .pipe(map((data) => data.results));
        }),
        map((businessCases) =>
          businessCases.filter(
            (businessCase) => businessCase.leadCustomerKey === this.customerKey,
          ),
        ),
        withLatestFrom(this.store.select(selectUserId)),
      )
      .subscribe({
        next: ([businessCases, userId]) => {
          const myCasesOnly = businessCases.filter((bCase) =>
            flatten(
              bCase.participants.map((p) => p.users.map((u) => u.userId)),
            ).includes(userId),
          );

          this.generateRows(businessCases, this.allRows);
          this.generateRows(myCasesOnly, this.allMyRows);

          this.rowsToShow = this.allRows;
          this.rowsGenerated = true;
        },
        error: (err) => console.error(err?.message),
      });
  }

  generateRows(
    businessCases: BusinessCaseResultDto[],
    targetRows: DuplicateBusinessCaseTableRow[],
  ) {
    targetRows.push(
      ...businessCases.map((businessCase) => ({
        id: businessCase.id,
        companyName: businessCase.company?.companyInfo?.legalName,
        autogeneratedBusinessCaseName:
          businessCase.autoGeneratedBusinessCaseName
            ? businessCase.autoGeneratedBusinessCaseName
            : '',
        financingVolume:
          this.businessCaseFacilityHelperService.getFinancingVolume(
            businessCase,
          ),
        lastModifiedDate: businessCase.lastModifiedDate,
        caseType: businessCase.businessCaseType,
        businessCase: businessCase,
      })),
    );
  }

  searchByAllFields(searchTerm: string) {
    const rowsToSearch = this.myCases ? this.allMyRows : this.allRows;

    this.rowsToShow = rowsToSearch.filter((row) => {
      return Object.values(pick(row, ALLOWED_FIELDS_TO_SEARCH))
        .toString()
        .toLowerCase()
        .includes(searchTerm.toLocaleLowerCase());
    });
  }

  onBackBtnClick() {
    this.sendCreationStep.emit(CreationStep.TYPE_SELECTION);
  }

  onCancel() {
    this.modalService.openComponent<CancelBusinessCaseCreationModalComponent>(
      CancelBusinessCaseCreationModalComponent,
      {},
      { keyboard: false },
      (res) => {
        if (res.success) {
          void this.router.navigate(['/']);
        }
      },
    );
  }

  changeRowSelection(row: TableRow) {
    if (row) {
      this.selectedBusinessCase = row.businessCase as BusinessCase;
      this.isBusinessCaseSelected = true;
    }
  }

  toggleCaseSelection(showOnlyMyCases: boolean) {
    this.updateRows(showOnlyMyCases);
    this.isBusinessCaseSelected = false;
    this.selectedBusinessCase = null;
  }

  private updateRows(showOnlyMyCases: boolean) {
    this.rowsToShow = showOnlyMyCases ? this.allMyRows : this.allRows;
  }

  navigateToDuplicateCase(isBusinessCaseSelected: boolean) {
    if (isBusinessCaseSelected) {
      const isUserNotPartOfCase =
        this.allRows
          .map((bCase) => bCase.id)
          .includes(this.selectedBusinessCase.id) &&
        !this.allMyRows
          .map((bCase) => bCase.id)
          .includes(this.selectedBusinessCase.id);

      if (!isUserNotPartOfCase) {
        void this.router.navigate([
          `${this.customerKey}/business-case/${this.selectedBusinessCase?.id}/duplicate-case`,
        ]);
        return;
      }

      this.modalService.openComponent(
        ConfirmationDialogComponent,
        {
          title: $localize`:@@businessCaseSelectorForDuplication.platformManager.confirmation:Sie sind kein Mitglied des Falls ${this.selectedBusinessCase.autoGeneratedBusinessCaseName}. Bitte wählen Sie eine der folgenden Optionen:`,
          svgIcon: 'svgAddMeToCaseDuplicate',
          confirmLabel: $localize`:@@businessCaseSelectorForDuplication.button.label.confirm:Fall beitreten`,
          cancelLabel: $localize`:@@businessCaseSelectorForDuplication.button.label.cancel:Fall ansehen`,
          confirmButtonColor: 'primary',
        },
        {
          windowClass: 'confirmation-dialog',
        },
        (result) => {
          if (result.success) {
            //TODO nested subscribe
            this.store
              .select(selectUserId)
              .pipe(take(1))
              .pipe(
                switchMap((userId) => {
                  return this.participantControllerService
                    .addParticipantUserToBusinessCase({
                      businessCaseId: this.selectedBusinessCase.id,
                      userId,
                    })
                    .pipe(
                      catchError(() => {
                        this.finToastService.show(Toast.error());
                        return EMPTY;
                      }),
                    );
                }),
              )
              .subscribe(() => {
                this.router.navigate([
                  `${this.customerKey}/business-case/${this.selectedBusinessCase?.id}/duplicate-case`,
                ]);
              });
          } else {
            this.router.navigate([
              `${this.customerKey}/business-case/${this.selectedBusinessCase?.id}`,
            ]);
          }
        },
      );
    }
  }
}
