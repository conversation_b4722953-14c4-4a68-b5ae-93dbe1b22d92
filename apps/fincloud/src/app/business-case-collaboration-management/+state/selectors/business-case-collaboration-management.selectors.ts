import {
  selectBusinessCaseCompanyId,
  selectBusinessCaseId,
  selectHasAnyBusinessCasePermission,
  selectIsParticipationTypeLeader,
  selectShowExportAllowSectionInCase,
} from '@fincloud/state/business-case';
import { selectCust<PERSON>Key } from '@fincloud/state/customer';
import {
  selectUserDracconCredentials,
  selectUserNextfolderCredentials,
} from '@fincloud/state/user';
import { BusinessCasePermission } from '@fincloud/types/enums';
import { createSelector } from '@ngrx/store';
import { collaborationMenuList } from '../../utils/menu-items-list-collaboration';

export const selectNextFolderVisibility = createSelector(
  selectUserNextfolderCredentials,
  selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00035]),
  (nextFolderCredentials, hasSynchronization) => {
    return nextFolderCredentials && hasSynchronization;
  },
);

export const selectDracoonVisibility = createSelector(
  selectUserDracconCredentials,
  selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00036]),
  (dracoonCredentials, hasSynchronization) => {
    return dracoonCredentials && hasSynchronization;
  },
);

export const selectCanHaveIntegrations = createSelector(
  selectNextFolderVisibility,
  selectDracoonVisibility,
  selectShowExportAllowSectionInCase,
  (nextFolderVisibility, dracoonVisibility, coreBankingVisibility) => {
    return nextFolderVisibility || dracoonVisibility || coreBankingVisibility;
  },
);

export const selectAdministrationMenuVisibility = createSelector(
  selectIsParticipationTypeLeader,
  selectCanHaveIntegrations,
  selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00033]),
  selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00038]),
  (hasAccess, isIntegrationsVisible, isFAQVisible, isCaseSettingsVisible) => {
    return (
      hasAccess ||
      isIntegrationsVisible ||
      isFAQVisible ||
      isCaseSettingsVisible
    );
  },
);

export const selectCollaborationMenuItems = createSelector(
  selectCanHaveIntegrations,
  (isIntegrationsVisible) => {
    return collaborationMenuList(isIntegrationsVisible);
  },
);

export const selectDataExportModalView = createSelector(
  selectBusinessCaseCompanyId,
  selectBusinessCaseId,
  selectCustomerKey,
  (companyId, businessCaseId, customerKey) => ({
    businessCaseId,
    companyId,
    customerKey,
  }),
);
