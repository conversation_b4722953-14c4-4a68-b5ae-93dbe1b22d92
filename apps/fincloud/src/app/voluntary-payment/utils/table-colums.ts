import { DatePipe } from '@angular/common';
import { CircleStatusComponent } from '@fincloud/components/booleans';
import { TableColumn } from '@fincloud/components/lists';
import { Locale } from '@fincloud/types/enums';

export const invoiceTableColumnsConfig = (locale: Locale): TableColumn[] => [
  {
    prop: 'statusConfig',
    name: $localize`:@@companyAnalysis.companyInformationSections.informationSection.label:Status`,
    width: 100,
    component: CircleStatusComponent,
    inputName: 'statusConfig',
    inputClass: 'invoice-table__invoice-status',
    filterable: false,
    sortable: false,
  },
  {
    prop: 'sevdeskInvoiceNumber',
    name: $localize`:@@tableColumns.invoiceNumber:Rechnungsnummer`,
    width: 180,
    filterable: true,
    sortable: false,
  },
  {
    prop: 'creationDate',
    name: $localize`:@@dashboard.businessCase.collaboration.applications.table.columns.startedOn:Datum`,
    width: 220,
    filterable: false,
    sortable: false,
    pipe: new DatePipe(locale),
  },
  {
    prop: 'amount',
    name: $localize`:@@dashboard.businessCase.collaboration.applications.table.columns.totalParticipationAmount:Betrag`,
    width: 150,
    filterable: true,
    sortable: false,
  },
  {
    id: 'buttonLink',
    width: 155,
    isCustomCellTemplate: true,
    sortable: false,
    inputClass: 'invoice-actions',
  },
];
