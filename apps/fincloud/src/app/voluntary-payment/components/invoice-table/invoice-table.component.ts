import { CurrencyPipe } from '@angular/common';
import {
  Component,
  Inject,
  Input,
  LOCALE_ID,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { CircleStatusConfig } from '@fincloud/components/booleans';
import { TableColumn } from '@fincloud/components/lists';
import { ConfirmationDialogComponent } from '@fincloud/components/modals';
import { InvoiceInformationControllerCoreService } from '@fincloud/core/billing';
import { FileService } from '@fincloud/core/files';
import { ModalService } from '@fincloud/core/modal';
import { Toast } from '@fincloud/core/toast';
import {
  GeneratedInvoice,
  VoluntaryPaymentsControllerService,
} from '@fincloud/swagger-generator/billing';
import { Locale } from '@fincloud/types/enums';
import { TableRow } from '@fincloud/types/models';
import { FinToastService } from '@fincloud/ui/toast';
import { invoiceTableColumnsConfig } from '../../utils/table-colums';

@Component({
  selector: 'app-invoice-table',
  templateUrl: './invoice-table.component.html',
  styleUrls: ['./invoice-table.component.scss'],
})
export class InvoiceTableComponent implements OnInit, OnChanges {
  @Input() invoices: GeneratedInvoice[];
  @Input() showHeader = true;
  @Input() loading: boolean;

  rows: TableRow[];
  columnsConfig: TableColumn[] = invoiceTableColumnsConfig(this.locale);

  constructor(
    private finToastService: FinToastService,
    private invoiceInformationService: InvoiceInformationControllerCoreService,
    private currencyPipe: CurrencyPipe,
    private fileService: FileService,
    private voluntaryPaymentService: VoluntaryPaymentsControllerService,
    private modalService: ModalService,
    @Inject(LOCALE_ID) private locale: Locale,
  ) {}

  ngOnInit() {
    this.rows = this.invoices.map((i) => this.mapInvoiceTableRow(i));
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.invoices && !changes.invoices.isFirstChange()) {
      this.rows = this.invoices.map((i) => this.mapInvoiceTableRow(i));
    }
  }

  downloadInvoice(invoice: GeneratedInvoice) {
    this.invoiceInformationService
      .getInvoiceAsPdfUsingGET(invoice.id)
      .subscribe({
        next: (stream) => {
          this.fileService.downloadBinary(
            stream as Blob,
            `${invoice.sevdeskInvoiceNumber}.pdf`,
          );
        },
        error: () => {
          this.finToastService.show(Toast.error());
        },
      });
  }

  cancelInvoice(invoice: GeneratedInvoice) {
    this.modalService.openComponent(
      ConfirmationDialogComponent,
      {
        title: $localize`:@@invoiceTable.title:Rechnung stornieren`,
        message: $localize`:@@invoiceTable.message:Bitte beachten Sie, dass der Vorgang ist nicht rückgängig gemacht werden kann.`,
        confirmLabel: $localize`:@@invoiceTable.confirmLabel:Rechnung stornieren`,
      },
      {
        windowClass: 'confirm-cancel-invoice-dialog',
      },
      (result) => {
        if (result.success) {
          this.voluntaryPaymentService
            .cancelVoluntaryPaymentUsingPOST(invoice.id)
            .subscribe({
              next: (cancelledInvoice: GeneratedInvoice) => {
                const rowsCopy = [...this.rows];
                const affectedRowIndex = rowsCopy.findIndex(
                  (r) => r.id === cancelledInvoice.id,
                );
                rowsCopy[affectedRowIndex] =
                  this.mapInvoiceTableRow(cancelledInvoice);
                this.rows = rowsCopy;

                this.finToastService.show(Toast.success());
              },
              error: () => {
                this.finToastService.show(Toast.error());
              },
            });
        }
      },
    );
  }

  private mapInvoiceTableRow(invoice: GeneratedInvoice): TableRow {
    return {
      ...invoice,
      statusConfig: this.getStatusConfig(invoice.status),
      amount: this.currencyPipe.transform(
        invoice.invoiceCalculationDetails?.calculatedFeeAmount,
      ) as string,
    };
  }

  private getStatusConfig(status: string): CircleStatusConfig {
    switch (status) {
      case 'PAID':
        return {
          tooltip: 'bezahlt',
          color: 'green',
        };
      case 'CANCELED':
        return {
          tooltip: 'abgesagt',
          color: 'red',
        };
      default:
        return {
          tooltip: 'steht aus',
          color: 'gray',
        };
    }
  }
}
