import { DuplicateBusinessCaseState } from '@fincloud/types/models';
import { createFeature, createReducer, on } from '@ngrx/store';
import { loadReadEstateFinancingStructureGroupsNamesSuccess } from '../actions/dupclitate-business-case-api.actions';
import { selectDuplicateBusinessCaseSelectors } from '../selectors/duplicate-business-case.selectors';

const initialState: DuplicateBusinessCaseState = {
  realEstateFinancingStructureGroupsNames: [],
};
export const duplicateBusinessCaseFeature = createFeature({
  name: 'duplicate-business-case',
  reducer: createReducer(
    initialState,
    on(
      loadReadEstateFinancingStructureGroupsNamesSuccess,
      (state, action): DuplicateBusinessCaseState => {
        return {
          ...state,
          realEstateFinancingStructureGroupsNames: action.groupsNames,
        };
      },
    ),
  ),
  extraSelectors: selectDuplicateBusinessCaseSelectors,
});
