import { Injectable } from '@angular/core';
import { selectBusinessCaseId } from '@fincloud/state/business-case';
import { FinStructureControllerService } from '@fincloud/swagger-generator/financing-details';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { catchError, map, of, switchMap } from 'rxjs';
import {
  DupclicateBusinessCasePageActions,
  DuplcateBusinessCaseApiActions,
} from '../actions';

@Injectable()
export class DuplicateBusinessCaseEffects {
  loadRealEstateFinanicngStructureGroupsNames$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        DupclicateBusinessCasePageActions.loadRealEstateFinancingStructureGroupsNames,
      ),
      concatLatestFrom(() => this.store.select(selectBusinessCaseId)),
      switchMap(([action, businessCaseId]) =>
        this.finStructureControllerService
          .getFinancingStructureMainGroups({ businessCaseId })
          .pipe(
            map((groupsNames) =>
              DuplcateBusinessCaseApiActions.loadReadEstateFinancingStructureGroupsNamesSuccess(
                { groupsNames },
              ),
            ),
            catchError((error) =>
              of(
                DuplcateBusinessCaseApiActions.loadReadEstateFinancingStructureGroupsNamesFailure(
                  { error },
                ),
              ),
            ),
          ),
      ),
    ),
  );
  constructor(
    private actions$: Actions,
    private store: Store,
    private finStructureControllerService: FinStructureControllerService,
  ) {}
}
