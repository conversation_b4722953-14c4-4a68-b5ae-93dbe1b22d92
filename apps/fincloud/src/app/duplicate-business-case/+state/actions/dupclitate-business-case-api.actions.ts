import { HttpErrorResponse } from '@angular/common/http';
import { PlainGroup } from '@fincloud/swagger-generator/financing-details';
import { createAction, props } from '@ngrx/store';

export const loadReadEstateFinancingStructureGroupsNamesSuccess = createAction(
  '[Duplicate Business Case API] Load real estate financing structure groups names Success',
  props<{ groupsNames: PlainGroup[] }>(),
);

export const loadReadEstateFinancingStructureGroupsNamesFailure = createAction(
  '[Duplicate Business Case API] Load real estate financing structure groups names Failure',
  props<{ error: HttpErrorResponse }>(),
);
