import { DuplicateBusinessCaseState } from '@fincloud/types/models';
import { createSelector } from '@ngrx/store';
import { BaseSelectors } from '@ngrx/store/src/feature_creator';

export const selectDuplicateBusinessCaseSelectors = (
  duplicateBusinessCaseFeature: BaseSelectors<
    'duplicate-business-case',
    DuplicateBusinessCaseState
  >,
) => {
  const selectGroupNames = createSelector(
    duplicateBusinessCaseFeature.selectRealEstateFinancingStructureGroupsNames,
    (groupNamesData) => groupNamesData.map((group) => group.groupName),
  );

  return {
    selectGroupNames,
  };
};
