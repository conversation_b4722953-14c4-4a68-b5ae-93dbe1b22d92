import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NsUiBooleansModule } from '@fincloud/components/booleans';
import { NsUiButtonsModule } from '@fincloud/components/buttons';
import { NsUiChartsModule } from '@fincloud/components/charts';
import { NsUiCompositeModule } from '@fincloud/components/composite';
import { NsUiFormlyModule } from '@fincloud/components/formly';
import { NsUiFormsModule } from '@fincloud/components/forms';
import { NsUiIconsModule } from '@fincloud/components/icons';
import { NsUiNumberModule } from '@fincloud/components/number';
import { NsUiSelectsModule } from '@fincloud/components/selects';
import { NsUiNgBootstrapModule } from '@fincloud/components/third-party-modules';
import { NsUiTooltipModule } from '@fincloud/components/tooltip';
import { NsUiTruncatedTextModule } from '@fincloud/components/truncated-text';
import { NsCoreDirectivesModule } from '@fincloud/core/directives';
import { NsCorePipesModule } from '@fincloud/core/pipes';
import { NsBusinessCaseRefactoringModule } from '@fincloud/neoshare/business-case';
import { NgbAccordionDirective } from '@ng-bootstrap/ng-bootstrap';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { DuplicateBusinessCaseEffects } from './+state/effects/duplicate-business-case.effects';
import { duplicateBusinessCaseFeature } from './+state/reducers/duplicate-business-case.reducer';
import { CaseStateChangeModalComponent } from './components/case-state-change-modal/case-state-change-modal.component';
import { DuplicateBusinessCaseBasicInfoComponent } from './components/duplicate-business-case-basic-info/duplicate-business-case-basic-info.component';
import { DuplicateBusinessCaseCollaborationComponent } from './components/duplicate-business-case-collaboration/duplicate-business-case-collaboration.component';
import { DuplicateBusinessCaseDataRoomComponent } from './components/duplicate-business-case-data-room/duplicate-business-case-data-room.component';
import { DuplicateBusinessCaseFinancingDetailsComponent } from './components/duplicate-business-case-financing-details/duplicate-business-case-financing-details.component';
import { DuplicateBusinessCaseManagementComponent } from './components/duplicate-business-case-management/duplicate-business-case-management.component';
import { DuplicateBusinessCaseComponent } from './components/duplicate-business-case/duplicate-business-case.component';
import { DuplicateBusinessCaseRoutingModule } from './duplicate-business-case.routing.module';

@NgModule({
  declarations: [
    DuplicateBusinessCaseComponent,
    DuplicateBusinessCaseDataRoomComponent,
    DuplicateBusinessCaseBasicInfoComponent,
    DuplicateBusinessCaseCollaborationComponent,
    DuplicateBusinessCaseManagementComponent,
    DuplicateBusinessCaseFinancingDetailsComponent,
    CaseStateChangeModalComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    DuplicateBusinessCaseRoutingModule,
    NsBusinessCaseRefactoringModule,
    NsCoreDirectivesModule,
    NsCorePipesModule,
    NsUiNgBootstrapModule,
    NsUiButtonsModule,
    NsUiChartsModule,
    NsUiBooleansModule,
    NsUiCompositeModule,
    NsUiSelectsModule,
    NsUiIconsModule,
    NsUiTruncatedTextModule,
    NsUiTooltipModule,
    NsUiFormsModule,
    NsUiNumberModule,
    NsUiFormlyModule,
  ],
  providers: [
    NgbAccordionDirective,
    provideState(duplicateBusinessCaseFeature),
    provideEffects(DuplicateBusinessCaseEffects),
  ],
})
export class DuplicateBusinessCaseModule {}
