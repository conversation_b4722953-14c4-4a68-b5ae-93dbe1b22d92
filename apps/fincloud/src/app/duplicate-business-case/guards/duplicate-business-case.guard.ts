import { inject } from '@angular/core';
import { CanActivateFn } from '@angular/router';
import { selectIsBusinessCaseRealEstate } from '@fincloud/state/business-case';
import { Store } from '@ngrx/store';
import { map, tap } from 'rxjs';
import { DupclicateBusinessCasePageActions } from '../+state/actions';

export const duplicateBusinessCaseGuard: CanActivateFn = () => {
  const store = inject(Store);
  return store.select(selectIsBusinessCaseRealEstate).pipe(
    tap((isRealEstate) => {
      if (isRealEstate) {
        store.dispatch(
          DupclicateBusinessCasePageActions.loadRealEstateFinancingStructureGroupsNames(),
        );
      }
    }),
    map(() => true),
  );
};
