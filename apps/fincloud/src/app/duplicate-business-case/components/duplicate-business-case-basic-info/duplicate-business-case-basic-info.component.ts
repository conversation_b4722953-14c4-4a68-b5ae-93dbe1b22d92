import {
  ChangeDetectorRef,
  Component,
  DestroyRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  FormBuilder,
  FormGroup,
  UntypedFormControl,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { SpecialFieldLabelOrKeyEnum } from '@fincloud/components/refs';
import { AuthenticationService } from '@fincloud/core/auth';
import { BusinessCaseModelService } from '@fincloud/core/business-case';
import {
  FormlyHelperService,
  TemplateSelectOption,
} from '@fincloud/core/formly';
import { ModalService } from '@fincloud/core/modal';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { neitherNullNorUndefined } from '@fincloud/core/utils';
import { BaseCreateUpdateBusinessCaseComponent } from '@fincloud/neoshare/base-create-update-business-case';
import { CreateFinancingStructureUtilService } from '@fincloud/neoshare/business-case';
import { PortalActionsService } from '@fincloud/neoshare/services';
import { selectRefsCaseCommonFields } from '@fincloud/state/business-case';
import { UserManagementControllerService } from '@fincloud/swagger-generator/authorization-server';
import {
  BusinessCaseControllerService,
  InformationControllerService,
  ParticipantControllerService,
  TemplateControllerService,
} from '@fincloud/swagger-generator/business-case-manager';
import { CompanyControllerService } from '@fincloud/swagger-generator/company';
import {
  Company,
  ExchangeBusinessCase,
  InformationRecord,
  Template,
} from '@fincloud/swagger-generator/exchange';
import { FinStructureControllerService } from '@fincloud/swagger-generator/financing-details';
import {
  BusinessCaseType,
  FinancingStructureType,
} from '@fincloud/types/enums';
import {
  AppState,
  MultiselectOption,
  financingStructureType,
} from '@fincloud/types/models';
import { FinToastService } from '@fincloud/ui/toast';
import {
  BUSINESS_CASE_TYPE_ICONS,
  BUSINESS_CASE_TYPE_LABELS,
} from '@fincloud/utils';
import { Store } from '@ngrx/store';
import { tap } from 'rxjs';

@Component({
  selector: 'app-duplicate-business-case-basic-info',
  templateUrl: './duplicate-business-case-basic-info.component.html',
  styleUrls: ['./duplicate-business-case-basic-info.component.scss'],
})
export class DuplicateBusinessCaseBasicInfoComponent
  extends BaseCreateUpdateBusinessCaseComponent
  implements OnInit
{
  @Input()
  businessCase: ExchangeBusinessCase;
  @Input() isBusinessCaseMiscellaneous: boolean;
  readonly totalFinancingVolume = $localize`:@@createBusinessCase.formField.total-financing-volume:Finanzierungsvolumen`;
  readonly realEstateTotalFinancingVolume = $localize`:@@createBusinessCase.formField.real-estate-total-financing-volume:Gesamtinvestitionskosten`;
  readonly ownCapacityBiggerThanTotalErrorMessage = $localize`:@@createBusinessCase.ownCapacityField.valueBiggerThanTotalError:Das Eigenkapital darf nicht größer sein als die Gesamtinvestitionskosten`;
  readonly BusinessCaseTypeLabels = BUSINESS_CASE_TYPE_LABELS;
  readonly BusinessCaseTypeIcons = BUSINESS_CASE_TYPE_ICONS;
  readonly BusinessCaseType: 'FINANCING_CASE';
  readonly financingStructureType = FinancingStructureType;
  businessCaseTypeOptions: MultiselectOption[];

  checkboxOptions = [
    {
      formControlName: 'dataRoom',
      label: $localize`:@@dashboard.businessCase.dataRoomTab:Data Room`,
    },
    {
      formControlName: 'collaboration',
      label: $localize`:@@dashboard.businessCase.collaboration:Kollaboration`,
    },
    {
      formControlName: 'administration',
      label: $localize`:@@dashboard.businessCase.tabs.administration:Verwaltung`,
    },
  ];

  @Output()
  tabCheckboxSelected = new EventEmitter<FormGroup>();

  @Output()
  sendFinancingVolume = new EventEmitter<number>();

  @Output()
  sendCompanyId = new EventEmitter<string>();

  @Output()
  sendIsCADRLinked = new EventEmitter<boolean>();

  @Output()
  sendSelectedTemplateId = new EventEmitter<Template>();

  @Output()
  sendBusinessCaseBaseForm = new EventEmitter<FormGroup>();

  @Output()
  sendParameters = new EventEmitter<{ [x: string]: unknown }>();

  formGroup: FormGroup;
  tabsSelectedForm: FormGroup;

  constructor(
    destroyRef: DestroyRef,
    templateControllerService: TemplateControllerService,
    authenticationService: AuthenticationService,
    companyService: CompanyControllerService,
    formlyHelperService: FormlyHelperService,
    businessCaseControllerService: BusinessCaseControllerService,
    router: Router,
    finToastService: FinToastService,
    modalService: ModalService,
    userManagementControllerService: UserManagementControllerService,
    participantControllerService: ParticipantControllerService,
    store: Store<AppState>,
    cdr: ChangeDetectorRef,
    informationController: InformationControllerService,
    portalActionsService: PortalActionsService,
    formBuilder: FormBuilder,
    createFinancingStructureUtilService: CreateFinancingStructureUtilService,
    refsControllerService: FinStructureControllerService,
    private businessCaseModelService: BusinessCaseModelService,
    regionalSettings: RegionalSettingsService,
  ) {
    super(
      destroyRef,
      templateControllerService,
      authenticationService,
      companyService,
      formlyHelperService,
      businessCaseControllerService,
      router,
      finToastService,
      modalService,
      userManagementControllerService,
      participantControllerService,
      store,
      cdr,
      informationController,
      portalActionsService,
      formBuilder,
      createFinancingStructureUtilService,
      refsControllerService,
      regionalSettings,
    );
  }

  ngOnInit() {
    super.ngOnInit();

    this.templateForm.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((parameters) => {
        // Check if the 'commissionFee' property exists in the 'parameters' object and its 'inputValue' is undefined. The purpose of this check is to avoid overwriting the 'inputValue'.
        if (
          parameters.commissionFee &&
          parameters.commissionFee.inputValue === undefined
        ) {
          const commissionFeeValue =
            this.businessCase.information?.[
              'commissionFee' as keyof InformationRecord
            ]?.value;

          if (commissionFeeValue) {
            this.templateForm.patchValue(
              {
                commissionFee: commissionFeeValue,
              },
              { emitEvent: false },
            );
          }
        }
        this.sendParameters.emit(parameters);
      });

    this.tabsSelectedForm = this.formBuilder.group(
      {
        dataRoom: true,
        collaboration: true,
        administration: true,
      },
      { validators: this.validateCheckboxes },
    );

    this.businessCaseBaseForm.addControl(
      'caseType',
      new UntypedFormControl(this.businessCase.businessCaseType),
    );

    /*
     * Set the financingVolume where the case is NOT real estate, from the case itself
     * OR if the case IS type real estate -> set the financing volume and the
     * own capacity values from the refs->common_fields
     */
    this.store
      .select(selectRefsCaseCommonFields)
      .pipe(
        tap((commonFields) => {
          if (
            this.businessCase?.structuredFinancingConfiguration
              ?.financingStructureType !== FinancingStructureType.REAL_ESTATE
          ) {
            this.businessCaseBaseForm.patchValue({
              finanzierungsVolumen:
                this.businessCaseModelService.getFinancingVolume(
                  this.businessCase,
                ),
            });
          } else {
            const totalAmountField = commonFields.find(
              (field) =>
                field.key ===
                SpecialFieldLabelOrKeyEnum.TOTAL_INVESTMENT_AMOUNT,
            );
            const totalAmountValue = totalAmountField?.value;

            const ownCapacityField = commonFields.find(
              (field) => field.key === SpecialFieldLabelOrKeyEnum.TOTAL_EQUITY,
            );
            const ownCapacityValue = ownCapacityField?.value;

            this.businessCaseBaseForm.patchValue({
              finanzierungsVolumen: totalAmountValue
                ? parseFloat(totalAmountValue as string)
                : null,
              eigenkapital: ownCapacityValue
                ? parseFloat(ownCapacityValue as string)
                : null,
            });
          }

          this.updateFinancingVolume();
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();

    this.businessCaseBaseForm.patchValue({
      company: this.mapToSelectOption(this.businessCase.company),
      isCADRLinked: this.businessCase.isCADRLinked,
    });

    this.emitTabsSelectedForm();
    this.businessCaseTypeOptions = this.allowedBusinessCaseTypes.map(
      (type) => ({
        label: type,
        value: type,
      }),
    );

    this.onBusinessCaseTypeSelect(
      this.businessCase.businessCaseType as BusinessCaseType,
    );

    this.selectCompany(this.businessCase.company);
    this.emitIsCADRLinked();

    this.updateTemplateValidator();

    this.businessCaseBaseForm.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.sendBusinessCaseBaseForm.emit(this.businessCaseBaseForm);
      });
  }

  compareCompanyFn(item: unknown, selected: unknown) {
    return item === selected;
  }

  getFormControl(
    formControlName: string,
    tabsSelectedForm: FormGroup,
  ): UntypedFormControl {
    return tabsSelectedForm.get(formControlName) as UntypedFormControl;
  }

  getFormControlLabel(label: string, isMiscCase: boolean): string {
    if (
      !isMiscCase &&
      label === $localize`:@@dashboard.businessCase.dataRoomTab:Data Room`
    ) {
      return $localize`:@@dashboard.businessCase.dataRoomTabAndFinancingDetails:Data room & Finanzierungsdetails`;
    }

    return label;
  }

  validateCheckboxes = (control: UntypedFormControl) => {
    const allUnselected = Object.values(control.value).every((value) => !value);
    return allUnselected ? { allUnselected: true } : null;
  };

  selectTemplate() {
    this.sendSelectedTemplateId.emit(
      this.businessCaseBaseForm.get('template')?.value,
    );
  }

  emitTabsSelectedForm() {
    this.updateTemplateValidator();
    this.tabCheckboxSelected.emit(this.tabsSelectedForm);
  }

  updateTemplateValidator() {
    if (this.tabsSelectedForm.get('dataRoom')?.value) {
      this.businessCaseBaseForm.get('template').clearValidators();
      this.businessCaseBaseForm.get('template').reset();
    } else {
      this.businessCaseBaseForm
        .get('template')
        .setValidators(Validators.required);
    }
    this.sendBusinessCaseBaseForm.emit(this.businessCaseBaseForm);
  }

  selectCompany(company: Company) {
    this.sendCompanyId.emit(company?.id);
  }

  emitIsCADRLinked() {
    this.sendIsCADRLinked.emit(
      this.businessCaseBaseForm.get('isCADRLinked')?.value,
    );
  }

  updateFinancingVolume() {
    this.sendFinancingVolume.emit(
      this.businessCaseBaseForm.get('finanzierungsVolumen')?.value,
    );
  }

  filterByType(
    templates: TemplateSelectOption[] | null,
    financingStructureType: financingStructureType,
  ): TemplateSelectOption[] {
    if (neitherNullNorUndefined(templates)) {
      return templates.filter((temp) => temp.type === financingStructureType);
    }
    return [];
  }
}
