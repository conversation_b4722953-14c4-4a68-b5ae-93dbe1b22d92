import {
  ChangeDetectorRef,
  Component,
  DestroyRef,
  OnInit,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { SpecialFieldLabelOrKeyEnum } from '@fincloud/components/refs';
import { StateTransfer } from '@fincloud/core/business-case';
import { ModalService } from '@fincloud/core/modal';
import { Toast } from '@fincloud/core/toast';
import {
  neitherNullNorUndefined,
  setTimeoutUnpatched,
} from '@fincloud/core/utils';
import { ActivityLogService } from '@fincloud/neoshare/business-case';
import {
  StateLibBusinessCasePageActions,
  selectBusinessCase,
  selectFacilitiesNames,
  selectHasAnyBusinessCasePermission,
  selectIsBusinessCaseMiscellaneous,
  selectTotalInvestmentAmount,
} from '@fincloud/state/business-case';
import { selectUserCustomerKey, selectUserId } from '@fincloud/state/user';
import {
  BusinessCaseControllerService,
  CriteriaRequest,
  DuplicateBusinessCaseControllerService,
  DuplicateBusinessCaseRequest,
  Faq,
  Group,
  InviteCustomerRequest,
  ParticipantControllerService,
  ParticipantCustomer,
  Template,
} from '@fincloud/swagger-generator/business-case-manager';
import {
  BusinessCaseParticipantCustomer,
  ExchangeBusinessCase,
} from '@fincloud/swagger-generator/exchange';
import {
  BusinessCasePermission,
  FinancingStructureType,
} from '@fincloud/types/enums';
import { FinToastService } from '@fincloud/ui/toast';
import { BUSINESS_CASE_TYPE_LABELS } from '@fincloud/utils';
import { Store } from '@ngrx/store';
import { Observable, forkJoin, of } from 'rxjs';
import {
  filter,
  finalize,
  shareReplay,
  switchMap,
  take,
  tap,
} from 'rxjs/operators';
import { duplicateBusinessCaseFeature } from '../../+state/reducers/duplicate-business-case.reducer';
import { CaseStateChangeModalComponent } from '../case-state-change-modal/case-state-change-modal.component';

@Component({
  selector: 'app-duplicate-business-case',
  templateUrl: './duplicate-business-case.component.html',
  styleUrls: ['./duplicate-business-case.component.scss'],
})
export class DuplicateBusinessCaseComponent implements OnInit {
  tabsSelectedForm: FormGroup;

  // TODO: To be reworked next 6 rows
  duplicateBusinessCase: DuplicateBusinessCaseRequest = {};
  dataRoomInfo: DuplicateBusinessCaseRequest = {};
  collaborationInfo: DuplicateBusinessCaseRequest = {};
  administrationInfo: DuplicateBusinessCaseRequest = {};
  basicInfo: DuplicateBusinessCaseRequest = {};
  financingDetails: DuplicateBusinessCaseRequest = {
    persistFinancingStructure: true,
  };

  templateId: string;
  currentUserId$ = this.store.select(selectUserId);
  parameters: { name: string; val: number };
  commissionFee: { name: string; val: number };
  companyParams: { name: string; val: string };
  rating: { name: string; val: string };
  isLoading = false;
  businessCaseBaseForm: FormGroup;
  canSeeCaseManagementTab: boolean;
  customerKey: string;
  financingVolume = 0;

  private businessCase: ExchangeBusinessCase;

  businessCase$: Observable<ExchangeBusinessCase> = this.store
    .select(selectBusinessCase)
    .pipe(
      filter((x) => !!x),
      shareReplay({ refCount: true, bufferSize: 1 }),
      take(1),
      tap((businessCase) => {
        this.businessCase = businessCase;
      }),
    );
  totalInvestmentAmount$: Observable<number> = this.store.select(
    selectTotalInvestmentAmount,
  );
  isBusinessCaseMiscellaneous$: Observable<boolean> = this.store.select(
    selectIsBusinessCaseMiscellaneous,
  );
  groupNames$: Observable<string[]> = this.businessCase$.pipe(
    filter(
      (businessCase) =>
        businessCase &&
        businessCase.structuredFinancingConfiguration.financingStructureType !==
          FinancingStructureType.MISCELLANEOUS,
    ),
    switchMap((businessCase) =>
      businessCase.structuredFinancingConfiguration.financingStructureType ===
      FinancingStructureType.REAL_ESTATE
        ? this.store.select(duplicateBusinessCaseFeature.selectGroupNames)
        : this.store.select(selectFacilitiesNames),
    ),
  );

  constructor(
    private destroyRef: DestroyRef,
    private store: Store,
    private duplicateBusinessCaseControllerService: DuplicateBusinessCaseControllerService,
    private modalService: ModalService,
    private businessCaseControllerService: BusinessCaseControllerService,
    private finToastService: FinToastService,
    private router: Router,
    private changeDetectorRef: ChangeDetectorRef,
    private activityLogService: ActivityLogService,
    private participantControllerService: ParticipantControllerService,
  ) {}

  ngOnInit() {
    this.store
      .select(selectUserCustomerKey)
      .pipe(take(1))
      .subscribe((customerKey) => (this.customerKey = customerKey));

    this.activityLogService.clearActivityLogInterval();

    this.currentUserId$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((userId) => {
        this.collaborationInfo.contactPersonIds = [userId];
      });

    this.store
      .select(
        selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00004]),
      )
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((canSeeCaseManagementTab: boolean) => {
        this.canSeeCaseManagementTab = canSeeCaseManagementTab;
      });
  }

  duplicateCase() {
    const financingData = this.businessCaseBaseForm.getRawValue();

    if (
      !financingData?.finanzierungsVolumen ||
      financingData?.finanzierungsVolumen < 0
    ) {
      return;
    }

    const isDataRoomSelected = this.tabsSelectedForm.get('dataRoom')?.value;
    if (
      this.tabsSelectedForm.valid &&
      this.businessCaseBaseForm.valid &&
      (isDataRoomSelected || this.templateId)
    ) {
      const parameters =
        this.basicInfo.businessCaseType ===
        BUSINESS_CASE_TYPE_LABELS.FINANCING_CASE
          ? [this.parameters, this.companyParams, this.rating].filter((param) =>
              neitherNullNorUndefined(param),
            )
          : [
              this.parameters,
              this.companyParams,
              this.commissionFee,
              this.rating,
            ].filter((param) => neitherNullNorUndefined(param));

      this.duplicateBusinessCase = {
        ...this.basicInfo,
        ...this.financingDetails,
        ...(isDataRoomSelected
          ? this.dataRoomInfo
          : { templateId: this.templateId }),

        ...(this.tabsSelectedForm.value.administration
          ? this.administrationInfo
          : { participants: null }),
        ...(this.tabsSelectedForm.value.collaboration
          ? this.collaborationInfo
          : {
              participants: [
                this.collaborationInfo.participants.find(
                  (participant) => participant.lead,
                ),
              ],
            }),
        parameters,
        facilityParameters: parameters,
        finStructureParameters: [
          {
            name: SpecialFieldLabelOrKeyEnum.TOTAL_INVESTMENT_AMOUNT,
            val: financingData.finanzierungsVolumen,
          },
          {
            name: SpecialFieldLabelOrKeyEnum.TOTAL_EQUITY,
            val: financingData.eigenkapital,
          },
        ],
        persistAllInformation: isDataRoomSelected,
        persistFinancingStructure:
          this.financingDetails.persistFinancingStructure,
      };
      this.isLoading = true;

      this.duplicateBusinessCaseControllerService
        .duplicateBusinessCase({
          businessCaseId: this.businessCase.id,
          body: {
            ...this.duplicateBusinessCase,
          },
        })
        .pipe(
          switchMap((res) => {
            return forkJoin([
              of(res),
              this.participantControllerService.updateParticipantStatePerception(
                {
                  businessCaseId: res.createdBusinessCase.id,
                  newState: 'FINANCING_CASE_CREATED',
                },
              ),
            ]);
          }),
          finalize(() => (this.isLoading = false)),
        )
        .subscribe({
          next: ([res]) => {
            if (
              this.canSeeCaseManagementTab &&
              this.businessCase.state !== 'INACTIVE_CANCELLED' &&
              this.businessCase.state !== 'INACTIVE_COMPLETED'
            ) {
              this.modalService.openComponent<CaseStateChangeModalComponent>(
                CaseStateChangeModalComponent,
                {
                  visibilityState: this.businessCase.state,
                },
                {},
                (result) => {
                  // this is the case when choose the COMPLETE option
                  if (result.success && result.data) {
                    this.participantControllerService
                      .updateParticipantStatePerception({
                        businessCaseId: this.businessCase.id,
                        newState: 'OTHER',
                      })
                      .subscribe();
                    this.businessCaseControllerService
                      .setState({
                        businessCaseId: this.businessCase.id,
                        stateTransfer: result.data as StateTransfer,
                      })
                      .subscribe({
                        next: () => {
                          this.goToBusinessCase(res.createdBusinessCase.id);
                        },
                        error: () => {
                          this.finToastService.show(Toast.error());
                        },
                      });
                  } else {
                    this.goToBusinessCase(res.createdBusinessCase.id);
                  }
                },
                () => {
                  // catching ESCAPE btn click
                  this.goToBusinessCase(res.createdBusinessCase.id);
                },
              );
            } else {
              this.goToBusinessCase(res.createdBusinessCase.id);
              this.finToastService.show(Toast.success());
            }
          },
          error: () => {
            this.finToastService.show(Toast.error());
          },
        });
    }
  }

  toggleTabVisibility(tabsSelectedForm: FormGroup) {
    this.tabsSelectedForm = tabsSelectedForm;
    this.changeDetectorRef.detectChanges();
  }

  getBusinessCaseBaseForm(businessCaseBaseForm: FormGroup) {
    this.businessCaseBaseForm = businessCaseBaseForm;
  }

  setFaqs(faqs: Faq[]) {
    this.administrationInfo.faq = faqs;
  }

  setMinAndMaxParticipationAmount(minAndMaxParticipationAmount: {
    [x: string]: number;
  }) {
    Object.keys(minAndMaxParticipationAmount).forEach((key) => {
      if (key === 'maxParticipationAmount') {
        this.collaborationInfo.maxParticipationAmount =
          minAndMaxParticipationAmount[key];
      } else {
        this.collaborationInfo.minParticipationAmount =
          minAndMaxParticipationAmount[key];
      }
    });
  }

  setGroups(groups: Group[]) {
    this.dataRoomInfo.groupsOrdered = groups;
  }

  setCriteria(criteria: CriteriaRequest[]) {
    this.collaborationInfo.criteria = criteria;
  }

  setVisibilityState(visibilitySate: boolean) {
    this.collaborationInfo.state = visibilitySate
      ? 'ACTIVE_PUBLIC'
      : 'ACTIVE_PRIVATE';
  }

  setInvitees(invitees: InviteCustomerRequest[]) {
    this.collaborationInfo.invitees = invitees;
  }

  setParticipants(participants: BusinessCaseParticipantCustomer[]) {
    this.collaborationInfo.participants = participants as ParticipantCustomer[];
  }

  setSelectedInboxDocuments(inboxDocuments: string[]) {
    this.dataRoomInfo.inboxDocuments = inboxDocuments;
  }

  setSelectedGroupFieldsValues(groupFields: string[]) {
    this.dataRoomInfo.copyInformationForFieldKeys = groupFields;
  }

  setCompanyId(companyId: string) {
    this.basicInfo.companyId = companyId;
    this.companyParams = { name: 'companyId', val: companyId };
  }

  setIsCADRLinked(isCADRLinked: boolean) {
    this.basicInfo.linkCADR = isCADRLinked;
  }

  setFinancingVolume(financingVolume: number) {
    this.parameters = { name: 'financingVolume', val: financingVolume };
  }

  setBusinessCaseType(businessCaseType: 'FINANCING_CASE') {
    this.basicInfo.businessCaseType = businessCaseType;
  }

  setTemplateId(template: Template) {
    this.templateId = template?.id;
  }

  setParameters(parameters: { [x: string]: unknown }) {
    this.commissionFee = {
      name: 'commissionFee',
      val: parameters['commissionFee'] as number,
    };
    this.rating = {
      name: 'rating',
      val: parameters['rating'] as string,
    };
  }

  goToBusinessCase(businessCaseId: string) {
    this.store.dispatch(
      StateLibBusinessCasePageActions.setLastVisitedUrl({
        payload: `${this.customerKey}/business-case/${businessCaseId}`,
      }),
    );

    setTimeoutUnpatched(() => {
      void this.router.navigate([
        `${this.customerKey}/business-case/${businessCaseId}`,
      ]);
    }, 0);
  }

  shareFinancingGroups(value: boolean): void {
    this.financingDetails.persistFinancingStructure = value;
  }
}
