import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { OverlayModule } from '@angular/cdk/overlay';
import { AvatarComponent } from '@fincloud/components/avatar';
import { NsUiBooleansModule } from '@fincloud/components/booleans';
import { NsUiHorizontalDividerModule } from '@fincloud/components/horizontal-divider';
import { NsUiIconsModule } from '@fincloud/components/icons';
import { NsUiLayoutModule } from '@fincloud/components/layout';
import { MessageStatusModule } from '@fincloud/components/message-status';
import { NsUiNavigationModule } from '@fincloud/components/navigation';
import { NsUiOnboardingTipsModule } from '@fincloud/components/onboarding-tips';
import { NsUiNgBootstrapModule } from '@fincloud/components/third-party-modules';
import { NsCorePipesModule } from '@fincloud/core/pipes';
import { FinBadgesModule } from '@fincloud/ui/badges';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinContainerModule } from '@fincloud/ui/container';
import { FinIconModule } from '@fincloud/ui/icon';
import { TourNgxBootstrapModule } from 'ngx-ui-tour-ngx-bootstrap';
import { NotificationBellComponent } from './components/notification-bell/notification-bell.component';
import { NotificationIconComponent } from './components/notification-icon/notification-icon.component';
import { NotificationSettingsComponent } from './components/notification-settings/notification-settings.component';
import { NotificationSystemCardComponent } from './components/notification-system-card/notification-system-card.component';
import { NotificationSystemEditorComponent } from './components/notification-system-editor/notification-system-editor.component';
import { NotificationSystemListHeaderComponent } from './components/notification-system-list-header/notification-system-list-header.component';
import { NotificationSystemListComponent } from './components/notification-system-list/notification-system-list.component';

@NgModule({
  declarations: [
    NotificationSystemEditorComponent,
    NotificationSystemListComponent,
    NotificationBellComponent,
    NotificationSystemCardComponent,
    NotificationIconComponent,
    NotificationSystemListHeaderComponent,
    NotificationSettingsComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    AvatarComponent,
    TourNgxBootstrapModule,
    MessageStatusModule,
    NsUiOnboardingTipsModule,
    FinButtonModule,
    FinBadgesModule,
    FinIconModule,
    OverlayModule,
    FinContainerModule,
    NsCorePipesModule,
    NsUiOnboardingTipsModule,
    NsUiLayoutModule,
    NsUiIconsModule,
    NsUiNavigationModule,
    MessageStatusModule,
    NsUiNgBootstrapModule,
    NsUiHorizontalDividerModule,
    NsUiBooleansModule,
  ],
  exports: [NotificationSystemEditorComponent],
})
export class NotificationSystemModule {}
