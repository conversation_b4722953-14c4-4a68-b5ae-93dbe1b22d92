import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  Inject,
  OnInit,
} from '@angular/core';
import {
  BehaviorSubject,
  Subject,
  debounceTime,
  filter,
  merge,
  of,
  take,
  tap,
  withLatestFrom,
} from 'rxjs';

import { ConnectedPosition } from '@angular/cdk/overlay';
import { DOCUMENT } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { LayoutCommunicationService } from '@fincloud/core/layout';
import { SocketService, SocketType } from '@fincloud/core/socket';
import { NotificationSystemFacade } from '@fincloud/state/notifications';
import { Notification } from '@fincloud/swagger-generator/platform-notification';
import { NotificationEditorMode } from '@fincloud/types/enums';
import { NOTIFICATION_DESTINATION } from '@fincloud/utils';
import { TourService } from 'ngx-ui-tour-ngx-bootstrap';
import { CUSTOMIZATIONS_ONBOARDING_STEP } from '../../utils/customizations-onboarding-step';
import { DEFAULT_TOUR_CONFIG } from '../../utils/default-tour-config';
import { NOTIFICATIONS_TOUR_CONFIG } from '../../utils/notifications-tour-config';
import { ONBOARDING_TIPS_NOTIFICATION_SYSTEM } from '../../utils/onboarding-tips-notification-system';

@Component({
  selector: 'app-notification-system-editor',
  templateUrl: './notification-system-editor.component.html',
  styleUrls: ['./notification-system-editor.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NotificationSystemEditorComponent implements OnInit {
  mode$ = new BehaviorSubject<NotificationEditorMode>(
    NotificationEditorMode.COLLAPSED,
  );

  summaryPanelPositions: ConnectedPosition[] = [
    {
      originX: 'end',
      originY: 'bottom',
      overlayX: 'end',
      overlayY: 'top',
    },
  ];

  toggleMode$ = new Subject<boolean>();

  notifications$ = this.facade.notifications$;

  showOnboarding$ = this.facade.showOnboarding$;

  NotificationEditorMode = NotificationEditorMode;

  onboardingTips = ONBOARDING_TIPS_NOTIFICATION_SYSTEM;

  activeOnboardingStep$ = new BehaviorSubject(null);

  // ngrx
  initialNotifications$ = of([]).pipe(take(1));

  notificationsList$ = this.facade.notificationsList$;

  constructor(
    private destroyRef: DestroyRef,
    public tourService: TourService,
    private layoutCommunicationService: LayoutCommunicationService,
    @Inject(DOCUMENT) private document: Document,
    private facade: NotificationSystemFacade,
    private socketService: SocketService,
  ) {}

  ngOnInit(): void {
    this.facade.fetchNotifications();
    this.facade.fetchSettings();
    this.facade.connectWebSocket();

    merge(
      this.toggleModeEffect(),
      this.listenForNotificationsEffect(),
      this.listenForOnboarding(),
    )
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe();
  }

  tourInit() {
    this.tourService.disableHotkeys();
    this.tourService.initialize(NOTIFICATIONS_TOUR_CONFIG, DEFAULT_TOUR_CONFIG);

    this.tourService.start();
  }

  finishOnboarding(stepName: string) {
    if (stepName === CUSTOMIZATIONS_ONBOARDING_STEP) {
      this.tourService.end();
      this.facade.finishOnboardingTour();
    }
  }

  toggleModeEffect() {
    return this.toggleMode$.pipe(
      debounceTime(100),
      filter(Boolean),
      debounceTime(100),
      withLatestFrom(this.mode$, this.notificationsList$),
      tap(([_, mode, notifications]) => {
        if (mode === NotificationEditorMode.COLLAPSED) {
          this.facade.toggleShowAllEnabled(true);
          this.facade.fetchNotifications();
          this.facade.fetchUnreadNotifications();
        }

        if (mode === NotificationEditorMode.EXPANDED) {
          this.facade.markAllAsSeen();
        }

        setTimeout(() => this.toggleMode(mode));
      }),
    );
  }

  closeNotificationList(currentMode: NotificationEditorMode) {
    if (currentMode === NotificationEditorMode.EXPANDED) {
      this.facade.markAllAsSeen();
      this.mode$.next(NotificationEditorMode.COLLAPSED);
    }
  }

  listenForOnboarding() {
    return merge(
      this.showOnboarding$.pipe(
        filter(Boolean),
        tap(() => this.tourInit()),
      ),
      this.listenForOnboardingStepChange(),
      this.listenForOnboardingEnd(),
    );
  }

  listenForOnboardingStepChange() {
    return this.tourService.stepShow$.pipe(
      tap((step) => {
        this.activeOnboardingStep$.next(
          this.onboardingTips.find((o) => o.stepName === step.step.stepId),
        );
      }),
    );
  }

  listenForOnboardingEnd() {
    return this.tourService.end$.pipe(
      withLatestFrom(this.showOnboarding$),
      tap(([, showOnboarding]) => {
        if (!showOnboarding) {
          return;
        }

        this.toggleMode$.next(true);
      }),
    );
  }

  listenForNotificationsEffect() {
    return this.socketService
      .getMessagesByDestination$(
        NOTIFICATION_DESTINATION,
        SocketType.PLATFORM_NOTIFICATION,
      )
      .pipe(
        tap((subscriptionMessage: Notification) => {
          this.facade.setIncomingNotification(subscriptionMessage);
        }),
        takeUntilDestroyed(this.destroyRef),
      );
  }

  toggleMode(mode: NotificationEditorMode): void {
    mode === NotificationEditorMode.COLLAPSED
      ? this.mode$.next(NotificationEditorMode.EXPANDED)
      : this.mode$.next(NotificationEditorMode.COLLAPSED);
  }
}
