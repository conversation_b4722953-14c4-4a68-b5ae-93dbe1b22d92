import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
} from '@angular/core';
import { Router } from '@angular/router';
import { Toast } from '@fincloud/core/toast';
import { StateLibNoopPageActions } from '@fincloud/state/utils';
import { ChatManagementControllerService } from '@fincloud/swagger-generator/communication';
import { Notification } from '@fincloud/swagger-generator/platform-notification';
import { NotificationMessageType } from '@fincloud/types/enums';
import { FinToastService } from '@fincloud/ui/toast';
import { catchError, of, take, tap } from 'rxjs';
@Component({
  selector: 'app-notification-system-card',
  templateUrl: './notification-system-card.component.html',
  styleUrls: ['./notification-system-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NotificationSystemCardComponent implements OnChanges {
  @Input() notification: Notification;

  @Input() customerKey: string;

  @Input() businessCaseName: string;

  @Output() closeNotificationPanel = new EventEmitter();

  @Output() hideNotification = new EventEmitter<Notification>();

  @Output() toggleReadStatus = new EventEmitter<Notification>();

  isCardHovered = false;

  isMenuOpened = false;

  notificationMessage: string;

  boldUserName: string;

  boldBusinessCaseName: string;

  elapsedTime: number;

  timePeriod: 'minutes' | 'hours' | 'days' | 'weeks';

  private readonly customerIsNotAPartOfTheCaseErrorCode = 'CMNS-1012';
  private readonly generalErrorText = $localize`:@@toast.message.error:Es ist ein Fehler aufgetreten`;
  private readonly noPermissionsToEnterCaseErrorText = $localize`:@@notificationSystem.notification.noPermissionsToEnterCase:Vorgang kann wegen fehlender Daten oder Berechtigungen nicht fortgesetzt werden.`;

  constructor(
    private router: Router,
    private finToastService: FinToastService,
    private chatManagementControllerService: ChatManagementControllerService,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.businessCaseName && changes.businessCaseName.currentValue) {
      this.boldBusinessCaseName = `<b>${this.businessCaseName}</b>`;

      if (this.boldUserName) {
        this.buildNotificationMessage();
      }
    }

    if (changes.notification && changes.notification.currentValue) {
      this.boldUserName = `<b>${this.notification.parameters.name}</b> `;
      this.calculateNotificationElapsedTime();

      if (this.businessCaseName) {
        this.buildNotificationMessage();
      }
    }
  }

  toggleIsCardHovered() {
    this.isCardHovered = !this.isCardHovered;
  }

  handleMenuContext(isOpened: boolean) {
    this.isMenuOpened = isOpened;
  }

  navigateToNotificationOrigin() {
    this.chatManagementControllerService
      .getAllChats({
        businessCaseId: this.notification.parameters.caseId,
        includeArchived: true,
      })
      .pipe(
        take(1),
        tap((caseChats) => {
          let route: string;
          if (this.notification.type === 'CHAT_RELATED') {
            const chat = caseChats.find(
              (caseChat) => caseChat.id === this.notification.parameters.chatId,
            );
            switch (chat.chatType) {
              case 'CONSORTIUM':
                route = `/${this.customerKey}/business-case/${this.notification.parameters.caseId}/chat/business-case`;
                break;
              case 'INTERNAL':
              case 'INTERNAL_GROUP':
              case 'INTERNAL_BILATERAL':
                route = `/${this.customerKey}/business-case/${this.notification.parameters.caseId}/chat/internal/${this.notification.parameters.chatId}`;
                break;
              case 'ON_TOPIC':
                route = `/${this.customerKey}/business-case/${this.notification.parameters.caseId}/chat/topic/${this.notification.parameters.chatId}`;
                break;
              case 'ONE_ON_ONE':
                route = `/${this.customerKey}/business-case/${this.notification.parameters.caseId}/chat/bilateral/${this.notification.parameters.chatId}`;
                break;
            }
          }
          this.router.navigate([route]);
        }),
        catchError((err) => {
          this.finToastService.show(
            Toast.error(
              err?.error?.code === this.customerIsNotAPartOfTheCaseErrorCode // if you aren't a part of the case, via checking for a specific error code (request usually provides 401)
                ? this.noPermissionsToEnterCaseErrorText // show a specific error toast
                : this.generalErrorText, // otherwise show the typical error toast
            ),
          );

          return of(StateLibNoopPageActions.noop());
        }),
      )
      .subscribe();

    this.closeNotificationPanel.emit();
  }

  private buildNotificationMessage() {
    switch (this.notification.message) {
      case NotificationMessageType.CHAT_MESSAGE_SENT:
        this.notificationMessage = $localize`:@@notificationSystem.notification.chatRelated.messageSent:${this.boldUserName} hat im Finanzierungsfall ${this.boldBusinessCaseName} eine neue Nachricht gesendet.`;
        break;
      case NotificationMessageType.CHAT_USER_TAGGED:
        this.notificationMessage = $localize`:@@notificationSystem.notification.chatRelated.userTagged:${this.boldUserName} hat Sie im Chat im Finanzierungsfall ${this.boldBusinessCaseName} getaggt.`;
        break;
      case NotificationMessageType.CHAT_ARCHIVED_AUTOMATICALLY:
        this.notificationMessage = $localize`:@@notificationSystem.notification.chatRelated.archivedAutomatically:Im Finanzierungsfall ${this.boldBusinessCaseName} wurde ein Chat automatisch archiviert.`;
        break;
      case NotificationMessageType.CHAT_REACTIVATED_BY_USER:
        this.notificationMessage = $localize`:@@notificationSystem.notification.chatRelated.reactivatedByUser:${this.boldUserName} hat einen Chat im Finanzierungsfall ${this.boldBusinessCaseName} reaktiviert.`;
        break;
      case NotificationMessageType.CHAT_CREATED_AUTOMATICALLY:
        this.notificationMessage = $localize`:@@notificationSystem.notification.chatRelated.createdAutomatically:Im Finanzierungsfall ${this.boldBusinessCaseName} wurde ein Chat automatisch erstellt.`;
        break;
      case NotificationMessageType.CHAT_REACTIVATED_AUTOMATICALLY:
        this.notificationMessage = $localize`:@@notificationSystem.notification.chatRelated.reactivatedAutomatically:Im Finanzierungsfall ${this.boldBusinessCaseName} wurde ein Chat automatisch reaktiviert.`;
        break;
      case NotificationMessageType.CHAT_ARCHIVED_MANUALLY:
        this.notificationMessage = $localize`:@@notificationSystem.notification.chatRelated.archivedManually:${this.boldUserName} hat einen Chat im Finanzierungsfall ${this.boldBusinessCaseName} archiviert.`;
        break;
    }
  }

  private calculateNotificationElapsedTime() {
    const now = new Date();
    const notificationDate = new Date(this.notification.timestamp);

    // Calculate the elapsed time and if negative we use 0
    const elapsedTimeInSeconds = Math.max(
      0,
      (now.getTime() - notificationDate.getTime()) / 1000,
    );

    if (elapsedTimeInSeconds < 3600) {
      this.timePeriod = 'minutes';
      this.elapsedTime = Math.floor(elapsedTimeInSeconds / 60);
    } else if (elapsedTimeInSeconds < 86400) {
      this.timePeriod = 'hours';
      this.elapsedTime = Math.floor(elapsedTimeInSeconds / 3600);
    } else if (elapsedTimeInSeconds < 604800) {
      this.timePeriod = 'days';
      this.elapsedTime = Math.floor(elapsedTimeInSeconds / 86400);
    } else {
      this.timePeriod = 'weeks';
      this.elapsedTime = Math.floor(elapsedTimeInSeconds / 604800);
    }
  }
}
