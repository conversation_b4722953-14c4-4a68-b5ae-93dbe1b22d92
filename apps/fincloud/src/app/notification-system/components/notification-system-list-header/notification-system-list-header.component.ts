import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
} from '@angular/core';

@Component({
  selector: 'app-notification-system-list-header',
  templateUrl: './notification-system-list-header.component.html',
  styleUrls: ['./notification-system-list-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NotificationSystemListHeaderComponent implements OnChanges {
  @Input() hasNotifications: boolean;

  @Output() updateAllNotificationsAsRead = new EventEmitter<boolean>();

  @Output() showAllEnabled = new EventEmitter<boolean>();

  @Output() openSettings = new EventEmitter<boolean>();

  public activeTab: 'all' | 'unread' = 'all';

  public goToSettings() {
    this.openSettings.emit();
  }

  public onUpdateAllNotificationsAsRead() {
    setTimeout(() => this.updateAllNotificationsAsRead.emit(true));
    this.activeTab = 'all';
  }

  ngOnChanges(changes: SimpleChanges) {
    if (
      this.activeTab === 'unread' &&
      changes.hasNotifications.previousValue &&
      !changes.hasNotifications.currentValue
    ) {
      this.showAllEnabled.emit(true);
      this.activeTab = 'all';
    }
  }
}
