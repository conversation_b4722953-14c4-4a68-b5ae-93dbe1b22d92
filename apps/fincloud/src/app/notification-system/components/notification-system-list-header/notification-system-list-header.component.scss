@use 'styles/src/lib/common';

:host ::ng-deep {
  .tabs-wrapper ul.nav-tabs {
    gap: 1rem;
  }

  .tabs-wrapper ul.nav-tabs li.nav-li a.nav-link {
    padding-bottom: 0;
    @include common.heading5(600);
  }
}

.title {
  padding: 1rem 1rem 1.2rem 1.2rem;
}

.list-header-options {
  width: max-content;
}

.nav-li {
  width: 10rem;

  .nav-link {
    padding-left: 1rem !important;
  }

  &:first-child {
    width: 5rem;

    .nav-link {
      padding-left: 1.2rem !important;
    }
  }
}
