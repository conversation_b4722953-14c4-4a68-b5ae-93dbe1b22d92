<div
  class="header-container d-flex justify-content-between align-items-center notifications-context"
>
  <h4
    class="title notifications-context"
    i18n="@@notificationSystem.header.title"
  >
    Benachrichtigungen
  </h4>
  <ui-actions-menu
    class="notifications-context"
    [optionsTemplate]="options"
    [hideArrow]="true"
    [menuOffset]="-60"
  ></ui-actions-menu>
</div>
<div class="tabs-wrapper notifications-context" header>
  <ul
    ngbNav
    #nav="ngbNav"
    class="nav-tabs notifications-context"
    [(activeId)]="activeTab"
  >
    <li
      ngbNavItem="all"
      class="nav-li notifications-context"
      (click)="showAllEnabled.emit(true)"
    >
      <a
        ngbNavLink
        class="nav-link notifications-context"
        i18n="@@notificationSystem.header.filters.all"
        >Alle</a
      >
    </li>
    @if (hasNotifications) {
      <li
        ngbNavItem="unread"
        class="nav-li"
        (click)="showAllEnabled.emit(false)"
      >
        <a
          ngbNavLink
          class="nav-link notifications-context"
          i18n="@@notificationSystem.header.filters.unread"
          >Ungelesen</a
        >
      </li>
    }
  </ul>
</div>

<ng-template #options>
  <div class="list-header-options">
    @if (hasNotifications) {
      <ui-actions-menu-item
        class="notifications-context"
        iconName="done_all"
        iconSize="medium"
        (clicked)="onUpdateAllNotificationsAsRead()"
        i18n-label="@@notificationSystem.header.actions.markAllAsRead"
        label="Alle als gelesen markieren"
      ></ui-actions-menu-item>
    }
    <ui-actions-menu-item
      class="notifications-context"
      iconName="settings"
      (clicked)="goToSettings()"
      iconSize="medium"
      i18n-label="@@notificationSystem.header.actions.settings"
      label="Einstellungen"
    ></ui-actions-menu-item>
  </div>
</ng-template>
