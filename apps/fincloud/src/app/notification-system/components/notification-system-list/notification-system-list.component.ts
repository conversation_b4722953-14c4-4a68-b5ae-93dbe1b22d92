import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  ElementRef,
  EventEmitter,
  Output,
  ViewChild,
} from '@angular/core';
import {
  Observable,
  Subject,
  fromEvent,
  map,
  merge,
  tap,
  withLatestFrom,
} from 'rxjs';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DateService, PAST_TIME_SUFFIX } from '@fincloud/core/date';
import { NotificationSystemFacade } from '@fincloud/state/notifications';
import { CONTEXT_SUPPORTED_CLASSES } from '@fincloud/utils';

@Component({
  selector: 'app-notification-system-list',
  templateUrl: './notification-system-list.component.html',
  styleUrls: ['./notification-system-list.component.scss'],
  providers: [
    DateService,
    {
      provide: PAST_TIME_SUFFIX,
      useValue: $localize`:@@dateService.timePastSince:vor`,
    },
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NotificationSystemListComponent implements AfterViewInit {
  @ViewChild('listContainer') listContainer: ElementRef<HTMLElement>;

  @Output() closed = new EventEmitter<boolean>();

  notifications$ = this.facade.notificationsList$;

  sortedNotificationsArr$ = this.notifications$.pipe(
    map((notifications) =>
      Object.values(notifications).sort(
        (a, b) =>
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
      ),
    ),
  );

  customerKey$ = this.facade.customerKey$;

  businessCaseNames$ = this.facade.businessCaseNames$;

  unreadNotificationsCount$ = this.facade.getUnreadNotificationsCount$;

  hasNotifications$: Observable<boolean> = this.notifications$.pipe(
    map((notifications) => !!Object.keys(notifications).length),
  );

  hasUnreadNotifications$: Observable<boolean> = this.notifications$.pipe(
    map(
      (notifications) =>
        !!Object.values(notifications).filter(
          (notification) => !notification.read,
        ).length,
    ),
  );

  isLoading$: Observable<boolean> = this.facade.notificationsIsLoading$;

  isLoadingNext$: Observable<boolean> = this.facade.isLoadingNext$;

  hasMore$: Observable<boolean> = this.facade.hasMore$;

  isShowAllEnabled$: Observable<boolean> = this.facade.isShowAllEnabled$;

  fetchNext$: Subject<boolean> = new Subject();

  markAllAsRead$: Subject<boolean> = new Subject();

  toggleShowAll$: Subject<boolean> = new Subject();

  showSettings = false;

  constructor(
    private destroyRef: DestroyRef,
    public facade: NotificationSystemFacade,
  ) {}

  ngAfterViewInit(): void {
    this.listContainer?.nativeElement?.focus();

    merge(
      this.handleCloseEventsEffect(),
      this.fetchNextEffect(),
      this.markAllAsReadEffect(),
      this.toggleShowAllEnabledEffect(),
    )
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe();
  }

  handleCloseEventsEffect() {
    return fromEvent(document, 'click').pipe(
      tap((event) => this.handleCloseEvent(event)),
    );
  }

  fetchNextEffect() {
    return this.fetchNext$.pipe(
      withLatestFrom(this.facade.pagingNumber$),
      tap(([_, pagingNumber]) => {
        this.facade.fetchNextNotifications(pagingNumber);
      }),
    );
  }

  markAllAsReadEffect() {
    return this.markAllAsRead$.pipe(
      tap(() => {
        this.facade.markAllAsRead();
        this.facade.clearUnreadNotifications();
        this.facade.toggleShowAllEnabled(true);
      }),
    );
  }

  toggleShowAllEnabledEffect() {
    return this.toggleShowAll$.pipe(
      tap((showAllEnabled) => {
        this.facade.toggleShowAllEnabled(showAllEnabled);
        this.facade.fetchNotifications();
      }),
    );
  }

  toggleShowSetting() {
    this.showSettings = !this.showSettings;
    if (!this.showSettings) {
      this.toggleShowAll$.next(true);
    }
  }

  handleCloseEvent(event: Event): void {
    const element = event.target as HTMLElement;

    if (this.isElementInContext(element)) {
      return;
    }

    this.closed.emit(true);
  }

  /* If the clicked element is in notifications window's context(visually)
  - we should not emit closed event */

  isElementInContext(element: HTMLElement): boolean {
    const elementClassList = element?.classList;
    const parentClassList = element?.parentElement?.classList;
    const isAvatar = !!element
      .closest('ui-avatar')
      ?.parentElement?.classList?.contains('notifications-context');
    const isActionsMenuItem = !!element.closest('ui-actions-menu-item');
    const isInContext = CONTEXT_SUPPORTED_CLASSES.some(
      (cssClass) =>
        elementClassList?.contains(cssClass) ||
        parentClassList?.contains(cssClass),
    );

    return isAvatar || isActionsMenuItem || isInContext;
  }

  getBusinessCaseNameForNotification(caseId: string): Observable<string> {
    return this.businessCaseNames$.pipe(
      map((caseNames) => {
        return caseNames.filter((caseName) => caseName.id === caseId)[0]
          ?.autoGeneratedBusinessCaseName;
      }),
    );
  }
}
