@if (!showSettings) {
  <div class="list-container notifications-context">
    <app-notification-system-list-header
      class="notifications-context"
      [hasNotifications]="(unreadNotificationsCount$ | async) > 0"
      (updateAllNotificationsAsRead)="markAllAsRead$.next(true)"
      (openSettings)="toggleShowSetting()"
      (showAllEnabled)="toggleShowAll$.next($event)"
    ></app-notification-system-list-header>
    @if (notifications$ | async; as notifications) {
      @if (isLoading$ | async | isFalsy) {
        @if (hasNotifications$ | async) {
          <ui-infinite-scroll
            #listContainer
            class="scrollbar-settings notifications-context tw-mt-[0.1rem] tw-h-[calc(100vh-20rem)]"
            appearance="standard"
            visibility="hover"
            [shouldScrollToBottom]="false"
            (scrolledDown)="this.fetchNext$.next(true)"
          >
            @for (
              notification of sortedNotificationsArr$ | async;
              track notification
            ) {
              <div class="card-list notifications-context tw-h-auto">
                <app-notification-system-card
                  [notification]="notification"
                  [businessCaseName]="
                    getBusinessCaseNameForNotification(
                      notification.parameters.caseId
                    ) | async
                  "
                  [customerKey]="customerKey$ | async"
                  (closeNotificationPanel)="closed.emit(true)"
                  (hideNotification)="facade.hideNotification($event)"
                  (toggleReadStatus)="facade.toggleReadStatus($event)"
                ></app-notification-system-card>
              </div>
            }
            @if (
              (isShowAllEnabled$ | async) &&
              (hasMore$ | async | isFalsy) &&
              (hasNotifications$ | async) &&
              (hasUnreadNotifications$ | async | isFalsy)
            ) {
              <div class="up-to-date">
                <ui-icon name="svgNotificationSystemEmptyState"></ui-icon>
                <div i18n="@@notificationSystem.notificationList.upToDate">
                  Sie sind auf dem neuesten Stand.
                </div>
              </div>
            }
            @if (hasNotifications$ | async | isFalsy) {
              <div class="no-more notifications-context">
                <ng-container *ngTemplateOutlet="emptyStateTpl"></ng-container>
              </div>
            }
          </ui-infinite-scroll>
        } @else {
          <div class="empty-state notifications-context">
            <ui-icon name="svgNotificationSystemEmptyState"></ui-icon>
            @if (isShowAllEnabled$ | async) {
              <div
                class="notifications-context"
                i18n="@@notificationSystem.notificationList.allTab.empty"
              >
                Derzeit gibt es keine neuen Benachrichtigungen.
              </div>
            }
            @if (isShowAllEnabled$ | async | isFalsy) {
              <div
                class="notifications-context"
                i18n="@@notificationSystem.notificationList.unreadTab.empty"
              >
                Derzeit gibt es keine ungelesenen Benachrichtigungen.
              </div>
            }
          </div>
        }
        @if (isLoadingNext$ | async) {
          <div class="loading-next notifications-context">
            <div class="spinner-border notifications-context"></div>
          </div>
        }
      } @else {
        <div class="loading-spinner notifications-context">
          <div class="spinner-border notifications-context"></div>
        </div>
      }
    }
  </div>
} @else {
  <div class="settings notifications-context">
    <app-notification-settings
      (goBack)="toggleShowSetting()"
    ></app-notification-settings>
  </div>
}

<ng-template #emptyStateTpl>
  <div class="empty-state notifications-context">
    <ui-icon name="svgNotificationSystemEmptyState"></ui-icon>
    @if (isShowAllEnabled$ | async) {
      <div
        class="notifications-context"
        i18n="@@notificationSystem.notificationList.allTab.empty"
      >
        Derzeit gibt es keine neuen Benachrichtigungen.
      </div>
    }
    @if (isShowAllEnabled$ | async | isFalsy) {
      <div
        class="notifications-context"
        i18n="@@notificationSystem.notificationList.unreadTab.empty"
      >
        Derzeit gibt es keine ungelesenen Benachrichtigungen.
      </div>
    }
  </div>
</ng-template>

<ng-template #noMoreTpl> </ng-template>
