@use 'styles/src/lib/common';
:host {
  @apply tw-block;

  ::ng-deep .ng-scroll-viewport {
    padding: 0 !important;
  }
  .notifications-system-panel {
    @apply tw-flex;
    @apply tw-flex-col;
    @apply tw-w-[38rem];
    @apply tw-p-[0.5rem];
    @apply tw-mt-[0.5rem];
  }

  .card-list {
    margin-right: 1rem;
    min-width: 0;
  }

  .list-container {
    @extend .notifications-system-panel;

    &:focus {
      outline: none;
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    height: 35rem;
    text-align: center;
    @include common.heading4();
  }

  .loading-spinner {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    height: 35rem;
  }

  .loading-next {
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 5rem;
    padding: 2rem;
  }

  .spinner-border {
    height: 5rem;
    width: 5rem;
    color: theme('colors.color-text-disabled');
  }

  .settings {
    @extend .notifications-system-panel;
    padding: 0;
  }

  .up-to-date {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    align-items: center;
    margin: 5rem 0;
    @include common.heading4();
  }
}
