<div class="settings-header notifications-context">
  <ui-icon
    (click)="exitSettings()"
    class="go-back notifications-context"
    size="medium"
    name="left"
    color="primary"
  ></ui-icon>
  <h4 class="title" i18n="@@notificationSystem.settings.title">
    Einstellungen
  </h4>
</div>

<ui-horizontal-divider color="gray"></ui-horizontal-divider>

@if (settingsGroup) {
  <div class="settings-list notifications-context" [formGroup]="settingsGroup">
    <div class="setting notifications-context">
      <span
        class="name notifications-context"
        i18n="@@notificationSystem.settings.chat"
        >Chat</span
      >
      <ui-switch
        class="role-value notifications-context"
        formControlName="CHAT_RELATED"
      ></ui-switch>
    </div>
    <!-- Will be used in the next phases of the notification system! -->
    <!-- <div class="setting notifications-context">
      <span class="name notifications-context">Data Room</span>
      <ui-switch
        class="role-value notifications-context"
      formControlName="DATA_ROOM"></ui-switch>
    </div>
    <div class="setting notifications-context">
      <span class="name notifications-context">Business case</span>
      <ui-switch
        class="role-value notifications-context"
      formControlName="BUSINESS_CASE"></ui-switch>
    </div>
    <div class="setting notifications-context">
      <span class="name notifications-context">Cadr</span>
      <ui-switch
        class="role-value notifications-context"
      formControlName="CADR"></ui-switch>
    </div> -->
  </div>
}
