import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  EventEmitter,
  OnInit,
  Output,
} from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import {
  BehaviorSubject,
  defer,
  filter,
  map,
  merge,
  of,
  switchMap,
  take,
  tap,
} from 'rxjs';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NotificationSystemFacade } from '@fincloud/state/notifications';
import {
  NotificationSettingsForm,
  NotificationSystemSettings,
  NotificationType,
} from '@fincloud/types/models';

@Component({
  selector: 'app-notification-settings',
  templateUrl: './notification-settings.component.html',
  styleUrls: ['./notification-settings.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NotificationSettingsComponent implements OnInit {
  @Output() goBack = new EventEmitter();

  chatSettingsToggle = false;

  settingsGroup: FormGroup<NotificationSettingsForm>;

  groupInitiated$ = new BehaviorSubject<boolean>(false);

  controlChanges$ = defer(() => of(!!this.settingsGroup)).pipe(
    filter(Boolean),
    switchMap(() =>
      merge(
        this.settingsGroup.controls['CHAT_RELATED'].valueChanges.pipe(
          map((value) => ({ type: 'CHAT_RELATED', isEnabled: value })),
        ),
        // Will be used in the next phases of the notification system!

        // this.settingsGroup.controls['BUSINESS_CASE'].valueChanges.pipe(
        //   map((value) => ({ type: 'BUSINESS_CASE', isEnabled: value }))
        // ),
        // this.settingsGroup.controls['CADR'].valueChanges.pipe(
        //   map((value) => ({ type: 'CADR', isEnabled: value }))
        // ),
        // this.settingsGroup.controls['DATA_ROOM'].valueChanges.pipe(
        //   map((value) => ({ type: 'DATA_ROOM', isEnabled: value }))
        // )
      ),
    ),
  );

  constructor(
    private destroyRef: DestroyRef,
    private facade: NotificationSystemFacade,
    private _ref: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    merge(this.initiateGroupEffect(), this.updateSettingsEffect())
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe();
  }

  initiateGroupEffect() {
    return this.facade.settings$.pipe(
      filter(Boolean),
      tap((settings) => this.createSettingsFormGroup(settings)),
      take(1),
    );
  }

  updateSettingsEffect() {
    return defer(() => of(!!this.settingsGroup)).pipe(
      filter(Boolean),
      switchMap(() => this.controlChanges$),
      tap(({ type, isEnabled }) => {
        this.facade.updateSettings(type as NotificationType, isEnabled);
        if (isEnabled) {
          setTimeout(() => {
            this.facade.fetchUnreadNotifications();
          }, 500);
        }
      }),
    );
  }

  createSettingsFormGroup(settings: NotificationSystemSettings): void {
    const form: NotificationSettingsForm = {} as NotificationSettingsForm;

    for (const key in settings) {
      form[key as NotificationType] = new FormControl(
        settings[key as NotificationType],
      );
    }

    this.settingsGroup = new FormGroup(form);
  }

  exitSettings() {
    this.goBack.emit();
  }
}
