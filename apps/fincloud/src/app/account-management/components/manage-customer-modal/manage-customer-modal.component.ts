import {
  Component,
  DestroyRef,
  Input,
  OnInit,
  QueryList,
  ViewChild,
  ViewChildren,
} from '@angular/core';
import { FormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { cloneDeep, isEqual } from 'lodash-es';
import {
  BehaviorSubject,
  Observable,
  combineLatest,
  distinctUntilChanged,
  filter,
  map,
  merge,
  startWith,
  switchMap,
  tap,
} from 'rxjs';

import {
  FileUploaderOptions,
  UploadFileWithHintComponent,
} from '@fincloud/components/files';
import { TextInputComponent } from '@fincloud/components/text';
import { DEFAULT_ASPECT_RATIO } from '@fincloud/core/aspect-ratio';
import { bicValidator, inputWhitespaceValidator } from '@fincloud/core/form';
import { Toast } from '@fincloud/core/toast';

import {
  Customer,
  CustomerDto,
} from '@fincloud/swagger-generator/authorization-server';
import {
  BankingGroup,
  CustomerStatus,
  CustomerType,
  FeatureTemplate,
  SalesChannel,
} from '@fincloud/types/enums';
import { AppState } from '@fincloud/types/models';
import { Store } from '@ngrx/store';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { CUSTOMER_FEATURE_TEMPLATE_OPTIONS } from '@fincloud/neoshare/account-management';
import { FinToastService } from '@fincloud/ui/toast';
import { CUSTOMER_TYPE_LABELS } from '@fincloud/utils';
import { AccountManagementPageActions } from '../../+state/actions';
import {
  selectCustomerErrorCode,
  selectManageCustomersState,
  selectSelectedManagedCustomer,
} from '../../+state/selectors/account-management.selectors';
import { CreateCustomerForm } from '../../models/create-customer-form';
import { DUPLICATE_BIC_CODE } from '../../utils/duplicate-bic-code';
import { DUPLICATE_CUSTOMER_KEY_CODE } from '../../utils/duplicate-customer-key-code';

@Component({
  selector: 'app-create-customer-modal',
  templateUrl: './manage-customer-modal.component.html',
  styleUrls: ['./manage-customer-modal.component.scss'],
  // changeDetection: ChangeDetectionStrategy.OnPush,
  // Our file uploader + image rendered component is not compatible with OnPush for now
})
export class ManageCustomerModalComponent implements OnInit {
  @ViewChildren(TextInputComponent) textInputs: QueryList<TextInputComponent>;

  @ViewChild(UploadFileWithHintComponent)
  uploadFileComponent: UploadFileWithHintComponent;

  @Input() isEditMode?: boolean = false;

  readonly customerKeyValidator = Validators.pattern(/^[a-z0-9-]+$/);

  headerTextCreate = $localize`:@@accountManagement.customer.modal.create.label:Neuen Kunden anlegen`;

  headerTextEdit = $localize`:@@accountManagement.editCustomerDetails:Kundendaten ändern`;

  manageCustomerForm: FormGroup<CreateCustomerForm>;
  customerTheme: string;

  get customerKeyController() {
    return this.manageCustomerForm?.get('customerKey');
  }
  get customerTypeController() {
    return this.manageCustomerForm?.get('customerType');
  }
  get nameController() {
    return this.manageCustomerForm?.get('name');
  }
  get bicController() {
    return this.manageCustomerForm?.get('bic');
  }
  get bankingGroupController() {
    return this.manageCustomerForm?.get('bankingGroup');
  }
  get featureTemplateController() {
    return this.manageCustomerForm?.get('featureTemplate');
  }
  get salesChannelController() {
    return this.manageCustomerForm?.get('salesChannel');
  }
  get isMfaEnforcedController() {
    return this.manageCustomerForm?.get('isMfaEnforced');
  }

  readonly handleErrorCode: { [key: string]: () => void } = {
    [DUPLICATE_CUSTOMER_KEY_CODE]: () => {
      this.customerKeyController.setErrors({
        keyAlreadyExists: true,
      });
    },
    [DUPLICATE_BIC_CODE]: () => {
      this.bicController.setErrors({
        bicAlreadyExists: true,
      });
    },
  };

  aspectRatio = DEFAULT_ASPECT_RATIO;

  pageState$ = this.store
    .select(selectManageCustomersState)
    .pipe(distinctUntilChanged(isEqual));

  errorCode$ = this.store.select(selectCustomerErrorCode);

  uploadTextHint = $localize`:@@uploadOrganizationLogoModal.dropZoneTitle:Wählen Sie eine PNG Datei aus`;

  uploadOptions: FileUploaderOptions = {
    maxFilesCount: 1,
    allowedMimeType: ['image/png'],
    maxFileSize: ********,
  };

  customerTypeOptions: Record<'label' | 'value', string>[] = Object.keys(
    CustomerType,
  )
    .filter((type) => type !== CustomerType.INTERNAL)

    .sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()))
    .map((c) => {
      return {
        label: CUSTOMER_TYPE_LABELS[c] || c,
        value: c,
      };
    });

  readonly featureTemplateOptions = CUSTOMER_FEATURE_TEMPLATE_OPTIONS;

  bankingGroupOptions = [
    {
      label: 'Volksbank',
      group: BankingGroup.VOLKSBANK,
    },
    {
      label: 'Sparkasse',
      group: BankingGroup.SPARKASSE,
    },
    {
      label: $localize`:@@accountManagement.customer.createCustomerModal.other:Andere`,
      group: BankingGroup.OTHER,
    },
  ];

  salesChannelOptions = [
    { label: 'neoshare', channel: SalesChannel.NEOSHARE },
    { label: 'BMS', channel: SalesChannel.BMS },
  ];

  showBicInput = false;
  isFormInitiated$ = new BehaviorSubject<boolean>(false);

  constructor(
    private destroyRef: DestroyRef,
    private formBuilder: UntypedFormBuilder,
    public activeModal: NgbActiveModal,
    private finToastService: FinToastService,
    private store: Store<AppState>,
  ) {}

  ngOnInit() {
    this.store.dispatch(AccountManagementPageActions.clearCustomerErrorCode());

    merge(
      this.createFormGroupEffect(),
      this.handleBicControllerEffect(),
      this.handleErrorsEffect(),
      this.setFeatureTemplateEffect(),
    )
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe();
  }

  createFormGroup(customer: Customer): void {
    const defaultTheme = this.featureTemplateOptions.find(
      (template) => template.theme === FeatureTemplate.NEOSHARE,
    ).label;
    this.customerTheme = this.featureTemplateOptions.find(
      (template) => template.theme === customer?.themeName,
    )?.label;
    const salesChannel =
      customer?.salesChannel === SalesChannel.NEOSHARE
        ? customer?.salesChannel.toLocaleLowerCase()
        : customer?.salesChannel;
    const defaultBankingGroup = this.bankingGroupOptions.find(
      (bank) => bank.group === BankingGroup.OTHER,
    ).label;
    const customerBankingGroup = this.bankingGroupOptions.find(
      (bank) => bank.group === customer?.bankingGroup,
    )?.label;

    this.manageCustomerForm = this.formBuilder.group(
      {
        customerKey: [
          this.isEditMode ? customer?.key : '',
          [Validators.required, this.customerKeyValidator],
        ],
        name: [
          this.isEditMode ? customer?.name : '',
          [Validators.required, inputWhitespaceValidator()],
        ],
        isMfaEnforced: [
          this.isEditMode ? customer?.mfaEnforced : false,
          [Validators.required],
        ],
        bic: [
          this.isEditMode ? customer?.baseInfo?.bic : '',
          [Validators.required, bicValidator()],
        ],
        customerType: [
          this.isEditMode
            ? (CustomerType[customer?.customerType] as CustomerType)
            : null,
          Validators.required,
        ],
        bankingGroup: [
          this.isEditMode ? customerBankingGroup : defaultBankingGroup,
          [Validators.required],
        ],
        featureTemplate: [
          !this.isEditMode ? defaultTheme : null,
          [Validators.required],
        ],
        salesChannel: [
          this.isEditMode
            ? salesChannel
            : SalesChannel.NEOSHARE.toLocaleLowerCase(),
          [Validators.required],
        ],
      },
      { updateOn: 'blur' },
    );

    if (this.isEditMode) {
      this.customerTypeController.disable();
      this.customerKeyController.disable();
    }
  }

  handleBicControllerEffect(): Observable<CustomerType> {
    return this.isFormInitiated$.pipe(
      filter(Boolean),
      switchMap(() =>
        this.customerTypeController.valueChanges.pipe(
          startWith(this.customerTypeController.value),
          tap((customerType) => {
            if (customerType === CustomerType.BANK) {
              this.bicController.enable();
              this.bankingGroupController.enable();
              this.showBicInput = true;
            } else {
              this.bicController.setValue('');
              this.bicController.disable();
              this.bankingGroupController.disable();
              this.salesChannelController.disable();
              this.showBicInput = false;
            }

            this.manageCustomerForm.updateValueAndValidity();
          }),
        ),
      ),
    );
  }

  handleErrorsEffect(): Observable<string> {
    return this.errorCode$.pipe(
      filter(Boolean),
      tap((errorCode) => {
        if (
          !Object.prototype.hasOwnProperty.call(this.handleErrorCode, errorCode)
        ) {
          this.finToastService.show(Toast.error());
        } else {
          this.handleErrorCode[errorCode]();
        }
      }),
    );
  }

  deleteCustomerLogo(customer: Customer) {
    this.store.dispatch(
      AccountManagementPageActions.deleteCustomerLogo({
        customerKey: customer.key,
      }),
    );
  }

  manageCustomer(customer: Customer) {
    this.validateActiveFormControls();

    this.manageCustomerForm = cloneDeep(this.manageCustomerForm);

    if (this.manageCustomerForm.valid) {
      const customerDto = this.createCustomerDto(customer);
      const logo = this.uploadFileComponent?.selectedFileForUpload ?? null;

      this.isEditMode
        ? this.store.dispatch(
            AccountManagementPageActions.editCustomer({ customerDto, logo }),
          )
        : this.store.dispatch(
            AccountManagementPageActions.createCustomer({
              customerDto,
              logo,
            }),
          );
    }
  }

  createCustomerDto(customer: Customer): CustomerDto {
    if (this.isEditMode) {
      return {
        ...customer,
        name: this.nameController.value,
        mfaEnforced: this.isMfaEnforcedController.value,
        key: this.customerKeyController.value,
        customerType: this.customerTypeController.value,
        baseInfo: {
          bic: this.bicController.value ?? undefined,
        },
        themeName: this.featureTemplateOptions.find(
          (template) => template.label === this.featureTemplateController.value,
        )?.theme,
        bankingGroup: this.bankingGroupOptions.find(
          (group) => group.label === this.bankingGroupController.value,
        )?.group,
        salesChannel: this.salesChannelOptions.find(
          (saleChannel) =>
            saleChannel.label === this.salesChannelController.value,
        )?.channel,
      };
    }

    return {
      name: this.nameController.value,
      key: this.customerKeyController.value,
      mfaEnforced: this.isMfaEnforcedController.value,
      customerStatus: CustomerStatus.REGULAR,
      customerType: this.customerTypeController.value,
      baseInfo: {
        bic: this.bicController.enabled ? this.bicController.value : undefined,
      },
      themeName: this.featureTemplateOptions.find(
        (template) => template.label === this.featureTemplateController.value,
      )?.theme,
      bankingGroup: this.bankingGroupOptions.find(
        (group) => group.label === this.bankingGroupController.value,
      )?.group,
      salesChannel: this.salesChannelOptions.find(
        (saleChannel) =>
          saleChannel.label === this.salesChannelController.value,
      )?.channel,
    };
  }

  closeModal() {
    if (this.isEditMode) {
      this.store.dispatch(AccountManagementPageActions.clearSelectedCustomer());
    }
    this.activeModal.close({
      success: false,
    });
  }

  updateIsMfaEnforced(isEnabled: boolean) {
    this.isMfaEnforcedController.setValue(isEnabled);
  }

  createFormGroupEffect(): Observable<Customer> {
    return this.store.select(selectSelectedManagedCustomer).pipe(
      tap((customer) => {
        this.createFormGroup(customer);
        this.isFormInitiated$.next(true);
      }),
    );
  }

  setFeatureTemplateEffect(): Observable<string> {
    return this.isFormInitiated$.pipe(
      filter(Boolean),
      switchMap(() =>
        combineLatest([
          this.salesChannelController.valueChanges.pipe(
            startWith(this.salesChannelController.value),
          ),
          this.bankingGroupController.valueChanges.pipe(
            startWith(this.bankingGroupController.value),
          ),
        ]),
      ),
      map(([salesChannelVal, bankingGroupVal]) => {
        if (this.isEditMode && !this.featureTemplateController.value) {
          return this.customerTheme;
        }
        if (
          salesChannelVal === SalesChannel.BMS &&
          bankingGroupVal.toUpperCase() === BankingGroup.VOLKSBANK
        ) {
          return this.featureTemplateOptions.find(
            (template) => template.theme === FeatureTemplate.VOLKSBANK,
          )?.label;
        }
        return (
          this.featureTemplateOptions.find(
            (template) => template.theme === FeatureTemplate.NEOSHARE,
          )?.label || this.featureTemplateOptions[0].label
        );
      }),
      tap((updatedValue) => {
        this.featureTemplateController.setValue(updatedValue);
      }),
    );
  }

  private validateActiveFormControls(): void {
    for (const controlName in this.manageCustomerForm.controls) {
      const control = this.manageCustomerForm.get(controlName);

      if (!control.disabled) {
        control.markAsTouched();
      }
    }
  }
}
