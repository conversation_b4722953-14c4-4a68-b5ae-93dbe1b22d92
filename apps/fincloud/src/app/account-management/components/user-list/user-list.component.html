@if (userListState$ | async; as state) {
  <div class="tw-flex justify-content-between align-items-center tw-mb-3">
    <div class="d-flex gap-3 align-items-center">
      <ui-button
        icon="chevron_left"
        label="Kunden"
        i18n-label="@@accountManagement.customer.customerList.header"
        type="outline"
        size="small"
        (clicked)="navigateToCustomers()"
      ></ui-button>
      <div class="d-flex align-items-center gap-2">
        <h2 class="tw-cursor-default">
          <span>{{ state.selectedCustomerName }} </span>
          <span i18n="@@accountManagement.users">Nutzer</span>
        </h2>
        <ui-number-tag [count]="state.totalUsersCount"></ui-number-tag>
      </div>
    </div>
    <div class="col-auto create-user">
      @if (!state.isGuestCustomer) {
        <ui-button
          icon="add"
          label="Nutzer erstellen"
          i18n-label="@@userList.createUser"
          (clicked)="manageUser()"
        ></ui-button>
      }
    </div>
  </div>
  <div class="mb-3">
    <div class="search-filter-container">
      <ui-search-filter
        class="search-filter"
        #searchFilter
        (search)="searchUsersByAllFields($event)"
        i18n-placeholder="@@accountManagement.user.list.search.bar.placeholder"
        placeholder="Nach E-Mail-Adresse suchen"
      >
      </ui-search-filter>
    </div>
  </div>
  <div class="tabs-wrapper">
    <ul
      ngbNav
      #nav="ngbNav"
      class="nav-tabs tabs-container mb-3"
      [(activeId)]="activeTabId"
      (activeIdChange)="changeUserState($event)"
    >
      <li [ngbNavItem]="userState.ACTIVE" ngbNavItem class="nav-li">
        <a ngbNavLink class="nav-link"
          ><div class="d-flex align-items-center gap-2">
            <span
              i18n="@@accountManagement.users.active"
              [ngClass]="{ 'normal-text': activeTabId !== userState.ACTIVE }"
              >Aktiv</span
            >
            <ui-number-tag [count]="state.activeUsersCount"></ui-number-tag>
          </div>
        </a>
        <ng-template ngbNavContent>
          @if (!state.usersListCount && !state.isUsersLoading) {
            <ng-container
              *ngTemplateOutlet="emptyTableActiveTpl"
            ></ng-container>
          } @else {
            <div class="users-list">
              <ui-fluid-table
                [columns]="columns"
                [templates]="{
                  actions: t1,
                  username: defaultTpl,
                  firstName: fullNameTpl,
                  userRoles: defaultTpl,
                  createdByName: defaultTpl,
                  dateFormatted: defaultTpl,
                }"
                [rows]="usersTableData$ | async"
                [externalPaging]="true"
                [externalSorting]="true"
                [count]="state.usersListCount"
                [pageLimit]="state.userFilters.limit"
                [offset]="state.userFilters.offset / state.userFilters.limit"
                [selectionType]="null"
                [materialClassEnabled]="true"
                [showSpinner]="state.isUsersLoading"
                [footerHeight]="50"
                [defaultSort]="defaultUsersSorting$ | async"
                [noDataAvailableMessage]="''"
                (pageChange)="changePage($event)"
                (sortChange)="saveSelectedSorting($event)"
              >
              </ui-fluid-table>
            </div>
          }
        </ng-template>
      </li>
      @if (state.isRegularCustomer) {
        <li [ngbNavItem]="userState.INACTIVE" ngbNavItem class="nav-li">
          <a ngbNavLink class="nav-link"
            ><div class="d-flex align-items-center gap-2">
              <span
                i18n="@@accountManagement.users.deactivated"
                [ngClass]="{
                  'normal-text': activeTabId !== userState.INACTIVE,
                }"
                >Deaktiviert</span
              >
              <ui-number-tag
                [count]="state.inactiveUsersCount"
              ></ui-number-tag></div
          ></a>
          <ng-template ngbNavContent>
            @if (!state.usersListCount && !state.isUsersLoading) {
              <ng-container
                *ngTemplateOutlet="emptyTableDeactivatedTpl"
              ></ng-container>
            } @else {
              <div class="users-list">
                <ui-fluid-table
                  [columns]="columns"
                  [templates]="{
                    actions: t1,
                    username: defaultTpl,
                    firstName: fullNameTpl,
                    userRoles: defaultTpl,
                    createdByName: defaultTpl,
                    dateFormatted: defaultTpl,
                  }"
                  [rows]="usersTableData$ | async"
                  [externalPaging]="true"
                  [externalSorting]="true"
                  [count]="state.usersListCount"
                  [pageLimit]="state.userFilters.limit"
                  [offset]="state.userFilters.offset / state.userFilters.limit"
                  [selectionType]="null"
                  [materialClassEnabled]="true"
                  [showSpinner]="state.isUsersLoading"
                  [footerHeight]="50"
                  [defaultSort]="defaultUsersSorting$ | async"
                  [noDataAvailableMessage]="''"
                  (pageChange)="changePage($event)"
                  (sortChange)="saveSelectedSorting($event)"
                >
                </ui-fluid-table>
              </div>
            }
          </ng-template>
        </li>
      }
    </ul>
    <div [ngbNavOutlet]="nav" class="tab-template-content"></div>
  </div>
  <ng-template #t1 let-row="row" let-value="value">
    <ui-actions-menu
      #actionMenuElement
      [optionsTemplate]="options"
      [hasInteractionState]="true"
      [hideArrow]="true"
      [showMenuBottomAndLeft]="true"
      iconColor="subtle"
      iconSize="medium-large"
      class="group-actions"
      (visibilityChange)="onMenuVisibilityChange($event, row)"
      [isInternalPortal]="true"
    ></ui-actions-menu>
    <ng-template #options>
      @if (!state.isGuestCustomer) {
        <ui-actions-menu-item
          class="action-group-menu-item"
          iconName="edit-note"
          iconSize="medium"
          label="Informationen ändern"
          i18n-label="@@customerList.EditDetails"
          (clicked)="manageUser(true, row)"
        ></ui-actions-menu-item>
      }
      <ui-actions-menu-item
        *ngxPermissionsOnly="[permissions.PERM_0017]"
        class="action-group-menu-item"
        [template]="actionMenuToggleTpl"
      ></ui-actions-menu-item>
    </ng-template>
    <ng-template #actionMenuToggleTpl>
      @if (activePlatformManagers$ | async; as activePlatformManagers) {
        <div class="d-flex justify-content-between toggle-action-menu">
          @if (userCaseParticipation$ | async; as userCaseParticipation) {
            @if (this.activeTabId === userState.ACTIVE) {
              <ui-tooltip
                i18n-text="@@customerList.disableUserTooltip"
                text="Dieser Nutzer kann nicht deaktiviert werden, da es in dieser Organisation derzeit keinen Plattformmanager gibt."
                [disabled]="
                  !(
                    (row
                      | executeFunc
                        : isLastPlatformManager
                        : activePlatformManagers) &&
                    !!userCaseParticipation?.isSolePlatformUser
                  )
                "
              >
                <ui-switch
                  i18n-label="@@customerList.disableUserLabel"
                  label="Nutzer deaktivieren"
                  labelSize="label-small-size"
                  size="small"
                  [manuallyUpdate]="true"
                  [formControl]="deactivatedUserStateFormControl"
                  (triggerParentValueUpdate)="
                    deactivateUser(
                      row,
                      activePlatformManagers,
                      !!userCaseParticipation?.isSolePlatformUser,
                      actionMenuElement
                    )
                  "
                  [disabled]="
                    (row
                      | executeFunc
                        : isLastPlatformManager
                        : activePlatformManagers) &&
                    !!userCaseParticipation?.isSolePlatformUser
                  "
                ></ui-switch>
              </ui-tooltip>
            }
          }
          @if (this.activeTabId === userState.INACTIVE) {
            <ui-switch
              size="small"
              labelSize="label-small-size"
              i18n-label="@@customerList.enableUserLabel"
              label="Nutzer aktivieren"
              [formControl]="activatedUserStateFormControl"
              [manuallyUpdate]="true"
              (triggerParentValueUpdate)="activateUser(row, actionMenuElement)"
            ></ui-switch>
          }
        </div>
      }
    </ng-template>
  </ng-template>
  <ng-template #defaultTpl let-row="row" let-value="value">
    <ui-truncated-text>{{ value }}</ui-truncated-text>
  </ng-template>
  <ng-template #fullNameTpl let-row="row" let-value="value">
    <ui-truncated-text
      >{{ row?.firstName }} {{ row?.lastName }}
    </ui-truncated-text>
  </ng-template>
}

<!-- According to design this is the empty state, but we are temporary removing it -->
<ng-template #emptyCellTpl>
  <div class="empty-cell">N/A</div>
</ng-template>

<ng-template #emptyTableActiveTpl>
  <app-account-management-empty-state
    [header]="emptyStateHeader"
    [description]="emptyStateDescriptionActivated"
  ></app-account-management-empty-state>
</ng-template>

<ng-template #emptyTableDeactivatedTpl>
  <app-account-management-empty-state
    [header]="emptyStateHeader"
    [description]="emptyStateDescriptionDeactivated"
  ></app-account-management-empty-state>
</ng-template>
