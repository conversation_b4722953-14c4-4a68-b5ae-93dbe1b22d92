import {
  ChangeDetectionStrategy,
  Component,
  Inject,
  LOCALE_ID,
  OnInit,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { SearchFilterComponent } from '@fincloud/components/search-filter';
import { ModalService } from '@fincloud/core/modal';
import { CustomerStatus, Locale } from '@fincloud/types/enums';
import { FluidTableSorting } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { isEqual } from 'lodash-es';
import { debounceTime, distinctUntilChanged, tap } from 'rxjs';

import { Page } from '@fincloud/components/lists';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { InternalPortalCustomerDto } from '@fincloud/swagger-generator/authorization-server';
import { CUSTOMER_TYPE_LABELS } from '@fincloud/utils';
import { AccountManagementPageActions } from '../../+state/actions';
import {
  selectCustomerListState,
  selectCustomerListTableData,
  selectCustomerTableColumnsConfig,
  selectDefaultCustomersSorting,
} from '../../+state/selectors/account-management.selectors';
import { ManageCustomerModalComponent } from '../manage-customer-modal/manage-customer-modal.component';

@Component({
  selector: 'app-customer-list',
  templateUrl: './customer-list.component.html',
  styleUrls: ['./customer-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CustomerListComponent implements OnInit {
  @ViewChild('searchFilter') searchFilterComponent: SearchFilterComponent;

  emptyStateHeader = $localize`:@@accountManagement.customers.regular.empty.state.header:Keine Kunden`;

  emptyStateDescriptionRegular = $localize`:@@accountManagement.customers.regular.empty.state.description:Im Moment sind keine regulären Kunden verfügbar`;

  emptyStateDescriptionGuest = $localize`:@@accountManagement.customers.guest.empty.state.description:Im Moment sind keine Gastkunden verfügbar`;

  customerStatus = CustomerStatus;

  customerTypeLabels = CUSTOMER_TYPE_LABELS;

  activeTabId = CustomerStatus.REGULAR;

  columns$ = this.store.select(selectCustomerTableColumnsConfig);

  pageState$ = this.store.select(selectCustomerListState).pipe(
    tap(({ customerStatus }) => (this.activeTabId = customerStatus)),
    distinctUntilChanged(isEqual),
    debounceTime(100),
  );

  tableData$ = this.store
    .select(
      selectCustomerListTableData(
        this.regionalSettings.dateFormat,
        this.locale,
      ),
    )
    .pipe(debounceTime(100));

  defaultCustomersSorting$ = this.store.select(selectDefaultCustomersSorting);

  constructor(
    private regionalSettings: RegionalSettingsService,
    @Inject(LOCALE_ID) private locale: Locale,
    private modalService: ModalService,
    private store: Store,
    private router: Router,
    private activatedRoute: ActivatedRoute,
  ) {}

  ngOnInit(): void {
    this.store.dispatch(AccountManagementPageActions.fetchInitialCustomers());
  }

  manageCustomer(
    isEditMode = false,
    customer?: InternalPortalCustomerDto,
  ): void {
    if (isEditMode) {
      this.store.dispatch(
        AccountManagementPageActions.setSelectedCustomer({ customer }),
      );
    }

    this.modalService.openComponent(
      ManageCustomerModalComponent,
      { isEditMode },
      {
        windowClass: 'account-management-customers-modal',
      },
    );
  }

  searchCustomersByAllFields(searchTerm: string) {
    this.store.dispatch(
      AccountManagementPageActions.setCustomerFilters({
        filters: {
          fieldName: 'name',
          searchPhrase: searchTerm.toLowerCase(),
          offset: 0,
        },
      }),
    );
  }

  changePage(changePageEvent: Page) {
    this.store.dispatch(
      AccountManagementPageActions.setCustomerFilters({
        filters: {
          offset: changePageEvent.offset,
        },
      }),
    );
  }

  changeStatus(status: CustomerStatus) {
    this.store.dispatch(
      AccountManagementPageActions.changeStatus({
        status,
      }),
    );
    if (this.searchFilterComponent?.searchTerm?.length !== 0) {
      this.searchFilterComponent?.clearSearchTerm();
    }
  }

  navigateToUsers(customer: InternalPortalCustomerDto): void {
    this.store.dispatch(
      AccountManagementPageActions.setSelectedCustomer({ customer }),
    );

    this.router.navigate([`${customer.customerKey}`, 'users'], {
      relativeTo: this.activatedRoute,
    });
  }

  saveSelectedSorting(selectedCustomersSorting: FluidTableSorting): void {
    this.store.dispatch(
      AccountManagementPageActions.setCustomerFilters({
        filters: {
          orderBy: this.mapTablePropName(selectedCustomersSorting.prop),
          direction: selectedCustomersSorting.dir.toUpperCase() as
            | 'ASC'
            | 'DESC',
        },
      }),
    );
  }

  mapTablePropName(property: string): string {
    if (property === 'createdByName') {
      return 'createdBy';
    }

    if (property === 'customerKey') {
      return 'key';
    }

    return property;
  }

  manageTemplates(customer: InternalPortalCustomerDto): void {
    this.router.navigate([`${customer.customerKey}`, 'template-management'], {
      relativeTo: this.activatedRoute,
    });
  }
}
