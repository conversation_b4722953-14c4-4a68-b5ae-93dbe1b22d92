@if (pageState$ | async; as state) {
  <div class="tw-flex tw-justify-between tw-items-center tw-mb-3">
    <h2>
      <div class="d-flex align-items-center gap-2">
        <span
          class="tw-cursor-default"
          i18n="@@accountManagement.customer.customerList.header"
          >Kunden</span
        >
        <ui-number-tag [count]="state?.totalCustomersCount"></ui-number-tag>
      </div>
    </h2>
    <ui-button
      icon="add"
      i18n-label="
        @@accountManagement.customer.customerList.label.createCustomer"
      label="Kunde erstellen"
      corners="rounded"
      (clicked)="manageCustomer()"
    ></ui-button>
  </div>
  <div class="mb-3">
    <div class="search-filter-container">
      <ui-search-filter
        #searchFilter
        (search)="searchCustomersByAllFields($event)"
        i18n-placeholder="
          @@accountManagement.customer.list.search.bar.placeholder"
        placeholder="Nach Kundennamen suchen"
        class="search-filter"
      >
      </ui-search-filter>
    </div>
  </div>
  <div class="tabs-wrapper">
    <ul
      ngbNav
      #nav="ngbNav"
      class="nav-tabs tabs-container mb-3"
      [(activeId)]="activeTabId"
      (activeIdChange)="changeStatus($event)"
    >
      <li [ngbNavItem]="customerStatus.REGULAR" ngbNavItem class="nav-li">
        <a ngbNavLink class="nav-link"
          ><div class="d-flex align-items-center gap-2">
            <span
              i18n="@@customerStatus.Regular"
              [ngClass]="{
                'normal-text': activeTabId !== customerStatus.REGULAR,
              }"
              >Regulär</span
            >
            <ui-number-tag
              [count]="state?.regularCustomerCount"
            ></ui-number-tag>
          </div>
        </a>
        <ng-template ngbNavContent>
          @if (
            !(tableData$ | async)?.customerList.length &&
            !state?.isCustomersLoading
          ) {
            <ng-container
              *ngTemplateOutlet="emptyTableRegularTpl"
            ></ng-container>
          } @else {
            <div class="customers-list">
              <ui-fluid-table
                [columns]="columns$ | async"
                [templates]="{
                  actions: actionsTpl,
                  customerKey: defaultTpl,
                  customerType: customerTypeLabelTpl,
                  name: defaultTpl,
                  bic: defaultTpl,
                  createdByName: defaultTpl,
                  creationDate: defaultTpl,
                  sourceType: sourceTypeTpl,
                }"
                [rows]="(tableData$ | async)?.customerList"
                [externalPaging]="true"
                [externalSorting]="true"
                [offset]="
                  state.customerFilters.offset / state.customerFilters.limit
                "
                [count]="state?.customersCount"
                [pageLimit]="state?.customerFilters.limit"
                [selectionType]="null"
                [materialClassEnabled]="true"
                [showSpinner]="state?.isCustomersLoading"
                [footerHeight]="50"
                [defaultSort]="defaultCustomersSorting$ | async"
                [noDataAvailableMessage]="''"
                (pageChange)="changePage($event)"
                (sortChange)="saveSelectedSorting($event)"
              >
              </ui-fluid-table>
            </div>
          }
        </ng-template>
      </li>
      <li [ngbNavItem]="customerStatus.GUEST" ngbNavItem class="nav-li">
        <a ngbNavLink class="nav-link"
          ><div class="d-flex align-items-center gap-2">
            <span
              i18n="@@customerStatus.Guest"
              [ngClass]="{
                'normal-text': activeTabId !== customerStatus.GUEST,
              }"
              >Gast</span
            >
            <ui-number-tag
              [count]="state?.guestCustomerCount"
            ></ui-number-tag></div
        ></a>
        <ng-template ngbNavContent>
          @if (
            !(tableData$ | async)?.customerList.length &&
            !state?.isCustomersLoading
          ) {
            <ng-container *ngTemplateOutlet="emptyTableGuestTpl"></ng-container>
          } @else {
            <div class="customers-list">
              <ui-fluid-table
                [columns]="columns$ | async"
                [templates]="{
                  actions: actionsTpl,
                  customerKey: defaultTpl,
                  customerType: customerTypeLabelTpl,
                  name: defaultTpl,
                  bic: defaultTpl,
                  createdByName: defaultTpl,
                  creationDate: defaultTpl,
                  sourceType: sourceTypeTpl,
                }"
                [rows]="(tableData$ | async)?.customerList"
                [externalPaging]="true"
                [externalSorting]="true"
                [offset]="
                  state.customerFilters.offset / state.customerFilters.limit
                "
                [count]="state?.customersCount"
                [pageLimit]="state?.customerFilters.limit"
                [selectionType]="null"
                [materialClassEnabled]="true"
                [showSpinner]="state?.isCustomersLoading"
                [footerHeight]="50"
                [defaultSort]="defaultCustomersSorting$ | async"
                [noDataAvailableMessage]="''"
                (pageChange)="changePage($event)"
                (sortChange)="saveSelectedSorting($event)"
              >
              </ui-fluid-table>
            </div>
          }
        </ng-template>
      </li>
    </ul>
    <div [ngbNavOutlet]="nav" class="tab-template-content"></div>
  </div>
  <ng-template #actionsTpl let-row="row" let-value="value">
    <ui-actions-menu
      [optionsTemplate]="options"
      [hasInteractionState]="true"
      [hideArrow]="true"
      [showMenuBottomAndLeft]="true"
      iconColor="subtle"
      iconSize="medium-large"
      class="group-actions"
      [isInternalPortal]="true"
    ></ui-actions-menu>
    <ng-template #options>
      <ui-actions-menu-item
        class="action-group-menu-item"
        iconName="supervised-user-circle"
        iconSize="medium"
        label="Nutzer ansehen"
        i18n-label="@@accountManagement.actions.view.users"
        (clicked)="navigateToUsers(row)"
      ></ui-actions-menu-item>
      @if (activeTabId === customerStatus.REGULAR) {
        <ui-actions-menu-item
          class="action-group-menu-item"
          iconName="template"
          iconSize="medium"
          label="Vorlagen ansehen"
          i18n-label="@@allGroupsPortalActions.label.view.templates"
          (clicked)="manageTemplates(row)"
        ></ui-actions-menu-item>
      }
      @if (activeTabId === customerStatus.REGULAR) {
        <ui-actions-menu-item
          class="action-group-menu-item"
          iconName="edit-note"
          iconSize="medium"
          label="Informationen ändern"
          i18n-label="@@customerList.EditDetails"
          (clicked)="manageCustomer(true, row)"
        ></ui-actions-menu-item>
      }
    </ng-template>
  </ng-template>
  <ng-template #defaultTpl let-row="row" let-value="value">
    <ui-truncated-text>{{ value }}</ui-truncated-text>
  </ng-template>
}

<!-- According to design this is the empty state, but we are temporary removing it -->
<ng-template #emptyCellTpl>
  <div class="empty-cell">N/A</div>
</ng-template>

<ng-template #usersList> <app-user-list></app-user-list> </ng-template>

<ng-template #emptyTableRegularTpl>
  <app-account-management-empty-state
    [header]="emptyStateHeader"
    [description]="emptyStateDescriptionRegular"
  ></app-account-management-empty-state>
</ng-template>

<ng-template #emptyTableGuestTpl>
  <app-account-management-empty-state
    [header]="emptyStateHeader"
    [description]="emptyStateDescriptionGuest"
  ></app-account-management-empty-state>
</ng-template>

<ng-template #customerTypeLabelTpl let-row="row" let-value="value">
  <ui-truncated-text>
    {{ customerTypeLabels[value] }}
  </ui-truncated-text>
</ng-template>

<ng-template #sourceTypeTpl let-row="row">
  <ui-truncated-text [ngClass]="row.sourceTypeCellClass">
    {{ row.sourceType }}
  </ui-truncated-text>
</ng-template>
