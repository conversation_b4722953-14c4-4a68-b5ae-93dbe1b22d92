import { Component, Inject, LOCALE_ID } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { StateLibAccountManagementPageActions } from '@fincloud/state/account-management';

import {
  Template,
  TemplateDto,
} from '@fincloud/swagger-generator/business-case-manager';
import { Locale } from '@fincloud/types/enums';
import { BLANK_TEMPLATE, BLANK_TEMPLATE_CADR } from '@fincloud/utils';
import { Store } from '@ngrx/store';
import {
  selectCustomerTemplatesState,
  selectSelectedCustomerName,
} from '../../+state/selectors/account-management.selectors';

@Component({
  selector: 'app-customer-templates',
  templateUrl: './customer-templates.component.html',
  styleUrls: ['./customer-templates.component.scss'],
})
export class CustomerTemplatesComponent {
  public template?: Template;
  public rawTemplate?: string;
  public isEditMode = true;

  customerTemplatesState$ = this.store.select(selectCustomerTemplatesState);

  selectedCustomerName$ = this.store.select(selectSelectedCustomerName);

  constructor(
    private store: Store,
    @Inject(LOCALE_ID) private locale: Locale,
    private router: Router,
    private activatedRoute: ActivatedRoute,
  ) {}

  toggleViewMode(): void {
    this.router.navigate([`/`], {
      relativeTo: this.activatedRoute.parent,
    });
  }
  createCompanyTemplate() {
    this.store.dispatch(
      StateLibAccountManagementPageActions.setBusinessCaseTemplate({
        template: BLANK_TEMPLATE_CADR,
        isCadrSelected: true,
        isNewTemplateMode: true,
      }),
    );
  }

  createCaseTemplate() {
    this.store.dispatch(
      StateLibAccountManagementPageActions.setBusinessCaseTemplate({
        template: BLANK_TEMPLATE as TemplateDto,
        isCadrSelected: false,
        isNewTemplateMode: true,
      }),
    );
  }
}
