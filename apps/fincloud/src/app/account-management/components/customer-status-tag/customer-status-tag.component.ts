import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { CustomerStatus } from '../../models/customer-status';
import { CUSTOMERS_STATUS_NAME_MAPPING } from '../../utils/customer-status';

@Component({
  selector: 'app-customer-status-tag',
  templateUrl: './customer-status-tag.component.html',
  styleUrls: ['./customer-status-tag.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CustomerStatusTagComponent {
  @Input() status: CustomerStatus;
  customerStatusName = CUSTOMERS_STATUS_NAME_MAPPING;
}
