import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Toast } from '@fincloud/core/toast';
import {
  CustomerManagementControllerService,
  User,
  UserManagementControllerService,
} from '@fincloud/swagger-generator/authorization-server';
import { OrganizationLogoControllerService } from '@fincloud/swagger-generator/document';
import {
  CustomerStatus,
  TemplateErrorCode,
  TusUploadType,
  UserRole,
  UserState,
} from '@fincloud/types/enums';
import {
  AppState,
  CaseParticipation,
  CustomerWithUsername,
  UserWithCreatorName,
} from '@fincloud/types/models';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import {
  Observable,
  catchError,
  exhaustMap,
  filter,
  forkJoin,
  map,
  mergeMap,
  of,
  switchMap,
  tap,
} from 'rxjs';

import { extractSnapshotUniqueIdFromCustomerKey } from '@fincloud/core/demo-snapshot';
import {
  FileManagerService,
  TusDocumentUploadController,
} from '@fincloud/core/files';
import { ModalService } from '@fincloud/core/modal';
import { IdentityService } from '@fincloud/core/services';
import {
  StateLibAccountManagementPageActions,
  selectGetSelectedUserCustomerKey,
} from '@fincloud/state/account-management';
import { selectCustomerKey } from '@fincloud/state/customer';
import { selectIsDemoEnvironment } from '@fincloud/state/environment';
import { selectRouteParams } from '@fincloud/state/router';
import { selectUserId } from '@fincloud/state/user';
import { StateLibNoopPageActions } from '@fincloud/state/utils';
import { TemplateControllerService } from '@fincloud/swagger-generator/business-case-manager';
import {
  CadrTemplateControllerService,
  CompanyContactPersonControllerService,
} from '@fincloud/swagger-generator/company';
import { SnapshotControllerService } from '@fincloud/swagger-generator/demo';
import { FinToastService, FinToastType } from '@fincloud/ui/toast';
import { concatLatestFrom } from '@ngrx/operators';
import { difference, merge } from 'lodash-es';

import { templateToastError } from '@fincloud/utils';
import {
  AccountManagementApiActions,
  AccountManagementPageActions,
} from '../actions';
import {
  selectCustomerFilters,
  selectCustomerKeysWithLoadedDemoSource,
  selectCustomerStatus,
  selectGetAllCustomersInitialRequestParams,
  selectGetAllCustomersRequestParams,
  selectGetAllUsersInitialRequestParams,
  selectGetAllUsersRequestParams,
  selectIsCompanyEmailTaken,
  selectSelectedCustomer,
  selectSelectedCustomerKey,
  selectUserFilters,
  selectUserState,
} from '../selectors/account-management.selectors';

@Injectable()
export class AccountManagementEffects {
  toastErrorMessage = $localize`:@@templateEditor.toast.error.prefix:Vorlage konnte nicht aktualisert werden. Folgendes Problem wurde erkannt:`;
  toastErrorMessageNewTemplate = $localize`:@@templateEditor.toast.error.newTemplate.prefix:Vorlage konnte nicht angelegt werden. Folgendes Problem wurde erkannt:`;

  globalTemplateTranslation = $localize`:@@globalTemplateTranslation:Vorlage `;
  toastSuccessMessageSuffix = $localize`:@@templateEditor.toast.success.suffix:erfolgreich angelegt. `;

  toastSuccessUpdateMessageSuffix = $localize`:@@templateEditor.toast.success.update.suffix:erfolgreich aktualisiert.`;

  userUpdateToastMessageSuccess = $localize`:@@customerList.userUpdate.toast.success:Nutzer erfolgreich aktualisiert`;
  userUpdateToastMessageFailure = $localize`:@@customerList.userUpdate.toast.failure:Ein Fehler ist aufgetreten. Versuchen Sie es bitte erneut.`;

  constructor(
    private actions$: Actions,
    private customerManagementControllerService: CustomerManagementControllerService,
    private userManagementControllerService: UserManagementControllerService,
    private organizationLogoService: OrganizationLogoControllerService,
    private store: Store<AppState>,
    private finToastService: FinToastService,
    private modalService: ModalService,
    private companyContactPersonService: CompanyContactPersonControllerService,
    private router: Router,
    private templateService: TemplateControllerService,
    private cadrTemplateService: CadrTemplateControllerService,
    private snapshotControllerService: SnapshotControllerService,
    private fileManagerService: FileManagerService,
    private tusDocumentUploadController: TusDocumentUploadController,
    private identityService: IdentityService,
  ) {}
  /* -------------- Customers -------------- */

  fetchInitialCustomers$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        AccountManagementPageActions.fetchInitialCustomers,
        AccountManagementPageActions.changeStatus,
      ),
      concatLatestFrom(() =>
        this.store.select(selectGetAllCustomersInitialRequestParams),
      ),
      switchMap(([, requestParams]) =>
        forkJoin([
          this.customerManagementControllerService.getAllCustomersWithSearch({
            ...requestParams,
            customerStatuses: [CustomerStatus.REGULAR],
          }),
          this.customerManagementControllerService.getAllCustomersWithSearch({
            ...requestParams,
            customerStatuses: [CustomerStatus.GUEST],
          }),
        ]).pipe(
          map(([regularCustomersResponse, guestCustomersResponse]) =>
            AccountManagementApiActions.fetchInitialCustomersSuccess({
              regularCustomersResponse,
              guestCustomersResponse,
            }),
          ),
          catchError((error) =>
            of(
              AccountManagementApiActions.fetchInitialCustomersFailure({
                error,
              }),
            ),
          ),
        ),
      ),
    ),
  );

  fetchInitialCustomersSuccess$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementApiActions.fetchInitialCustomersSuccess),
      concatLatestFrom(() => this.store.select(selectCustomerStatus)),
      map(
        ([
          { regularCustomersResponse, guestCustomersResponse },
          customerStatus,
        ]) =>
          AccountManagementPageActions.attachUsersToCustomers({
            customersResponse:
              customerStatus === CustomerStatus.REGULAR
                ? regularCustomersResponse
                : guestCustomersResponse,
          }),
      ),
    ),
  );

  fetchCustomers$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        AccountManagementPageActions.fetchCustomers,
        AccountManagementApiActions.createCustomerSuccess,
        AccountManagementApiActions.editCustomerSuccess,
        AccountManagementPageActions.setCustomerFilters,
      ),
      concatLatestFrom(() =>
        this.store.select(selectGetAllCustomersRequestParams),
      ),
      switchMap(([, requestParams]) =>
        this.customerManagementControllerService
          .getAllCustomersWithSearch(requestParams)
          .pipe(
            map((customersResponse) =>
              AccountManagementApiActions.fetchCustomersSuccess({
                customersResponse,
              }),
            ),
            catchError((error) =>
              of(AccountManagementApiActions.fetchCustomersFailure({ error })),
            ),
          ),
      ),
    ),
  );

  fetchCustomersSuccess$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementApiActions.fetchCustomersSuccess),
      map(({ customersResponse }) =>
        /* We do this, so we can get the name if the user who created the case : / */
        AccountManagementPageActions.attachUsersToCustomers({
          customersResponse,
        }),
      ),
    ),
  );

  fetchCustomersCounts$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementApiActions.fetchCustomersSuccess),
      concatLatestFrom(() => this.store.select(selectCustomerFilters)),
      switchMap(([, { fieldName, searchPhrase }]) =>
        this.customerManagementControllerService
          .getAllCustomersByStatusesCounts({
            fieldName,
            searchPhrase,
          })
          .pipe(
            map(({ regularCustomerCount, guestCustomerCount }) =>
              AccountManagementApiActions.fetchCustomersCountsSuccess({
                customersCount: { regularCustomerCount, guestCustomerCount },
              }),
            ),
            catchError(() =>
              of(AccountManagementApiActions.fetchCustomersCountsFailure()),
            ),
          ),
      ),
    ),
  );

  fetchInitialCustomersCounts$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        AccountManagementApiActions.fetchInitialCustomersSuccess,
        AccountManagementPageActions.changeStatus,
      ),
      concatLatestFrom(() => this.store.select(selectCustomerFilters)),
      switchMap(([, { fieldName, searchPhrase }]) =>
        this.customerManagementControllerService
          .getAllCustomersByStatusesCounts({
            fieldName,
            searchPhrase,
          })
          .pipe(
            map(({ regularCustomerCount, guestCustomerCount }) =>
              AccountManagementApiActions.fetchInitialCustomersCountsSuccess({
                customersCount: { regularCustomerCount, guestCustomerCount },
              }),
            ),
            catchError(() =>
              of(AccountManagementApiActions.fetchCustomersCountsFailure()),
            ),
          ),
      ),
    ),
  );

  fetchTotalCustomersCounts$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        AccountManagementApiActions.fetchCustomersSuccess,
        AccountManagementApiActions.fetchInitialCustomersSuccess,
      ),
      switchMap(() =>
        this.customerManagementControllerService
          .getAllCustomersByStatusesCounts()
          .pipe(
            map(({ regularCustomerCount, guestCustomerCount }) =>
              AccountManagementApiActions.fetchTotalCustomersCountsSuccess({
                totalCustomersCount: {
                  regularCustomerCount,
                  guestCustomerCount,
                },
              }),
            ),
            catchError(() =>
              of(
                AccountManagementApiActions.fetchTotalCustomersCountsFailure(),
              ),
            ),
          ),
      ),
    ),
  );

  fetchCustomersFailure$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementApiActions.fetchCustomersFailure),
      map(() =>
        AccountManagementApiActions.setCustomersSuccess({
          customerList: [],
          customersResponse: {
            count: 0,
          },
        }),
      ),
    ),
  );

  attachUsersToCustomers$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementPageActions.attachUsersToCustomers),
      switchMap(({ customersResponse }) => {
        const userRequestsMap = new Map<string, Observable<User>>();
        if (customersResponse.customers.length === 0) {
          return of(
            AccountManagementApiActions.setCustomersSuccess({
              customerList: [],
              customersResponse,
            }),
          );
        }
        customersResponse.customers.forEach((customer) => {
          const userId = customer.createdBy;

          if (userId && !userRequestsMap.get(userId)) {
            userRequestsMap.set(
              userId,
              this.userManagementControllerService
                .getUserById({ userId })
                .pipe(
                  catchError(() => of({ firstName: '', lastName: '' } as User)),
                ),
            );
          }
        });

        const isNoCustomersWithCreatedById =
          [...userRequestsMap.values()].length === 0;

        if (isNoCustomersWithCreatedById) {
          const customersWithName = [...customersResponse.customers].map(
            (customer) =>
              ({
                ...customer,
                createdByName: '',
              }) as CustomerWithUsername,
          );

          return of(
            AccountManagementApiActions.setCustomersSuccess({
              customerList: customersWithName,
              customersResponse,
            }),
          );
        }

        return forkJoin([...userRequestsMap.values()]).pipe(
          map((users) => {
            const userNames = new Map<string, string>();
            users.forEach((user) => {
              userNames.set(user.id, `${user?.firstName} ${user?.lastName}`);
            });

            const customersWithName = [...customersResponse.customers].map(
              (customer) =>
                ({
                  ...customer,
                  createdByName: userNames.get(customer.createdBy),
                }) as CustomerWithUsername,
            );

            return AccountManagementApiActions.setCustomersSuccess({
              customerList: customersWithName,
              customersResponse,
            });
          }),
        );
      }),
    ),
  );

  attachSourceTypesToInitialCustomers$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementApiActions.fetchInitialCustomersSuccess),
      concatLatestFrom(() => [
        this.store.select(selectCustomerStatus),
        this.store.select(selectIsDemoEnvironment),
      ]),
      filter(([_, __, isDemoEnvironment]) => isDemoEnvironment),
      map(([action, customerStatus]) => {
        const customersResponse =
          customerStatus === CustomerStatus.REGULAR
            ? action.regularCustomersResponse
            : action.guestCustomersResponse;

        return customersResponse?.customers;
      }),
      map((customersList) => {
        if (!customersList?.length) {
          return StateLibNoopPageActions.noop();
        }

        return AccountManagementPageActions.attachSourceTypesToCustomers({
          customers: customersList,
        });
      }),
    ),
  );

  attachSourceTypesToCustomers$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementApiActions.fetchCustomersSuccess),
      concatLatestFrom(() => this.store.select(selectIsDemoEnvironment)),
      filter(([_, isDemoEnvironment]) => isDemoEnvironment),
      map(([action]) => {
        if (!action.customersResponse.customers?.length) {
          return StateLibNoopPageActions.noop();
        }

        return AccountManagementPageActions.attachSourceTypesToCustomers({
          customers: action.customersResponse.customers,
        });
      }),
    ),
  );

  getSnapshotUniqueIdFromCustomerKey$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementPageActions.attachSourceTypesToCustomers),
      concatLatestFrom(() =>
        this.store.select(selectCustomerKeysWithLoadedDemoSource),
      ),
      switchMap(([action, loadedCustomerKeys]) => {
        const responseCustomerKeys = action.customers.map(
          (customer) => customer.customerKey,
        );
        const notLoadedCustomerKeys = difference(
          responseCustomerKeys,
          loadedCustomerKeys,
        );

        const snapshotIdsForNotLoadedCustomers = merge(
          {},
          ...notLoadedCustomerKeys.map((customerKey) => ({
            [customerKey]: extractSnapshotUniqueIdFromCustomerKey(customerKey),
          })),
        );

        return notLoadedCustomerKeys
          .filter(
            (customerKey) => snapshotIdsForNotLoadedCustomers[customerKey],
          )
          .map((customerKey) =>
            AccountManagementPageActions.fetchSnapshotForCustomer({
              snapshotUniqueId: snapshotIdsForNotLoadedCustomers[customerKey],
              customerKey: customerKey,
            }),
          );
      }),
    ),
  );

  fetchSnapshotsForCustomer$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementPageActions.fetchSnapshotForCustomer),
      mergeMap(({ snapshotUniqueId, customerKey }) =>
        this.snapshotControllerService
          .getSnapshotByUniqueIdentifierWithoutSnapshotData({
            uniqueIdentifier: snapshotUniqueId,
          })
          .pipe(
            map((snapshot) => {
              if (!snapshot) {
                return AccountManagementApiActions.fetchSnapshotForCustomerFailure();
              }

              return AccountManagementApiActions.fetchSnapshotForCustomerSuccess(
                {
                  snapshot,
                  customerKey,
                },
              );
            }),
            catchError(() =>
              of(AccountManagementApiActions.fetchSnapshotForCustomerFailure()),
            ),
          ),
      ),
    ),
  );

  createCustomer$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementPageActions.createCustomer),
      concatLatestFrom(() => this.store.select(selectUserId)),
      switchMap(([{ customerDto, logo }, userId]) =>
        this.customerManagementControllerService
          .registerCustomer({ body: { ...customerDto, createdBy: userId } })
          .pipe(
            map((customer) =>
              AccountManagementApiActions.createCustomerSuccess({
                customer,
                logo,
              }),
            ),
            catchError((error) =>
              of(AccountManagementApiActions.createCustomerFailure({ error })),
            ),
          ),
      ),
    ),
  );

  createOrEditCustomerSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          AccountManagementApiActions.createCustomerSuccess,
          AccountManagementApiActions.editCustomerSuccess,
        ),
        tap(() => this.finToastService.show(Toast.success())),
      ),
    { dispatch: false },
  );

  editCustomer$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementPageActions.editCustomer),
      switchMap(({ customerDto, logo }) =>
        this.customerManagementControllerService
          .editCustomer({ customerKey: customerDto.key, body: customerDto })
          .pipe(
            map((customer) =>
              AccountManagementApiActions.editCustomerSuccess({
                customer,
                logo,
              }),
            ),
            catchError((error) =>
              of(AccountManagementApiActions.editCustomerFailure({ error })),
            ),
          ),
      ),
    ),
  );

  loadCustomerLogo$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementPageActions.setSelectedCustomer),
      switchMap(({ customer }) =>
        this.organizationLogoService
          .getOrganizationLogo({
            customerKey: customer.customerKey,
          })
          .pipe(
            map((logoData) =>
              AccountManagementApiActions.loadCustomerLogoSuccess({ logoData }),
            ),
            catchError((error) =>
              of(
                AccountManagementApiActions.loadCustomerLogoFailure({ error }),
              ),
            ),
          ),
      ),
    ),
  );

  tusUploadCustomerLogo$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        AccountManagementPageActions.createCustomer,
        AccountManagementPageActions.editCustomer,
      ),
      concatLatestFrom(() => this.store.select(selectCustomerKey)),
      filter(([{ customerDto, logo }, customerKey]) => !!logo),
      switchMap(([{ customerDto, logo }, customerKey]) => {
        const endpoint =
          this.tusDocumentUploadController.getUploadOrganizationLogoPath(
            customerDto.key,
          );

        return this.fileManagerService.upload({
          endpoint,
          typeOfTusUpload: TusUploadType.ORGANIZATION_LOGO,
          file: logo,
          customerKey,
          uploadId: this.identityService.generateKey(),
        });
      }),
      map(({ document }) =>
        AccountManagementApiActions.uploadCustomerLogoSuccess({
          logoData: document,
        }),
      ),
      catchError((error) =>
        of(AccountManagementApiActions.uploadCustomerLogoFailure({ error })),
      ),
    ),
  );

  deleteCustomerLogo$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementPageActions.deleteCustomerLogo),
      switchMap(({ customerKey }) =>
        this.organizationLogoService
          .deleteOrganizationLogo({
            customerKey,
          })
          .pipe(
            map(() => AccountManagementApiActions.deleteCustomerLogoSuccess()),
            catchError((error) => {
              this.finToastService.show(Toast.error());

              return of(
                AccountManagementApiActions.deleteCustomerLogoFailure({
                  error,
                }),
              );
            }),
          ),
      ),
    ),
  );

  fetchSelectedCustomer$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        AccountManagementPageActions.fetchSelectedCustomer,
        AccountManagementPageActions.setSelectedCustomer,
      ),
      switchMap(({ customer }) =>
        this.customerManagementControllerService
          .getCustomerByKey({
            customerKey: customer.customerKey,
          })
          .pipe(
            map((customer) =>
              AccountManagementApiActions.fetchSelectedCustomerSuccess({
                customer,
              }),
            ),
            catchError((error) =>
              of(
                AccountManagementApiActions.fetchSelectedCustomerFailure({
                  error,
                }),
              ),
            ),
          ),
      ),
    ),
  );

  fetchSelectedCustomerFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(AccountManagementApiActions.fetchSelectedCustomerFailure),
        tap(() => this.router.navigate(['/'])),
      ),
    { dispatch: false },
  );

  /* -------------- Users -------------- */
  fetchInitialUsers$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        AccountManagementPageActions.fetchInitialUsers,
        AccountManagementPageActions.changeUsersState,
        AccountManagementApiActions.deactivateUserSuccess,
        AccountManagementApiActions.activateUserSuccess,
      ),
      concatLatestFrom(() => [
        this.store.select(selectGetAllUsersInitialRequestParams),
        this.store.select(selectSelectedCustomerKey),
      ]),
      switchMap(([, requestParams, selectedCustomerKey]) =>
        forkJoin([
          this.userManagementControllerService.getAllUsersByCustomerAndStatesWithSearch(
            {
              ...requestParams,
              customerKey: selectedCustomerKey,
              userStates: [UserState.ACTIVE],
            },
          ),
          this.userManagementControllerService.getAllUsersByCustomerAndStatesWithSearch(
            {
              ...requestParams,
              customerKey: selectedCustomerKey,
              userStates: [UserState.INACTIVE],
            },
          ),
        ]).pipe(
          map(([activeUsersResponse, deactivatedUsersResponse]) =>
            AccountManagementApiActions.fetchInitialUsersSuccess({
              activeUsersResponse,
              deactivatedUsersResponse,
            }),
          ),
          catchError((error) =>
            of(
              AccountManagementApiActions.fetchInitialUsersFailure({
                error,
              }),
            ),
          ),
        ),
      ),
    ),
  );

  fetchUsers$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        AccountManagementPageActions.fetchUsers,
        AccountManagementPageActions.setUserFilters,
        AccountManagementApiActions.createUserSuccess,
        AccountManagementApiActions.editUserSuccess,
      ),
      concatLatestFrom(() => this.store.select(selectGetAllUsersRequestParams)),
      switchMap(([, requestParams]) =>
        this.userManagementControllerService
          .getAllUsersByCustomerAndStatesWithSearch(requestParams)
          .pipe(
            map((usersResponse) =>
              AccountManagementApiActions.fetchUsersSuccess({
                usersResponse,
              }),
            ),
            catchError((error) =>
              of(AccountManagementApiActions.fetchUsersFailure({ error })),
            ),
          ),
      ),
    ),
  );

  fetchActiveAccountManagers$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        AccountManagementPageActions.fetchActiveAccountManagers,
        AccountManagementApiActions.activateUserSuccess,
        AccountManagementApiActions.deactivateUserSuccess,
        AccountManagementApiActions.createUserSuccess,
        AccountManagementApiActions.editUserSuccess,
      ),
      concatLatestFrom(() => this.store.select(selectRouteParams)),
      switchMap(([, { selectedCustomerKey }]) =>
        this.userManagementControllerService
          .getAllUsersByCustomerAndStatesWithSearch({
            customerKey: selectedCustomerKey,
            userStates: [UserState.ACTIVE],
            offset: 0,
            limit: 10000,
            fieldName: 'userRoles.name',
            searchPhrase: UserRole.PLATFORM_MANAGER,
          })
          .pipe(
            map((usersResponse) =>
              AccountManagementApiActions.fetchActiveAccountManagersSuccess({
                activePlatformManagers: usersResponse.users,
              }),
            ),
            catchError((error) =>
              of(
                AccountManagementApiActions.fetchActiveAccountManagersFailure({
                  error,
                }),
              ),
            ),
          ),
      ),
    ),
  );

  fetchInitialUsersSuccess$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementApiActions.fetchInitialUsersSuccess),
      concatLatestFrom(() => this.store.select(selectUserState)),
      map(([{ activeUsersResponse, deactivatedUsersResponse }, userState]) =>
        AccountManagementPageActions.attachUserCreatorsToUsers({
          usersResponse:
            userState === UserState.ACTIVE
              ? activeUsersResponse
              : deactivatedUsersResponse,
        }),
      ),
    ),
  );

  fetchUsersSuccess$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementApiActions.fetchUsersSuccess),
      map(({ usersResponse }) =>
        AccountManagementPageActions.attachUserCreatorsToUsers({
          usersResponse,
        }),
      ),
    ),
  );

  fetchUsersCounts$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementApiActions.fetchUsersSuccess),
      concatLatestFrom(() => [
        this.store.select(selectSelectedCustomerKey),
        this.store.select(selectUserFilters),
      ]),
      switchMap(([, customerKey, { fieldName, searchPhrase }]) =>
        this.userManagementControllerService
          .getAllUsersByCustomerAndStateCount({
            customerKey,
            fieldName,
            searchPhrase,
          })
          .pipe(
            map(({ activeUsersCount, inactiveUsersCount }) =>
              AccountManagementApiActions.fetchUsersCountsSuccess({
                usersCount: { activeUsersCount, inactiveUsersCount },
              }),
            ),
            catchError(() =>
              of(AccountManagementApiActions.fetchUsersCountsFailure()),
            ),
          ),
      ),
    ),
  );

  fetchInitialUsersCounts$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementApiActions.fetchInitialUsersSuccess),
      concatLatestFrom(() => [
        this.store.select(selectSelectedCustomerKey),
        this.store.select(selectUserFilters),
      ]),
      switchMap(([, customerKey, { fieldName, searchPhrase }]) =>
        this.userManagementControllerService
          .getAllUsersByCustomerAndStateCount({
            customerKey,
            fieldName,
            searchPhrase,
          })
          .pipe(
            map(({ activeUsersCount, inactiveUsersCount }) =>
              AccountManagementApiActions.fetchInitialUsersCountsSuccess({
                usersCount: { activeUsersCount, inactiveUsersCount },
              }),
            ),
            catchError(() =>
              of(AccountManagementApiActions.fetchUsersCountsFailure()),
            ),
          ),
      ),
    ),
  );

  fetchTotalUsersCounts$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        AccountManagementApiActions.fetchUsersSuccess,
        AccountManagementApiActions.fetchInitialUsersSuccess,
      ),
      concatLatestFrom(() => [this.store.select(selectSelectedCustomerKey)]),
      switchMap(([, customerKey]) =>
        this.userManagementControllerService
          .getAllUsersByCustomerAndStateCount({
            customerKey,
          })
          .pipe(
            map(({ activeUsersCount, inactiveUsersCount }) =>
              AccountManagementApiActions.fetchTotalUsersCountsSuccess({
                totalUsersCount: {
                  activeUsersCount,
                  inactiveUsersCount,
                },
              }),
            ),
            catchError(() =>
              of(AccountManagementApiActions.fetchTotalUsersCountsFailure()),
            ),
          ),
      ),
    ),
  );

  fetchUsersFailure$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementApiActions.fetchUsersFailure),
      map(() =>
        AccountManagementApiActions.setUsersSuccess({
          usersList: [],
          usersResponse: { count: 0 },
        }),
      ),
    ),
  );

  attachUserCreatorsToUsers$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementPageActions.attachUserCreatorsToUsers),
      switchMap(({ usersResponse }) => {
        if (usersResponse.users.length === 0) {
          return of(
            AccountManagementApiActions.setUsersSuccess({
              usersList: [],
              usersResponse,
            }),
          );
        }

        const requests = usersResponse.users.map((user) =>
          this.userManagementControllerService
            .getUserById({
              userId: user.createdByUserId,
            })
            .pipe(
              catchError(() => of({ firstName: '', lastName: '' } as User)),
            ),
        );

        return forkJoin(requests).pipe(
          map((users) => {
            const usersWithCreatorName = usersResponse.users.map(
              (user, index) =>
                ({
                  ...user,
                  createdByName:
                    `${users[index].firstName} ${users[index].lastName}`.trim(),
                }) as UserWithCreatorName,
            );

            return AccountManagementApiActions.setUsersSuccess({
              usersList: usersWithCreatorName,
              usersResponse,
            });
          }),
        );
      }),
    ),
  );

  initiateUserCreation$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementPageActions.initiateUserCreation),
      concatLatestFrom(() => this.store.select(selectSelectedCustomer)),
      exhaustMap(([{ userDto }, { customerKey }]) =>
        this.companyContactPersonService
          .getContactPersonByEmail({
            contactPersonEmail: userDto.username,
            customerKey,
          })
          .pipe(
            map((contactPersonCompanyDto) =>
              AccountManagementApiActions.initiateUserCreationSuccess({
                userDto,
                contactPersonCompanyDto,
              }),
            ),
            catchError((error) => {
              this.finToastService.show(Toast.error());

              return of(
                AccountManagementApiActions.initiateUserCreationFailure({
                  error: null,
                }),
              );
            }),
          ),
      ),
    ),
  );

  initiateUserEdit$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementPageActions.initiateUserEdit),
      concatLatestFrom(() => this.store.select(selectSelectedCustomer)),
      exhaustMap(([{ request }, { customerKey }]) =>
        this.companyContactPersonService
          .getContactPersonByEmail({
            contactPersonEmail: request.body.username,
            customerKey,
          })
          .pipe(
            map((contactPersonCompanyDto) =>
              AccountManagementApiActions.initiateUserEditSuccess({
                contactPersonCompanyDto,
                request,
              }),
            ),
            catchError((error) => {
              this.finToastService.show(Toast.error());

              return of(
                AccountManagementApiActions.initiateUserEditFailure({
                  error,
                }),
              );
            }),
          ),
      ),
    ),
  );

  createUser$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementApiActions.initiateUserCreationSuccess),
      concatLatestFrom(() => [
        this.store.select(selectSelectedCustomer),
        this.store.select(selectIsCompanyEmailTaken),
      ]),
      filter(([, , isCompanyEmailTaken]) => !isCompanyEmailTaken),
      exhaustMap(([{ userDto }, { customerKey }]) =>
        this.userManagementControllerService
          .registerUser({
            customerKey,
            body: userDto,
          })
          .pipe(
            map((user) => {
              this.finToastService.show(Toast.success());

              return AccountManagementApiActions.createUserSuccess({ user });
            }),
            catchError((error) =>
              of(AccountManagementApiActions.createUserFailure({ error })),
            ),
          ),
      ),
    ),
  );

  editUser$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementApiActions.initiateUserEditSuccess),
      concatLatestFrom(() => this.store.select(selectIsCompanyEmailTaken)),
      filter(([, isCompanyEmailTaken]) => !isCompanyEmailTaken),
      exhaustMap(([{ request }]) => {
        return this.userManagementControllerService
          .updateUserDetails(request)
          .pipe(
            map((user) => {
              this.finToastService.show(
                Toast.success(this.userUpdateToastMessageSuccess),
              );

              return AccountManagementApiActions.editUserSuccess({ user });
            }),
            catchError((error) =>
              of(AccountManagementApiActions.editUserFailure({ error })),
            ),
          );
      }),
    ),
  );

  fetchUserCaseParticipation$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementPageActions.setSelectedUser),
      filter(({ user }) => !!user),
      concatLatestFrom(() => this.store.select(selectSelectedCustomerKey)),
      switchMap(([{ user }, customerKey]) => {
        return this.userManagementControllerService
          .getUserParticipationInBusinessCasesInternal({
            userId: user.userId,
            customerKey,
          })
          .pipe(
            map(({ userParticipation }) => {
              const caseParticipation = userParticipation.reduce(
                (result, item) => {
                  result.totalCases++;
                  if (item.lastUserForParticipant) {
                    result.isSolePlatformUser = true;
                    result.casesAsSoleParticipant++;
                  }
                  return result;
                },
                {
                  totalCases: 0,
                  casesAsSoleParticipant: 0,
                  isSolePlatformUser: false,
                } as CaseParticipation,
              );

              return AccountManagementApiActions.fetchUserParticipationInCasesSuccess(
                { caseParticipation },
              );
            }),
            catchError((error) =>
              of(
                AccountManagementApiActions.fetchUserParticipationInCasesFailure(
                  { error },
                ),
              ),
            ),
          );
      }),
    ),
  );

  closeModalOnSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          AccountManagementApiActions.createCustomerSuccess,
          AccountManagementApiActions.editCustomerSuccess,
          AccountManagementApiActions.createUserSuccess,
          AccountManagementApiActions.editUserSuccess,
        ),
        tap(() => this.modalService.closeActiveModals()),
      ),
    { dispatch: false },
  );

  /* -------------- Templates -------------- */

  fetchInitialBusinessCaseTemplates$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        AccountManagementPageActions.fetchInitialTemplates,
        AccountManagementApiActions.initiateBusinessCaseTemplateEditSuccess,
        StateLibAccountManagementPageActions.initiateBusinessCaseTemplateCreate,
        AccountManagementApiActions.fetchSelectedCustomerSuccess,
      ),
      concatLatestFrom(() =>
        this.store.select(selectGetSelectedUserCustomerKey),
      ),
      switchMap(([, selectedCustomerKey]) =>
        this.templateService
          .getAllTemplatesByAccountManager({
            customerKey: selectedCustomerKey,
          })
          .pipe(
            map((businessCaseTemplatesResponse) =>
              AccountManagementApiActions.fetchBusinessCaseTemplatesSuccess({
                businessCaseTemplatesResponse,
              }),
            ),
            catchError((error) =>
              of(
                AccountManagementApiActions.fetchBusinessCaseTemplatesFailure({
                  error,
                }),
              ),
            ),
          ),
      ),
    ),
  );

  fetchInitialCadrTemplates$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        AccountManagementPageActions.fetchInitialTemplates,
        AccountManagementApiActions.initiateCadrTemplateEditSuccess,
        AccountManagementApiActions.fetchSelectedCustomerSuccess,
      ),
      concatLatestFrom(() =>
        this.store.select(selectGetSelectedUserCustomerKey),
      ),
      switchMap(([, requestParams]) =>
        this.cadrTemplateService
          .getCadrTemplateByAccountManager({ customerKey: requestParams })
          .pipe(
            map((cadrResponse) =>
              AccountManagementApiActions.fetchCadrTemplatesSuccess({
                cadrTemplateResponse: cadrResponse,
              }),
            ),
            catchError((error) =>
              of(
                AccountManagementApiActions.fetchCadrTemplatesFailure({
                  error,
                }),
              ),
            ),
          ),
      ),
    ),
  );

  initiateBusinessCaseTemplateEdit$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        StateLibAccountManagementPageActions.initiateBusinessCaseTemplateEdit,
      ),
      exhaustMap((payload) =>
        this.templateService
          .editTemplateByAccountManager({
            templateId: payload.templateId,
            customerKey: payload.customerKey,
            body: payload.body,
          })
          .pipe(
            map((template) => {
              return AccountManagementApiActions.initiateBusinessCaseTemplateEditSuccess(
                {
                  template: template.updatedTemplate,
                  isCadrSelected: false,
                  isNewTemplateMode: false,
                },
              );
            }),
            catchError((error) => {
              return of(
                AccountManagementApiActions.initiateBusinessCaseTemplateEditFailure(
                  {
                    error,
                  },
                ),
              );
            }),
          ),
      ),
    ),
  );

  initiateCadrTemplateEdit$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibAccountManagementPageActions.initiateCadrTemplateEdit),
      exhaustMap((request) =>
        this.cadrTemplateService
          .editTemplateByAccountManager({
            customerKey: request.customerKey,
            body: request.body,
          })
          .pipe(
            map((template) => {
              return AccountManagementApiActions.initiateCadrTemplateEditSuccess(
                {
                  template,
                  isCadrSelected: true,
                  isNewTemplateMode: false,
                },
              );
            }),
            catchError((error) => {
              return of(
                AccountManagementApiActions.initiateCadrTemplateEditFailure({
                  error,
                }),
              );
            }),
          ),
      ),
    ),
  );

  initiateBusinessCaseTemplateCreate$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        StateLibAccountManagementPageActions.initiateBusinessCaseTemplateCreate,
      ),
      exhaustMap((createBusinessCaseTemplateRequest) =>
        this.templateService
          .createTemplateByAccountManager({
            customerKey: createBusinessCaseTemplateRequest.customerKey,
            body: createBusinessCaseTemplateRequest.body,
          })
          .pipe(
            map((createBusinessCaseResponse) => {
              return AccountManagementApiActions.initiateCreateNewBusinessCaseTemplateSuccess(
                {
                  template: createBusinessCaseResponse,
                },
              );
            }),
            catchError((error) => {
              return of(
                AccountManagementApiActions.initiateCreateNewBusinessCaseTemplateFailure(
                  {
                    error,
                  },
                ),
              );
            }),
          ),
      ),
    ),
  );

  activateUser$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementPageActions.activateUser),
      switchMap(({ userId }) =>
        this.userManagementControllerService.activateUser({ userId }).pipe(
          map(() => AccountManagementApiActions.activateUserSuccess()),
          catchError((error) =>
            of(AccountManagementApiActions.activateUserFailure({ error })),
          ),
        ),
      ),
    ),
  );

  deactivateUser$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountManagementPageActions.deactivateUser),
      switchMap(({ userId, managerUserId }) => {
        const data: {
          userId: string;
          managerUserId?: string;
        } = {
          userId,
        };

        if (managerUserId) {
          data.managerUserId = managerUserId;
        }

        return this.userManagementControllerService.deactivateUser(data).pipe(
          map(() => AccountManagementApiActions.deactivateUserSuccess()),
          catchError((error) =>
            of(AccountManagementApiActions.deactivateUserFailure({ error })),
          ),
        );
      }),
    ),
  );

  toggleUserStatusSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          AccountManagementApiActions.activateUserSuccess,
          AccountManagementApiActions.deactivateUserSuccess,
        ),
        tap(() =>
          this.finToastService.show({
            type: FinToastType.SUCCESS,
            message: this.userUpdateToastMessageSuccess,
          }),
        ),
      ),
    { dispatch: false },
  );

  initiateCadrTemplateCreate$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibAccountManagementPageActions.initiateCadrTemplateCreate),
      exhaustMap((createCadrTemplateRequest) =>
        this.cadrTemplateService
          .createTemplateByAccountManager({
            customerKey: createCadrTemplateRequest.customerKey,
            body: createCadrTemplateRequest.body,
          })
          .pipe(
            map((createCadrTemplateResponse) => {
              return AccountManagementApiActions.initiateCreateNewCadrTemplateSuccess(
                {
                  template: createCadrTemplateResponse,
                },
              );
            }),
            catchError((error) => {
              return of(
                AccountManagementApiActions.initiateCreateNewBusinessCaseTemplateFailure(
                  {
                    error,
                  },
                ),
              );
            }),
          ),
      ),
    ),
  );

  initiateCreateNewBusinessCaseTemplateSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          AccountManagementApiActions.initiateCreateNewBusinessCaseTemplateSuccess,
        ),
        tap((newTemplateResponse) =>
          this.finToastService.show({
            type: FinToastType.SUCCESS,
            message: `${this.globalTemplateTranslation} '${newTemplateResponse.template.name}' ${this.toastSuccessMessageSuffix}`,
          }),
        ),
      ),
    { dispatch: false },
  );

  toggleUserStatusFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          AccountManagementApiActions.activateUserFailure,
          AccountManagementApiActions.deactivateUserFailure,
        ),
        tap(() =>
          this.finToastService.show({
            type: FinToastType.ERROR,
            message: this.userUpdateToastMessageFailure,
          }),
        ),
      ),
    { dispatch: false },
  );

  initiateCreateNewCadrTemplateSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          AccountManagementApiActions.initiateCreateNewCadrTemplateSuccess,
        ),
        tap(() => {
          const cadrPlaceHolder = 'CADR';
          this.finToastService.show({
            type: FinToastType.SUCCESS,
            message: `${this.globalTemplateTranslation} ${cadrPlaceHolder} ${this.toastSuccessMessageSuffix}`,
          });
        }),
      ),
    { dispatch: false },
  );

  initiateCreateNewCadrTemplateFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          AccountManagementApiActions.initiateCreateNewBusinessCaseTemplateFailure,
        ),
        tap((newCadrError) => {
          const errorMessage = (
            newCadrError.error?.error as { message: string }
          )?.message;
          const errorCode: TemplateErrorCode = newCadrError.error?.error?.code;
          if (Object.values(TemplateErrorCode).includes(errorCode)) {
            this.finToastService.show({
              type: FinToastType.ERROR,
              message: templateToastError(errorMessage)[errorCode],
            });
          } else {
            this.finToastService.show({
              type: FinToastType.ERROR,
              message: $localize`:@@templateEditor.toast.error.message.ifNoErrorDisplayed2:Vorlage konnte nicht angelegt werden. Bitte überprüfen Sie Ihre Vorlage.`,
            });
          }
        }),
      ),
    { dispatch: false },
  );

  initiateBusinessCaseTemplateEditSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          AccountManagementApiActions.initiateBusinessCaseTemplateEditSuccess,
        ),
        tap((editedTemplate) => {
          this.finToastService.show({
            type: FinToastType.SUCCESS,
            message: `${this.globalTemplateTranslation} '${editedTemplate.template?.name}' ${this.toastSuccessUpdateMessageSuffix}`,
          });
        }),
      ),
    { dispatch: false },
  );

  initiateBusinessCaseTemplateEditFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          AccountManagementApiActions.initiateBusinessCaseTemplateEditFailure,
        ),
        tap((editedTemplateError) => {
          const errorMessage = (
            editedTemplateError.error?.error as { message: string }
          )?.message;
          const errorCode: TemplateErrorCode =
            editedTemplateError.error?.error?.code;
          if (Object.values(TemplateErrorCode).includes(errorCode)) {
            this.finToastService.show({
              type: FinToastType.ERROR,
              message: templateToastError(errorMessage)[errorCode],
            });
          } else {
            this.finToastService.show({
              type: FinToastType.ERROR,
              message: `${this.toastErrorMessage} ${errorMessage}`,
            });
          }
        }),
      ),
    { dispatch: false },
  );

  initiateCadrTemplateEditSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(AccountManagementApiActions.initiateCadrTemplateEditSuccess),
        tap((editedCadr) => {
          this.finToastService.show({
            type: FinToastType.SUCCESS,
            message: `${this.globalTemplateTranslation} '${editedCadr.template.versionDescription}' ${this.toastSuccessUpdateMessageSuffix}`,
          });
        }),
      ),
    { dispatch: false },
  );

  initiateCadrTemplateEditFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(AccountManagementApiActions.initiateCadrTemplateEditFailure),
        tap((editedCadrError) => {
          const errorMessage = (
            editedCadrError.error?.error as { message: string }
          )?.message;
          const errorCode: TemplateErrorCode =
            editedCadrError.error?.error?.code;
          if (Object.values(TemplateErrorCode).includes(errorCode)) {
            this.finToastService.show({
              type: FinToastType.ERROR,
              message: templateToastError(errorMessage)[errorCode],
            });
          } else {
            this.finToastService.show({
              type: FinToastType.ERROR,
              message: `${this.toastErrorMessage} ${errorMessage}`,
            });
          }
        }),
      ),
    { dispatch: false },
  );

  initiateCreateNewBusinessCaseTemplateFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          AccountManagementApiActions.initiateCreateNewBusinessCaseTemplateFailure,
        ),
        tap((newBusinessCaseError) => {
          const errorMessage = (
            newBusinessCaseError.error?.error as { message: string }
          )?.message;
          const errorCode: TemplateErrorCode =
            newBusinessCaseError.error?.error?.code;
          if (Object.values(TemplateErrorCode).includes(errorCode)) {
            this.finToastService.show({
              type: FinToastType.ERROR,
              message: templateToastError(errorMessage)[errorCode],
            });
          } else {
            this.finToastService.show({
              type: FinToastType.ERROR,
              message: $localize`:@@templateEditor.toast.error.message.ifNoErrorDisplayed:Vorlage konnte nicht aktualisert werden. Bitte überprüfen Sie Ihre Vorlage.`,
            });
          }
        }),
      ),
    { dispatch: false },
  );
}
