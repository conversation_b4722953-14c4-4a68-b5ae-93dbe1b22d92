import {
  CustomerWithUsername,
  SnapshotCustomerAdditionalInfo,
} from '@fincloud/types/models';

import { generateSnapshotDemoTypeCssClass } from '@fincloud/core/demo-snapshot';
import {
  CUSTOMER_SOURCE_TYPE_LABELS,
  CUSTOMER_TYPE_LABELS,
} from '@fincloud/utils';
import { transformDate } from './transform-date';

export function toCustomerTableRow(
  customer: CustomerWithUsername,
  defaultDateFormat: string,
  locale: string,
  additionalInfo: SnapshotCustomerAdditionalInfo,
  isDemoEnvironment: boolean,
) {
  return {
    ...customer,
    customerKey: customer.customerKey,
    name: customer.name,
    bic: customer.bic,
    customerTypeLabel: CUSTOMER_TYPE_LABELS[customer.customerType],
    createdBy: customer.createdByName,
    creationDate: transformDate(
      customer.creationDate,
      defaultDateFormat,
      locale,
    ),
    createdOn: customer.creationDate ?? '',
    ...(isDemoEnvironment && {
      source: additionalInfo?.source ?? CUSTOMER_SOURCE_TYPE_LABELS.MAIN,
      sourceType:
        CUSTOMER_SOURCE_TYPE_LABELS[additionalInfo?.sourceType ?? 'MAIN'],
      sourceTypeCellClass: generateSnapshotDemoTypeCssClass(
        additionalInfo?.sourceType,
      ),
    }),
  };
}
