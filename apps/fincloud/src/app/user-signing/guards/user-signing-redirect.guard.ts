import { inject } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  GuardR<PERSON>ult,
  MaybeAsync,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import { WindowRef } from '@fincloud/core/services';
import { RedirectEvent } from '@fincloud/types/models';
import { USER_SIGNING_REDIRECT_MAPPING } from '@fincloud/utils';

export function userSigningRedirectGuard(
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot,
): MaybeAsync<GuardResult> {
  const router = inject(Router);
  const windowRef = inject(WindowRef);

  const pathname = state.url;
  // Extract event name from URL, considering additional parameters, and take only the event name => example event => &event=session_timeout&la=de&e=46a23806-1bae-40fe-9df8-959a100deece
  const event = windowRef.nativeWindow.location.href
    .split('event=')[1]
    .split('&')[0];

  const redirectUrl = pathname.replace(
    '/redirect/',
    USER_SIGNING_REDIRECT_MAPPING[event as RedirectEvent],
  );

  return void router.navigate([redirectUrl ?? '']);
}
