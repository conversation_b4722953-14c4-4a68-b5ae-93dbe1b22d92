import { Inject, Injectable, LOCALE_ID } from '@angular/core';
import { FileService } from '@fincloud/core/files';
import { Toast } from '@fincloud/core/toast';
import { SpreadsheetExportControllerService } from '@fincloud/swagger-generator/dashboard';
import { Locale } from '@fincloud/types/enums';
import { FinToastService } from '@fincloud/ui/toast';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { catchError, map, mergeMap, of, tap } from 'rxjs';
import { formatExcelName } from '../../utils/excel-export-filename-formatter';
import {
  BankGeneralSpreadsheetsExportApiActions,
  BankGeneralSpreadsheetsExportPageActions,
  BankKpiSpreadsheetsExportApiActions,
  BankKpiSpreadsheetsExportPageActions,
  ExportStartedApiActions,
  RealEstateFinanceKpiExportApiActions,
  RealEstateFinanceKpiExportPageActions,
  RealEstatePortfolioGeneralExportApiActions,
  RealEstatePortfolioGeneralExportPageActions,
  RealEstatePortfolioKpiExportApiActions,
  RealEstatePortfolioKpiExportPageActions,
} from '../actions';

@Injectable()
export class ExportExcelEffects {
  exportBankGeneralSpreadsheet$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        BankGeneralSpreadsheetsExportPageActions.exportBankGeneralSpreadsheet,
      ),
      mergeMap((action) => {
        return this.spreadSheetExportControllerService
          .getBankGeneralSpreadsheetExport(action.payload.filters)
          .pipe(
            tap((blob: Array<string>) => {
              const suffix = $localize`:@@bank.dashboard.exportExcel.general.filename.suffix:Allgemein`;

              this.fileService.downloadBinary(
                blob as unknown as Blob,
                formatExcelName(this.locale, suffix),
                'application/octet-stream',
              );
            }),
            map((blob: Array<string>) =>
              BankGeneralSpreadsheetsExportApiActions.exportBankGeneralSpreadsheetSuccess(),
            ),
            catchError((err) =>
              of(
                BankGeneralSpreadsheetsExportApiActions.exportBankGeneralSpreadsheetFailure(
                  { payload: err },
                ),
              ),
            ),
          );
      }),
    );
  });

  exportBankKPISpreadsheet$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(BankKpiSpreadsheetsExportPageActions.exportBankKpiSpreadsheet),
      mergeMap((action) => {
        return this.spreadSheetExportControllerService
          .getBankKpiSpreadsheetExport(action.payload.filters)
          .pipe(
            tap((blob: Array<string>) => {
              const suffix = $localize`:@@bank.dashboard.exportExcel.kpi.filename.suffix:KPI`;

              this.fileService.downloadBinary(
                blob as unknown as Blob,
                formatExcelName(this.locale, suffix),
                'application/octet-stream',
              );
            }),
            map((blob: Array<string>) =>
              BankKpiSpreadsheetsExportApiActions.exportBankKpiSpreadsheetSuccess(),
            ),
            catchError((err) =>
              of(
                BankKpiSpreadsheetsExportApiActions.exportBankKpiSpreadsheetFailure(
                  { payload: err },
                ),
              ),
            ),
          );
      }),
    );
  });

  exportRealEstatePortfolioGeneralSpreadsheet$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        RealEstatePortfolioGeneralExportPageActions.exportRealEstatePortfolioGeneralSpreadsheet,
      ),
      mergeMap((action) => {
        return this.spreadSheetExportControllerService
          .getPortfolioGeneralSpreadsheetExport(action.payload.filters)
          .pipe(
            tap((blob: Array<string>) => {
              const suffix = $localize`:@@realEstate.dashboard.exportExcel.portfolio.general.filename.suffix:Portfolio - Allgemein `;

              this.fileService.downloadBinary(
                blob as unknown as Blob,
                formatExcelName(this.locale, suffix),
                'application/octet-stream',
              );
            }),
            map((blob: Array<string>) =>
              RealEstatePortfolioGeneralExportApiActions.exportRealEstatePortfolioGeneralSpreadsheetSuccess(),
            ),
            catchError((err) =>
              of(
                RealEstatePortfolioGeneralExportApiActions.exportRealEstatePortfolioGeneralSpreadsheetFailure(
                  {
                    payload: err,
                  },
                ),
              ),
            ),
          );
      }),
    );
  });

  exportRealEstatePortfolioKpiSpreadsheet$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        RealEstatePortfolioKpiExportPageActions.exportRealEstatePortfolioKpiSpreadsheet,
      ),
      mergeMap((action) => {
        return this.spreadSheetExportControllerService
          .getPortfolioKpiSpreadsheetExport(action.payload.filters)
          .pipe(
            tap((blob: Array<string>) => {
              const suffix = $localize`:@@realEstate.dashboard.exportExcel.portfolio.kpi.filename.suffix:Portfolio - KPI `;

              this.fileService.downloadBinary(
                blob as unknown as Blob,
                formatExcelName(this.locale, suffix),
                'application/octet-stream',
              );
            }),
            map((blob: Array<string>) =>
              RealEstatePortfolioKpiExportApiActions.exportRealEstatePortfolioKpiSpreadsheetSuccess(),
            ),
            catchError((err) =>
              of(
                RealEstatePortfolioKpiExportApiActions.exportRealEstatePortfolioKpiSpreadsheetFailure(
                  {
                    payload: err,
                  },
                ),
              ),
            ),
          );
      }),
    );
  });

  exportRealEstateFinanceKPISpreadsheet$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        RealEstateFinanceKpiExportPageActions.exportRealEstateFinanceKpiSpreadsheet,
      ),
      mergeMap((action) => {
        return this.spreadSheetExportControllerService
          .getFinanceKpiSpreadsheetExport(action.payload.filters)
          .pipe(
            tap((blob: Array<string>) => {
              const suffix = $localize`:@@realEstate.dashboard.exportExcel.finance.kpi.filename.suffix:Finanzdaten - KPI `;

              this.fileService.downloadBinary(
                blob as unknown as Blob,
                formatExcelName(this.locale, suffix),
                'application/octet-stream',
              );
            }),
            map((blob: Array<string>) =>
              RealEstateFinanceKpiExportApiActions.exportRealEstateFinanceKpiSpreadsheetSuccess(),
            ),
            catchError((err) =>
              of(
                RealEstateFinanceKpiExportApiActions.exportRealEstateFinanceKpiSpreadsheetFailure(
                  {
                    payload: err,
                  },
                ),
              ),
            ),
          );
      }),
    );
  });

  exportStarted$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        BankGeneralSpreadsheetsExportPageActions.exportBankGeneralSpreadsheet,
        BankKpiSpreadsheetsExportPageActions.exportBankKpiSpreadsheet,
        RealEstatePortfolioGeneralExportPageActions.exportRealEstatePortfolioGeneralSpreadsheet,
        RealEstatePortfolioKpiExportPageActions.exportRealEstatePortfolioKpiSpreadsheet,
        RealEstateFinanceKpiExportPageActions.exportRealEstateFinanceKpiSpreadsheet,
      ),
      tap(() =>
        this.finToastService.show(
          Toast.info(
            $localize`:@@excelSpreadsheetDownload.toast.info:Excel-Export gestartet. Ihr Download sollte in wenigen Sekunden verfügbar sein.`,
          ),
        ),
      ),
      mergeMap(() => {
        return of(ExportStartedApiActions.exportStartSuccess());
      }),
    );
  });

  exportFailed$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        BankGeneralSpreadsheetsExportApiActions.exportBankGeneralSpreadsheetFailure,
        BankKpiSpreadsheetsExportApiActions.exportBankKpiSpreadsheetFailure,
        RealEstatePortfolioGeneralExportApiActions.exportRealEstatePortfolioGeneralSpreadsheetFailure,
        RealEstatePortfolioKpiExportApiActions.exportRealEstatePortfolioKpiSpreadsheetFailure,
        RealEstateFinanceKpiExportApiActions.exportRealEstateFinanceKpiSpreadsheetFailure,
      ),
      tap(() =>
        this.finToastService.show(
          Toast.error(
            $localize`:@@excelSpreadsheetDownload.toast.error:Es ist ein Fehler aufgetreten, bitte versuchen Sie es erneut.`,
          ),
        ),
      ),
      mergeMap(() => {
        return of(ExportStartedApiActions.exportStartFailure());
      }),
    );
  });

  constructor(
    private actions$: Actions,
    private fileService: FileService,
    @Inject(LOCALE_ID) private locale: Locale,
    private spreadSheetExportControllerService: SpreadsheetExportControllerService,
    private finToastService: FinToastService,
  ) {}
}
