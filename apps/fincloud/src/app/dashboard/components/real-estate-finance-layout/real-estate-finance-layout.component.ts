import { Component, DestroyRef, OnInit } from '@angular/core';
import { KpiMetric } from '@fincloud/components/lists';

import { CurrencyPipe, DecimalPipe, PercentPipe } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { RemoveTrailingZerosPipe } from '@fincloud/core/pipes';
import { selectUserCustomerKey } from '@fincloud/state/user';
import { AppState } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { isNumber } from 'lodash-es';
import { BehaviorSubject, combineLatest, filter, tap } from 'rxjs';
import {
  selectFinanceKpiSpreadsheetData,
  selectFinancingStructureChartData,
  selectGeneralNumbersOverview,
} from '../../+state/selectors/dashboard.selectors';
import { DashboardGeneralNumbersOverview } from '../../models/bank-kpi-location-overview-widget';

@Component({
  selector: 'app-real-estate-finance-layout',
  templateUrl: './real-estate-finance-layout.component.html',
  styleUrls: ['./real-estate-finance-layout.component.scss'],
})
export class RealEstateFinanceLayoutComponent implements OnInit {
  kpiData$ = new BehaviorSubject(null);
  chartData$ = this.store.select(selectFinancingStructureChartData);
  financeKpiSpreadsheetData$ = this.store.select(
    selectFinanceKpiSpreadsheetData,
  );
  customerKey$ = this.store.select(selectUserCustomerKey);

  kpis: KpiMetric[] = [
    {
      key: 'numberOfCases',
      title: $localize`:@@dashboard.realEstate.finance.kpi.cases:Fälle`,
      value: null,
    },
    {
      key: 'totalLTV',
      title: $localize`:@@dashboard.realEstate.finance.kpi.totalLTV:LTV Gesamt`,
      value: null,
    },
    {
      key: 'totalLTC',
      title: $localize`:@@dashboard.realEstate.finance.kpi.totalLTC:LTC Gesamt`,
      value: null,
    },
    {
      key: 'totalMarketValue',
      title: $localize`:@@dashboard.realEstate.finance.kpi.totalMarketValue:Marktwert Gesamt`,
      value: null,
    },
  ];

  constructor(
    private destroyRef: DestroyRef,
    private currencyPipe: CurrencyPipe,
    private percentPipe: PercentPipe,
    private decimalPipe: DecimalPipe,
    private removeTrailingZeroesPipe: RemoveTrailingZerosPipe,
    private store: Store<AppState>,
  ) {}

  ngOnInit(): void {
    combineLatest([this.store.select(selectGeneralNumbersOverview)])
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        filter((d) => !!d),
        tap(([generalNumbersOverview]) => {
          if (generalNumbersOverview) {
            this.kpiData$.next(
              this.mapKpis(
                generalNumbersOverview as DashboardGeneralNumbersOverview,
              ),
            );
          }
        }),
      )
      .subscribe();
  }

  mapKpis(numbersOverview: DashboardGeneralNumbersOverview) {
    Object.entries(numbersOverview).forEach(([key, value]) => {
      if (!isNumber(value)) return;

      this.kpis = this.kpis.map((k) => {
        if (k.key === key) {
          switch (k.key) {
            case 'totalLTC':
            case 'totalLTV':
              // value is divided by 100 because the percent pipe works with fractions between 0 and 1 but our APIs return actual percentage between 0-100%
              k.value = this.removeTrailingZeroesPipe.transform(
                this.percentPipe.transform(value / 100),
              );
              break;
            case 'totalMarketValue':
              k.value = this.currencyPipe.transform(value);
              break;
            default:
              k.value = this.decimalPipe.transform(value);
              break;
          }
        }

        return k;
      });
    });

    return [this.kpis];
  }
}
