import { Component, DestroyRef, OnInit } from '@angular/core';

import { CurrencyPipe, DecimalPipe, PercentPipe } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AzureMapPopupService } from '@fincloud/components/azure-map';
import { KpiMetric } from '@fincloud/components/lists';
import { PointOfInterest } from '@fincloud/core/location';
import { RemoveTrailingZerosPipe } from '@fincloud/core/pipes';
import { selectUserCustomerKey } from '@fincloud/state/user';
import { Store } from '@ngrx/store';
import { isNumber } from 'lodash-es';
import { BehaviorSubject, Observable, combineLatest, filter, tap } from 'rxjs';
import {
  selectBankGeneralSpreadsheetData,
  selectBankKpiSpreadsheetData,
  selectGeneralNumbersOverview,
  selectPointsOfInterest,
} from '../../+state/selectors/dashboard.selectors';
import { DashboardGeneralNumbersOverview } from '../../models/bank-kpi-location-overview-widget';

@Component({
  selector: 'app-bank-corporate-real-estate-layout',
  templateUrl: './bank-corporate-real-estate-layout.component.html',
  styleUrls: ['./bank-corporate-real-estate-layout.component.scss'],
  providers: [AzureMapPopupService],
})
export class BankCorporateRealEstateLayoutComponent implements OnInit {
  kpiData$ = new BehaviorSubject(null);
  bankGeneralSpreadsheetData$ = this.store.select(
    selectBankGeneralSpreadsheetData,
  );
  bankKpiSpreadsheetData$ = this.store.select(selectBankKpiSpreadsheetData);
  customerKey$ = this.store.select(selectUserCustomerKey);

  kpis: KpiMetric[] = [
    {
      key: 'numberOfCases',
      title: $localize`:@@dashboard.bank.portfolio.realEstateKpiLocationOverviewWidget.cases:Fälle`,
      value: null,
    },
    {
      key: 'numberOfConsortialCases',
      flexBasis: '15%',
      title: $localize`:@@dashboard.bank.portfolio.realEstateKpiLocationOverviewWidget.allConsortialCases:Alle Konsortialfälle`,
      value: null,
    },
    {
      key: 'totalLTV',
      flexBasis: '5%',
      title: $localize`:@@dashboard.bank.portfolio.realEstateKpiLocationOverviewWidget.totalLtv:LTV Gesamt`,
      value: null,
    },
    {
      key: 'totalOwnInvestmentAmount',
      flexBasis: '20%',
      title: $localize`:@@dashboard.bank.portfolio.realEstateKpiLocationOverviewWidget.totalOwnInvestmentAmount:Gesamtkreditbetrag`,
      value: null,
    },
  ];

  getPointsOfInterest$: Observable<PointOfInterest[]> = this.store.select(
    selectPointsOfInterest,
  );

  activePopupId$: Observable<string> = this.azureMapPopupService.activePopupId$;

  constructor(
    private destroyRef: DestroyRef,
    private currencyPipe: CurrencyPipe,
    private percentPipe: PercentPipe,
    private removeTrailingZeroesPipe: RemoveTrailingZerosPipe,
    private decimalPipe: DecimalPipe,
    private store: Store,
    private azureMapPopupService: AzureMapPopupService,
  ) {}

  ngOnInit(): void {
    combineLatest([
      this.store.select(selectPointsOfInterest),
      this.store.select(selectGeneralNumbersOverview),
    ])
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        filter((d) => !!d),
        tap(([propertyMapLocations, generalNumbersOverview]) => {
          if (generalNumbersOverview) {
            const d = [
              this.mapKpis(
                generalNumbersOverview as DashboardGeneralNumbersOverview,
              ),
            ];
            this.kpiData$.next(d);
          }
        }),
      )
      .subscribe();
  }

  mapKpis(numbersOverview: DashboardGeneralNumbersOverview) {
    Object.entries(numbersOverview).forEach(([key, value]) => {
      if (!isNumber(value)) return;

      this.kpis.forEach((k) => {
        if (k.key === key) {
          const v = value.toString();

          switch (key) {
            case 'totalOwnInvestmentAmount':
              k.value = this.currencyPipe.transform(v);
              break;
            case 'totalLTV':
              // value is divided by 100 because the percent pipe works with fractions between 0 and 1 but our APIs return actual percentage between 0-100%
              k.value = this.removeTrailingZeroesPipe.transform(
                this.percentPipe.transform(value / 100),
              );
              break;
            default:
              k.value = this.decimalPipe.transform(v);
              break;
          }
        }
      });
    });

    return this.kpis;
  }
}
