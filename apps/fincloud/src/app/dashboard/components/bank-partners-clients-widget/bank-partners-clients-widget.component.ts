import {
  Component,
  DestroyRef,
  Inject,
  LOCALE_ID,
  OnInit,
} from '@angular/core';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import {
  StateLibCustomerPageActions,
  selectCustomerLogo,
} from '@fincloud/state/customer';
import { CustomerType, Locale } from '@fincloud/types/enums';
import { AppState, PillItem } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { combineLatest, filter, tap } from 'rxjs';
import {
  selectClientsOverviewData,
  selectFinancingPartnerOverviewData,
} from '../../+state/selectors/dashboard.selectors';
import { ClientTableRow } from '../../models/client-table-row';
import { DashboardClientsOverview } from '../../models/dashboard-clients-overview';
import { DashboardFinancingPartnersOverview } from '../../models/dashboard-financing-partners-overview';
import { FinancingPartnerTableRow } from '../../models/financing-partner-table-row';
import { getClientWidgetTableDefaultSort } from '../../utils/get-client-widget-table-default-sort';
import { getFinancingPartnerWidgetTableColumnsConfig } from '../../utils/get-financing-partner-widget-table-columns-config';
import { getFinancingPartnerWidgetTableDefaultSort } from '../../utils/get-financing-partner-widget-table-default-sort';
import { getClientWidgetTableColumnsConfig } from '../../utils/partners-clients-widget-table-columns';

@Component({
  selector: 'app-bank-partners-clients-widget',
  templateUrl: './bank-partners-clients-widget.component.html',
  styleUrls: ['./bank-partners-clients-widget.component.scss'],
})
export class BankPartnersClientsWidgetComponent implements OnInit {
  pills: PillItem[] = [
    {
      key: 'financingPartners',
      value: $localize`:@@dashbaord.bank.partnersClientsWidget.financingPartner:Finanzierungspartner`,
    },
    {
      key: 'clients',
      value: $localize`:@@dashbaord.bank.partnersClientsWidget.clients:Kunden`,
    },
  ];

  activePill = this.pills[0];

  financingPartnerTableColumns = getFinancingPartnerWidgetTableColumnsConfig(
    this.locale,
    this.regionalSettings,
  );
  financingPartnerTableDefaultSort =
    getFinancingPartnerWidgetTableDefaultSort();

  clientTableColumns = getClientWidgetTableColumnsConfig(
    this.locale,
    this.regionalSettings,
  );
  clientTableDefaultSort = getClientWidgetTableDefaultSort();

  financingPartnerTableRows: FinancingPartnerTableRow[];
  clientTableRows: ClientTableRow[];

  get isFinancingPartnerEmpty() {
    return (
      !!this.financingPartnerTableRows &&
      this.financingPartnerTableRows.length === 0
    );
  }

  get isClientsEmpty() {
    return !!this.clientTableRows && this.clientTableRows.length === 0;
  }

  constructor(
    private destroyRef: DestroyRef,
    private store: Store<AppState>,
    @Inject(LOCALE_ID) private locale: Locale,
    private regionalSettings: RegionalSettingsService,
  ) {}

  ngOnInit(): void {
    combineLatest([
      this.store.select(selectFinancingPartnerOverviewData),
      this.store.select(selectClientsOverviewData),
    ])
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        filter((d) => !!d),
        tap(([financingPartnerOverviewData, clientsOverviewData]) => {
          this.fetchFinancingPartnerLogos(
            financingPartnerOverviewData as DashboardFinancingPartnersOverview[],
          );
          this.generateFinancingPartnerTableRows(
            financingPartnerOverviewData as DashboardFinancingPartnersOverview[],
          );
          this.generateClientsTableRows(
            clientsOverviewData as DashboardClientsOverview[],
          );
        }),
      )
      .subscribe();
  }

  fetchFinancingPartnerLogos(
    financingPartnerOverviewData: DashboardFinancingPartnersOverview[],
  ) {
    financingPartnerOverviewData?.forEach((c) => {
      this.store.dispatch(
        StateLibCustomerPageActions.loadCustomerLogo({
          payload: { customerKey: c.financingPartnerKey },
        }),
      );
    });
  }

  generateFinancingPartnerTableRows(
    financingPartnerOverviewData: DashboardFinancingPartnersOverview[],
  ) {
    this.financingPartnerTableRows = financingPartnerOverviewData
      ?.map((c) => {
        return {
          financingPartner: c?.financingPartnerName,
          cases: c?.businessCases,
          participationAmount: c?.totalParticipationAmount,
          imgSrc: this.store.select(
            selectCustomerLogo(
              c?.financingPartnerKey,
              c?.financingPartnerType as CustomerType,
              true,
            ),
          ),
        };
      })
      .filter((row) => row.participationAmount > 0);
  }

  generateClientsTableRows(clientsOverviewData: DashboardClientsOverview[]) {
    this.clientTableRows = clientsOverviewData?.map((c) => {
      return {
        name: c?.clientName,
        cases: c?.businessCases,
        totalLoanAmount: c?.totalInvestmentAmount,
      };
    });
  }
}
