import { CurrencyPipe, DecimalPipe } from '@angular/common';
import { Component, DestroyRef, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AzureMapPopupService } from '@fincloud/components/azure-map';
import { KpiMetric } from '@fincloud/components/lists';
import { selectUserCustomerKey } from '@fincloud/state/user';
import { Store } from '@ngrx/store';
import { isNumber } from 'lodash-es';
import { BehaviorSubject, Observable, combineLatest, filter, tap } from 'rxjs';
import {
  selectGeneralNumbersOverview,
  selectPointsOfInterest,
  selectPortfolioGeneralSpreadsheetData,
  selectPortfolioKpiSpreadsheetData,
} from '../../+state/selectors/dashboard.selectors';
import { DashboardGeneralNumbersOverview } from '../../models/bank-kpi-location-overview-widget';

@Component({
  selector: 'app-real-estate-portfolio-layout',
  templateUrl: './real-estate-portfolio-layout.component.html',
  styleUrls: ['./real-estate-portfolio-layout.component.scss'],
  providers: [AzureMapPopupService],
})
export class RealEstatePortfolioLayoutComponent implements OnInit {
  kpiData$ = new BehaviorSubject(null);
  portfolioGeneralSpreadsheetData$ = this.store.select(
    selectPortfolioGeneralSpreadsheetData,
  );
  portfolioKpiSpreadsheetData$ = this.store.select(
    selectPortfolioKpiSpreadsheetData,
  );
  customerKey$ = this.store.select(selectUserCustomerKey);

  kpis: KpiMetric[][] = [
    [
      {
        key: 'numberOfCases',
        title: $localize`:@@dashboard.realEstate.portfolio.kpi.cases:Fälle`,
        flexBasis: '10%',
        value: null,
      },
      {
        key: 'allRentalUnits',
        title: $localize`:@@dashboard.realEstate.portfolio.kpi.allRentalUnits:Alle Mieteinheiten`,
        value: null,
      },
      {
        key: 'totalRentalSpace',
        title: $localize`:@@dashboard.realEstate.portfolio.kpi.totalRentalSpace:Mietfläche Gesamt (m²)`,
        value: null,
      },
    ],
    [
      {
        key: 'totalInvestmentAmount',
        title: $localize`:@@dashboard.realEstate.portfolio.kpi.totalInvestmentAmount:Gesamtinvestitionskosten`,
        flexBasis: '10%',
        value: null,
      },
      {
        key: 'totalMarketValue',
        title: $localize`:@@dashboard.realEstate.portfolio.kpi.totalMarketValue:Marktwert Gesamt`,
        value: null,
      },
      {
        key: 'totalGrossFloorArea',
        title: $localize`:@@dashboard.realEstate.portfolio.kpi.totalGrossFloorArea:BGF Gesamt (m²)`,
        value: null,
      },
    ],
  ];

  activePopupId$: Observable<string> = this.azureMapPopupService.activePopupId$;
  getPointsOfInterest$ = this.store.select(selectPointsOfInterest);

  constructor(
    private destroyRef: DestroyRef,
    private currencyPipe: CurrencyPipe,
    private decimalPipe: DecimalPipe,
    private store: Store,
    private azureMapPopupService: AzureMapPopupService,
  ) {}

  ngOnInit(): void {
    combineLatest([
      this.store.select(selectPointsOfInterest),
      this.store.select(selectGeneralNumbersOverview),
    ])
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        filter((d) => !!d),
        tap(([propertyMapLocations, generalNumbersOverview]) => {
          if (generalNumbersOverview) {
            this.kpiData$.next(
              this.mapKpis(
                generalNumbersOverview as DashboardGeneralNumbersOverview,
              ),
            );
          }
        }),
      )
      .subscribe();
  }

  mapKpis(numbersOverview: DashboardGeneralNumbersOverview) {
    Object.entries(numbersOverview).forEach(([key, value]) => {
      if (!isNumber(value)) return;

      this.kpis.forEach((row) => {
        row.forEach((k) => {
          if (k.key === key) {
            const v = value.toString();

            switch (key) {
              case 'totalInvestmentAmount':
              case 'totalMarketValue':
                k.value = this.currencyPipe.transform(v);
                break;
              default:
                k.value = this.decimalPipe.transform(v);
                break;
            }
          }
        });
      });
    });

    return this.kpis;
  }
}
