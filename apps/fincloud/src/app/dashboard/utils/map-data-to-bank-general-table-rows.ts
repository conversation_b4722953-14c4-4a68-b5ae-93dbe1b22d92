import { ArrayNode } from '@fincloud/swagger-generator/dashboard';
import { CASE_TYPES } from '@fincloud/utils';
import { BankDashboardGeneralTableRow } from '../models/bank-dashboard-general-table-row';
import { ObjectAddress } from '../models/object-address';
import { buildAddress } from './build-address';
import { FINANCING_TYPES } from './financing-types';
import { mapCaseStatus } from './map-case-status';

export function mapDataToBankGeneralTableRows(
  data: ArrayNode,
): BankDashboardGeneralTableRow[] {
  return (data as BankDashboardGeneralTableRow[]).map((node) => {
    const objectAddress = buildAddress(
      (node.objectAddress as unknown as { address: ObjectAddress })?.address,
    );
    const creationDate = node.creationDate * 1000;
    const caseType = node.caseType && CASE_TYPES[node.caseType];
    const financingType =
      node.financingType && FINANCING_TYPES[node.financingType];
    const esgCertified =
      node.esgCertified &&
      node.esgCertified[0]?.toUpperCase() + node.esgCertified.slice(1);
    const caseStatus = mapCaseStatus(node.caseStatus, node.caseState);

    return {
      businessCaseId: node.businessCaseId,
      objectName: node.objectName,
      caseState: node.caseState,
      objectAddress,
      creationDate,
      caseType,
      financingType,
      caseStatus,
      company: node.company,
      ownInvestmentAmount: node.ownInvestmentAmount,
      objectType: node.objectType,
      assetClass: node.assetClass,
      energyEfficiencyClass: node.energyEfficiencyClass,
      esgCertified,
      ecoreScoring: node.ecoreScoring,
    };
  });
}
