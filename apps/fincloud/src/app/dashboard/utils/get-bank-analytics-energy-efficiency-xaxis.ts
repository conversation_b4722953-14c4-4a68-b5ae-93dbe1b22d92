import { XAXisComponentOption } from 'echarts';
import { getEnergyClasses } from './asset-classes-widget';

export const getBankAnalyticsEnergyEfficiencyXAxis = () => {
  return <XAXisComponentOption[]>[
    {
      type: 'category',
      data: getEnergyClasses(),
      axisLine: {
        lineStyle: {
          color: '#D4D4D9',
        },
      },
      axisLabel: {
        interval: 0,
        color: '#A8A9B2',
      },
    },
  ];
};
