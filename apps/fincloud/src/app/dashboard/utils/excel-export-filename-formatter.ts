import { DatePipe } from '@angular/common';
import { Locale } from '@fincloud/types/enums';
import { padStart } from 'lodash-es';

export function formatExcelName(locale: Locale, suffix: string) {
  const datePipe = new DatePipe(locale);

  const now = new Date();
  const hours = padStart(now.getHours().toString(), 2, '0');
  const minutes = padStart(now.getMinutes().toString(), 2, '0');
  const seconds = padStart(now.getSeconds().toString(), 2, '0');
  // we need this in order to download a file with hh:mm:ss format for the time, because when downloading the file the ':' gets replaced by '_' by the browser
  const unicodeColonSeparator = '\uA789';
  const time = `${hours}${unicodeColonSeparator}${minutes}${unicodeColonSeparator}${seconds}`;

  const date = datePipe.transform(now);

  return `${suffix} - ${date} ${time}.xlsx`;
}
