import { FinancingRepaidState } from '@fincloud/types/enums';
import { PERCEPTION_TRANSLATIONS_MAP } from '@fincloud/utils';

export function mapCaseStatus(nodeCaseStatus: string, nodeCaseState: string) {
  let caseStatus;

  if (nodeCaseStatus === FinancingRepaidState.FINANCING_REPAID) {
    if (nodeCaseState === 'active') {
      caseStatus = PERCEPTION_TRANSLATIONS_MAP.get(
        FinancingRepaidState.FINANCING_REPAID_ACTIVE,
      );
    } else {
      caseStatus = PERCEPTION_TRANSLATIONS_MAP.get(
        FinancingRepaidState.FINANCING_REPAID_INACTIVE,
      );
    }
  } else {
    caseStatus = PERCEPTION_TRANSLATIONS_MAP.get(nodeCaseStatus);
  }
  return caseStatus;
}
