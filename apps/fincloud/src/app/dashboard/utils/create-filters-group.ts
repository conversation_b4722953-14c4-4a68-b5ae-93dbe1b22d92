import { FormControl, FormGroup } from '@angular/forms';
import { FiltersForm } from '../models/dashboard-filters-form';
import { DASHBOARD_FILTERS_FORMINITIAL_DATA } from './dashboard-filters-form-initial-data';

export const createFiltersGroup = (): FormGroup<FiltersForm> => {
  return new FormGroup<FiltersForm>({
    financingType: new FormGroup({
      realEstate: new FormControl(
        DASHBOARD_FILTERS_FORMINITIAL_DATA.financingType.realEstate,
      ),
      corporate: new FormControl(
        DASHBOARD_FILTERS_FORMINITIAL_DATA.financingType.corporate,
      ),
      miscellaneous: new FormControl(
        DASHBOARD_FILTERS_FORMINITIAL_DATA.financingType.miscellaneous,
      ),
    }),
    caseType: new FormGroup({
      financing: new FormControl(
        DASHBOARD_FILTERS_FORMINITIAL_DATA.caseType.financing,
      ),
    }),
    caseStatus: new FormControl(
      DASHBOARD_FILTERS_FORMINITIAL_DATA.caseStatus.value,
    ),
    caseStatusTags: new FormControl(
      DASHBOARD_FILTERS_FORMINITIAL_DATA.caseStatusTags,
    ),
  });
};
