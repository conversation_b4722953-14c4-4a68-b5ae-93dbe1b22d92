import { SeriesOption } from 'echarts';
import { EnergyClassesOverview } from '../models/energy-classes-overview';
import { getEnergyClasses } from './asset-classes-widget';

export const getRealEstateAnalyticsEnergyEfficiencySeries = (
  data: EnergyClassesOverview,
) => {
  const energyClassNames = getEnergyClasses();

  const energyClassesTotal = Array(energyClassNames.length).fill(0);
  const energyClassesCertified = Array(energyClassNames.length).fill(0);
  const energyClassesNonCertified = Array(energyClassNames.length).fill(0);

  energyClassNames.forEach((energyClassName, index) => {
    const foundEnergyClass = data?.energyEfficiencyClasses?.find(
      (eClass) => eClass.energyClass === energyClassName,
    );

    if (foundEnergyClass) {
      energyClassesTotal[index] = foundEnergyClass?.totalNumberPerEnergyClass;
      energyClassesCertified[index] =
        foundEnergyClass?.esgCertificate?.certified | 0;
      energyClassesNonCertified[index] =
        foundEnergyClass?.esgCertificate?.nonCertified | 0;
    }
  });

  return <SeriesOption[]>[
    {
      name: $localize`:@@dashboard.bank.analytics.energy.title:Energieeffizienzklasse`,
      type: 'bar',
      barWidth: 10,
      itemStyle: {
        borderRadius: [2, 2, 0, 0],
      },
      emphasis: {
        focus: 'series',
      },
      data: energyClassesTotal,
    },
    {
      name: $localize`:@@dashboard.bank.analytics.certified.title:Zertifiziert`,
      type: 'bar',
      barWidth: 10,
      itemStyle: {
        borderRadius: [2, 2, 0, 0],
      },
      emphasis: {
        focus: 'series',
      },
      data: energyClassesCertified,
    },
    {
      name: $localize`:@@dashboard.bank.analytics.nonCertified.title:Nicht zertifiziert`,
      type: 'bar',
      barWidth: 10,
      itemStyle: {
        borderRadius: [2, 2, 0, 0],
      },
      emphasis: {
        focus: 'series',
      },
      data: energyClassesNonCertified,
    },
  ];
};
