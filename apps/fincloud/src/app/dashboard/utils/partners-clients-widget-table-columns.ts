import { CurrencyPipe, DecimalPipe } from '@angular/common';
import { FluidTableColumn, getComparator } from '@fincloud/components/lists';
import { ChainedPipes, RemoveTrailingZerosPipe } from '@fincloud/core/pipes';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { Locale } from '@fincloud/types/enums';
import { ClientTableRow } from '../models/client-table-row';

export const getClientWidgetTableColumnsConfig = (
  locale: Locale,
  regionalSettings: RegionalSettingsService,
): FluidTableColumn[] => [
  {
    name: $localize`:@@dashboard.bank.financingPartnerClientWidget.client.name:Name`,
    prop: 'name',
    width: 175,
    flexGrow: 2,
    sortable: true,
    comparator: getComparator<ClientTableRow>(
      (r) => r?.name?.toLocaleLowerCase() ?? '',
    ),
  },
  {
    name: $localize`:@@dashboard.bank.financingPartnerClientWidget.client.cases:Fälle`,
    prop: 'cases',
    width: 75,
    flexGrow: 0.7,
    sortable: true,
    pipe: new ChainedPipes([
      new DecimalPipe(locale),
      new RemoveTrailingZerosPipe(regionalSettings),
    ]),
  },
  {
    name: $localize`:@@dashboard.bank.financingPartnerClientWidget.client.totalLoanAmount:Kreditbetrag`,
    prop: 'totalLoanAmount',
    width: 100,
    flexGrow: 1.2,
    sortable: true,
    pipe: new ChainedPipes([
      new CurrencyPipe(locale),
      new RemoveTrailingZerosPipe(regionalSettings),
    ]),
  },
];
