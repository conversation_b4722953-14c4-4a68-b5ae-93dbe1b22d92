import { convertToDecimalPercentage } from '@fincloud/core/utils';
import { FinStructureExtractionsExtended } from '@fincloud/types/models';
import { CASE_TYPES } from '@fincloud/utils';
import { BankDashboardKpiTableRow } from '../models/bank-dashboard-kpi-table-row';
import { ObjectAddress } from '../models/object-address';
import { buildAddress } from './build-address';
import { FINANCING_TYPES } from './financing-types';
import { mapCaseStatus } from './map-case-status';

export function mapDataToBankKpiTableRows(
  data: FinStructureExtractionsExtended[],
): BankDashboardKpiTableRow[] {
  return data.map((prop) => {
    const projectName = (prop.fields as { projectName: string }).projectName;
    const creationDate =
      (prop.fields as { creationDate: number }).creationDate * 1000;
    const objectAddress = buildAddress(
      (prop.fields.propertyLocation as { address: ObjectAddress })?.address,
    );
    const caseType =
      prop.fields.caseType &&
      CASE_TYPES[(prop.fields as { caseType: string }).caseType];
    const financingType =
      prop.fields.financingType &&
      FINANCING_TYPES[(prop.fields as { financingType: string }).financingType];
    const ltcAtMarketValue = convertToDecimalPercentage(
      (prop.fields as { ltcAtMarketValue: number }).ltcAtMarketValue,
    );
    const ltcAtTotalInvestmentAmount = convertToDecimalPercentage(
      (prop.fields as { ltcAtTotalInvestmentAmount: number })
        .ltcAtTotalInvestmentAmount,
    );
    const utilizationOfDebtActual = convertToDecimalPercentage(
      (prop.fields as { utilizationOfDebtActual: number })
        .utilizationOfDebtActual,
    );
    const utilizationOfDebtTarget = convertToDecimalPercentage(
      (prop.fields as { utilizationOfDebtTarget: number })
        .utilizationOfDebtTarget,
    );
    const propertyDebtForAnnualIncomeActual = convertToDecimalPercentage(
      (prop.fields as { propertyDebtForAnnualIncomeActual: number })
        .propertyDebtForAnnualIncomeActual,
    );
    const propertyDebtForAnnualIncomeTarget = convertToDecimalPercentage(
      (prop.fields as { propertyDebtForAnnualIncomeTarget: number })
        .propertyDebtForAnnualIncomeTarget,
    );
    const projectProfitPercentage = convertToDecimalPercentage(
      (prop.fields as { projectProfitPercentage: number })
        .projectProfitPercentage,
    );
    const rentalSpaceFactor = convertToDecimalPercentage(
      (prop.fields as { rentalSpaceFactor: number }).rentalSpaceFactor,
    );
    const multiplierMarketValueActual =
      (prop.fields as { multiplierMarketValueActual: number })
        .multiplierMarketValueActual &&
      parseFloat(
        (
          prop.fields as { multiplierMarketValueActual: number }
        ).multiplierMarketValueActual.toFixed(2),
      );
    const multiplierMarketValueTarget =
      (prop.fields as { multiplierMarketValueTarget: number })
        .multiplierMarketValueTarget &&
      parseFloat(
        (
          prop.fields as { multiplierMarketValueTarget: number }
        ).multiplierMarketValueTarget.toFixed(2),
      );
    const multiplierTotalInvestmentAmountActual =
      (prop.fields as { multiplierTotalInvestmentAmountActual: number })
        .multiplierTotalInvestmentAmountActual &&
      parseFloat(
        (
          prop.fields as { multiplierTotalInvestmentAmountActual: number }
        ).multiplierTotalInvestmentAmountActual.toFixed(2),
      );
    const multiplierTotalInvestmentAmountTarget =
      (prop.fields as { multiplierTotalInvestmentAmountTarget: number })
        .multiplierTotalInvestmentAmountTarget &&
      parseFloat(
        (
          prop.fields as { multiplierTotalInvestmentAmountTarget: number }
        ).multiplierTotalInvestmentAmountTarget.toFixed(2),
      );
    const propertyUnsecuredPortion60Percent = (
      prop.fields as { propertyUnsecuredPortion60Percent: number }
    ).propertyUnsecuredPortion60Percent;
    const propertyUnsecuredPortion70Percent = (
      prop.fields as { propertyUnsecuredPortion70Percent: number }
    ).propertyUnsecuredPortion70Percent;
    const propertyUnsecuredPortion80Percent = (
      prop.fields as { propertyUnsecuredPortion80Percent: number }
    ).propertyUnsecuredPortion80Percent;

    const caseStatus =
      prop.fields.caseStatus &&
      mapCaseStatus(prop.fields.caseStatus as string, prop.caseState);

    return {
      businessCaseId: prop.businessCaseId,
      projectName,
      objectAddress,
      creationDate,
      caseType,
      financingType,
      caseStatus,
      caseState: prop.caseState,
      ltcAtMarketValue,
      ltcAtTotalInvestmentAmount,
      utilizationOfDebtActual,
      utilizationOfDebtTarget,
      propertyDebtForAnnualIncomeActual,
      propertyDebtForAnnualIncomeTarget,
      projectProfitPercentage,
      rentalSpaceFactor,
      multiplierMarketValueActual,
      multiplierMarketValueTarget,
      multiplierTotalInvestmentAmountActual,
      multiplierTotalInvestmentAmountTarget,
      propertyUnsecuredPortion60Percent,
      propertyUnsecuredPortion70Percent,
      propertyUnsecuredPortion80Percent,
    };
  });
}
