import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
} from '@angular/core';
import {
  FieldDtoWithIndex,
  TemplateFieldViewModel,
} from '@fincloud/core/business-case';
import {
  DOCUMENT_GROUP_PREFIX,
  FolderStructureViewMode,
} from '@fincloud/neoshare/folder-structure';
import {
  StateLibFolderStructureFolderPageActions,
  folderStructureFeature,
} from '@fincloud/state/folder-structure';
import {
  Folder,
  Information,
} from '@fincloud/swagger-generator/business-case-manager';
import { Information as CompanyInformation } from '@fincloud/swagger-generator/company';

import { FormBuilder } from '@angular/forms';
import { FOLDER_STRUCTURE_VIEW_MODE_OPTIONS } from '@fincloud/neoshare/folder-structure';
import { NextFolderDocument } from '@fincloud/swagger-generator/document';
import { FolderStructureContext } from '@fincloud/types/enums';
import {
  DataRoomHighlight,
  DataRoomTemplateFieldData,
  FolderWithBreadcrumbs,
  ValueChangeModel,
} from '@fincloud/types/models';
import { FinBreadcrumbItem } from '@fincloud/ui/breadcrumb';
import { FinButtonAppearance } from '@fincloud/ui/button';
import { FinEmptyStateType } from '@fincloud/ui/empty-state';
import { FinSize } from '@fincloud/ui/types';
import { FinWarningMessageAppearance } from '@fincloud/ui/warning-message';
import { Store } from '@ngrx/store';
import { isEqual } from 'lodash-es';
import { Observable, distinctUntilChanged, map } from 'rxjs';
import { SubfolderClickedEvent } from '../../models/subfolder-clicked-event';

@Component({
  selector: 'app-folder-structure',
  templateUrl: './folder-structure.component.html',
  styleUrl: './folder-structure.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FolderStructureComponent implements OnChanges {
  @Input() context: FolderStructureContext;
  @Input() groupKey: string;

  @Input() rootFolder: Folder;
  @Input() documentFields: TemplateFieldViewModel[];
  @Input() documentTemplateFieldData: DataRoomTemplateFieldData;
  @Input() isServiceSynchronizedWithDracoon: boolean;
  @Input() isServiceSynchronizedWithNextfolder: boolean;
  @Input() nextfolderDocuments: NextFolderDocument[];

  @Input() editMode: boolean;
  @Input() searchMode: boolean;
  @Input() highlight: DataRoomHighlight;
  @Input() forceDocumentHoverStop = false;

  @Output() searchResultFolderClicked = new EventEmitter<string>();
  @Output() showFolderInEnclosingFolder = new EventEmitter<string>();

  @Output() fileUpload = new EventEmitter<ValueChangeModel>();
  @Output() fieldDeleted = new EventEmitter<Information | CompanyInformation>();
  @Output() openModalRequested = new EventEmitter<{
    modalTab: 'edit' | 'revisions';
    field: FieldDtoWithIndex;
  }>();
  @Output() showDocumentInEnclosingFolder = new EventEmitter<string>();
  @Output() documentFieldValueChanged = new EventEmitter<ValueChangeModel>();

  currentFolder$: Observable<FolderWithBreadcrumbs> = this.store
    .select(folderStructureFeature.selectCurrentFolderPerGroup)
    .pipe(
      map((currentFolderPerGroup) => currentFolderPerGroup[this.groupKey]),
      distinctUntilChanged(isEqual),
    );

  viewModeForm = this.fb.group({
    viewMode: [FolderStructureViewMode.GRID],
  });

  readonly finSize = FinSize;
  readonly finButtonAppearance = FinButtonAppearance;
  readonly finWarningMessageAppearance = FinWarningMessageAppearance;
  readonly finEmptyStateType = FinEmptyStateType;

  readonly documentGroupPrefix = DOCUMENT_GROUP_PREFIX;
  readonly viewModeOptions = FOLDER_STRUCTURE_VIEW_MODE_OPTIONS;

  readonly warningMessageLabel = $localize`:@@businessCase.dataRoom.text.nextfolderInfo:Unterstützte Dateiformate: PNG, JPG, PDF bis 100MB. Für einen idealen Workflow verarbeiten Sie PDF-Daten im DIN A4 Format.`;
  readonly emptyStateTitle = $localize`:@@folderStructure.dragDropPlaceholder:Fügen Sie Dokumente und Ordner per Drag & Drop aus der linken Spalte hinzu oder laden Sie eine Datei hoch`;

  constructor(
    private store: Store,
    private fb: FormBuilder,
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    if (
      changes.rootFolder &&
      !isEqual(
        changes.rootFolder.previousValue,
        changes.rootFolder.currentValue,
      )
    ) {
      this.store.dispatch(
        StateLibFolderStructureFolderPageActions.folderStructureChanged({
          groupKey: this.groupKey,
          rootFolder: this.rootFolder,
          searchMode: this.searchMode,
        }),
      );
    }
  }

  navigateToSubfolder({ folder, index }: SubfolderClickedEvent): void {
    if (this.searchMode) {
      this.searchResultFolderClicked.emit(
        this.documentGroupPrefix + this.groupKey,
      );
    }
    this.store.dispatch(
      StateLibFolderStructureFolderPageActions.setCurrentFolderForGroup({
        groupKey: this.groupKey,
        folder,
        folderIndex: index,
      }),
    );
  }

  onBreadcrumbClick(selectedBreadcrumb: FinBreadcrumbItem): void {
    this.store.dispatch(
      StateLibFolderStructureFolderPageActions.navigateToFolderOnBreadcrumbClick(
        {
          groupKey: this.groupKey,
          selectedBreadcrumb,
        },
      ),
    );
  }

  emitShowInEnclosingFolder(folderId: string) {
    this.showFolderInEnclosingFolder.emit(folderId);
  }

  emitFileUpload(event: ValueChangeModel): void {
    this.fileUpload.emit(event);
  }

  emitFieldDeleted(information: Information | CompanyInformation): void {
    this.fieldDeleted.emit(information);
  }

  emitOpenModalRequested(
    modalTab: 'edit' | 'revisions',
    field: FieldDtoWithIndex,
  ): void {
    this.openModalRequested.emit({
      modalTab,
      field,
    });
  }

  emitShowDocumentInEnclosingFolder(fieldKey: string): void {
    this.showDocumentInEnclosingFolder.emit(fieldKey);
  }

  emitDocumentFieldValueChanged(valueChange: ValueChangeModel): void {
    this.documentFieldValueChanged.emit(valueChange);
  }
}
