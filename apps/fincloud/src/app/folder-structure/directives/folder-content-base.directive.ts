import { Directive, EventEmitter, Inject, Input, Output } from '@angular/core';
import {
  FieldDtoWithIndex,
  TemplateFieldViewModel,
} from '@fincloud/core/business-case';
import {
  FOLDER_CONTENT_BASE_SERVICE_TOKEN,
  FolderContentBaseServiceDefinition,
} from '@fincloud/neoshare/folder-structure';
import {
  FieldDto,
  Folder,
  Information,
} from '@fincloud/swagger-generator/business-case-manager';
import { Information as CompanyInformation } from '@fincloud/swagger-generator/company';
import { NextFolderDocument } from '@fincloud/swagger-generator/document';
import {
  DataRoomHighlight,
  DataRoomTemplateFieldData,
  ValueChangeModel,
} from '@fincloud/types/models';
import { FinSize } from '@fincloud/ui/types';
import { SubfolderClickedEvent } from '../models/subfolder-clicked-event';

@Directive()
export class FolderContentBase {
  @Input() groupKey: string;
  @Input() folder: Folder;
  @Input() highlight: DataRoomHighlight;

  @Input() documentFields: TemplateFieldViewModel[];
  @Input() documentTemplateFieldData: DataRoomTemplateFieldData;
  @Input() isServiceSynchronizedWithDracoon: boolean;
  @Input() isServiceSynchronizedWithNextfolder: boolean;
  @Input() nextfolderDocuments: NextFolderDocument[];

  @Input() editMode: boolean;
  @Input() searchMode: boolean;

  @Output() subfolderClicked = new EventEmitter<SubfolderClickedEvent>();
  @Output() showFolderInEnclosingFolder = new EventEmitter<string>();

  @Output() fileUpload = new EventEmitter<ValueChangeModel>();
  @Output() fieldDeleted = new EventEmitter<Information | CompanyInformation>();
  @Output() openModalRequested = new EventEmitter<{
    modalTab: 'edit' | 'revisions';
    field: FieldDtoWithIndex;
  }>();
  @Output() showDocumentInEnclosingFolder = new EventEmitter<string>();
  @Output() documentFieldValueChanged = new EventEmitter<ValueChangeModel>();

  readonly finSize = FinSize;

  constructor(
    @Inject(FOLDER_CONTENT_BASE_SERVICE_TOKEN)
    private folderContentBaseService: FolderContentBaseServiceDefinition,
  ) {}

  // Folder related methods

  showFolderDetails(folder: Folder): void {
    this.folderContentBaseService.showFolderDetails(folder);
  }

  renameFolder(folder: Folder): void {
    this.folderContentBaseService.renameFolder(folder, this.groupKey);
  }

  moveFolder(folderId: string): void {
    this.folderContentBaseService.moveFolder(folderId, this.groupKey);
  }

  deleteFolder(folderId: string): void {
    this.folderContentBaseService.deleteFolder(folderId, this.groupKey);
  }

  // Document related methods

  moveDocument(documentField: FieldDto): void {
    this.folderContentBaseService.moveDocument(documentField, this.groupKey);
  }

  // Folder related event emitters

  emitSubfolderClicked(folder: Folder, index: number): void {
    this.subfolderClicked.emit({ folder, index });
  }

  emitShowFolderInEnclosingFolder(folderId: string): void {
    this.showFolderInEnclosingFolder.emit(folderId);
  }

  // Document related event emitters

  emitFileUpload(event: ValueChangeModel): void {
    this.fileUpload.emit(event);
  }

  emitFieldDeleted(information: Information | CompanyInformation): void {
    this.fieldDeleted.emit(information);
  }

  emitOpenModalRequested(
    modalTab: 'edit' | 'revisions',
    field: FieldDtoWithIndex,
  ): void {
    this.openModalRequested.emit({
      modalTab,
      field,
    });
  }

  emitShowDocumentInEnclosingFolder(fieldKey: string): void {
    this.showDocumentInEnclosingFolder.emit(fieldKey);
  }

  emitDocumentFieldValueChanged(valueChange: ValueChangeModel): void {
    this.documentFieldValueChanged.emit(valueChange);
  }

  handleShowFolderInEnclosingFolder(event: MouseEvent, folderId: string): void {
    event.stopPropagation();
    this.emitShowFolderInEnclosingFolder(folderId);
  }
}
