import { Injectable } from '@angular/core';
import {
  FolderContentBaseServiceDefinition,
  FolderDeleteModalComponent,
  FolderDetailsComponent,
  MoveFileModalComponent,
} from '@fincloud/neoshare/folder-structure';
import {
  StateLibFolderStructureDocumentPageActions,
  StateLibFolderStructureFolderPageActions,
} from '@fincloud/state/folder-structure';
import { StateLibUsersPageActions } from '@fincloud/state/users';
import {
  FieldDto,
  Folder,
  Group,
} from '@fincloud/swagger-generator/business-case-manager';
import { FinModalService } from '@fincloud/ui/modal';
import { FinSize } from '@fincloud/ui/types';
import { DEFAULT_USER_NAME } from '@fincloud/utils';
import { Store } from '@ngrx/store';
import { filter, takeUntil } from 'rxjs';

@Injectable()
export class BusinessCaseDataRoomFolderContentBaseService
  implements FolderContentBaseServiceDefinition
{
  constructor(
    private finModalService: FinModalService,
    private store: Store,
  ) {}

  // Folder related methods

  showFolderDetails(folder: Folder): void {
    this.store.dispatch(
      StateLibUsersPageActions.loadUsers({
        payload: {
          userIds: [folder.createdById, folder.updatedById].filter(
            (userId) => userId !== DEFAULT_USER_NAME,
          ),
        },
      }),
    );

    this.finModalService.open(FolderDetailsComponent, {
      data: folder,
      size: FinSize.M,
    });
  }

  renameFolder(folder: Folder, groupKey: string): void {
    this.store.dispatch(
      StateLibFolderStructureFolderPageActions.openRenameFolderModal({
        groupKey,
        name: folder.name,
        id: folder.id,
      }),
    );
  }

  moveFolder(folderId: string, groupKey: string): void {
    const moveFolderModalRef = this.finModalService.open(
      MoveFileModalComponent,
      {
        data: groupKey,
        size: FinSize.XL,
      },
    );

    moveFolderModalRef.componentInstance.confirmMove
      .pipe(takeUntil(moveFolderModalRef.afterClosed()))
      .subscribe(
        ({
          sourceFolderGroup,
          targetFolderId,
          targetFolderGroup,
        }: {
          sourceFolderGroup: Group;
          targetFolderId: string;
          targetFolderGroup: Group;
        }) => {
          this.store.dispatch(
            StateLibFolderStructureFolderPageActions.moveFolder({
              folderId,
              targetFolderId,
              targetFolderGroup,
              sourceFolderGroup: sourceFolderGroup,
            }),
          );
        },
      );
  }

  deleteFolder(folderId: string, groupKey: string): void {
    this.finModalService
      .open(FolderDeleteModalComponent)
      .afterClosed()
      .pipe(filter(Boolean))
      .subscribe(() => {
        this.store.dispatch(
          StateLibFolderStructureFolderPageActions.deleteFolder({
            folderId,
            folderGroupKey: groupKey,
          }),
        );
      });
  }

  // Document related methods

  moveDocument(documentField: FieldDto, groupKey: string): void {
    const moveDocumentModalRef = this.finModalService.open(
      MoveFileModalComponent,
      {
        data: groupKey,
        size: FinSize.XL,
      },
    );

    moveDocumentModalRef.componentInstance.confirmMove
      .pipe(takeUntil(moveDocumentModalRef.afterClosed()))
      .subscribe(
        ({
          sourceFolderGroup,
          targetFolderId,
          targetFolderGroup,
        }: {
          sourceFolderGroup: Group;
          targetFolderId: string;
          targetFolderGroup: Group;
        }) => {
          this.store.dispatch(
            StateLibFolderStructureDocumentPageActions.moveDocument({
              documentField: documentField,
              targetFolderId,
              targetFolderGroup,
              sourceFolderGroup,
            }),
          );
        },
      );
  }
}
