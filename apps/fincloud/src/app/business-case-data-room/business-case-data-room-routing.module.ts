import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { financingStructureActivateGuard } from '@fincloud/neoshare/business-case';
import {
  neogptChatActivateGuard,
  neogptChatDeactivateGuard,
} from '@fincloud/neoshare/neogpt-chat';
import { NeoGptActiveSession } from '@fincloud/types/enums';
import { BusinessCaseDataRoomTabsComponent } from './components/business-case-data-room-tabs/business-case-data-room-tabs.component';
import { BusinessCaseDataRoomComponent } from './components/business-case-data-room/business-case-data-room.component';
import { accessLinkedCompanyGuard } from './guards/access-linked-comapny.guard';
import { loadCompanyDataRoomFolderStructuresGuard } from './guards/load-company-data-room-folder-structures.guard';
import { loadDataRoomFolderStructuresGuard } from './guards/load-data-room-folder-structures.guard';

const routes: Routes = [
  {
    path: '',
    component: BusinessCaseDataRoomTabsComponent,
    children: [
      {
        path: '',
        redirectTo: 'case',
        pathMatch: 'full',
      },
      {
        path: 'case',
        component: BusinessCaseDataRoomComponent,
        canActivate: [
          neogptChatActivateGuard,
          loadDataRoomFolderStructuresGuard,
          financingStructureActivateGuard,
        ],
        canDeactivate: [neogptChatDeactivateGuard],
        data: { activeSession: NeoGptActiveSession.DATA_ROOM },
      },
      {
        path: 'company',
        component: BusinessCaseDataRoomTabsComponent,
        canActivate: [
          accessLinkedCompanyGuard,
          loadCompanyDataRoomFolderStructuresGuard,
        ],
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BusinessCaseDataRoomRoutingModule {}
