import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  Inject,
  Input,
  LOCALE_ID,
  OnInit,
  ViewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  DataGridConfig,
  DataGridViewMode,
} from '@fincloud/components/data-grid';
import { TableColumn, TableComponent } from '@fincloud/components/lists';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { Toast } from '@fincloud/core/toast';
import { RevisionTableRow } from '@fincloud/neoshare/business-case-fields';
import {
  StateLibBusinessCasePageActions,
  StateLibFacilityPageActions,
  selectUsersById,
} from '@fincloud/state/business-case';
import { StateLibUserPageActions } from '@fincloud/state/user';
import { User } from '@fincloud/swagger-generator/authorization-server';
import {
  FacilitiesControllerService,
  Facility,
  FacilityField,
  FacilityRevision,
} from '@fincloud/swagger-generator/business-case-manager';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import { Locale } from '@fincloud/types/enums';
import { BusinessCaseDashboardState, Dictionary } from '@fincloud/types/models';
import { FinToastService } from '@fincloud/ui/toast';
import { Store } from '@ngrx/store';
import { cloneDeep, isString, uniq } from 'lodash-es';
import { Observable, combineLatest, of } from 'rxjs';
import { finalize, map, tap } from 'rxjs/operators';
import { facilityRevisionsTableColumnsConfig } from '../../utils/facility-field-revisions-table-columns';

@Component({
  selector: 'app-facility-field-revisions',
  templateUrl: './facility-field-revisions.component.html',
  styleUrls: ['./facility-field-revisions.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FacilityFieldRevisionsComponent implements OnInit {
  @Input() businessCase: ExchangeBusinessCase;

  @Input() facility: Facility;
  @Input() field: FacilityField;

  @ViewChild('table', { static: true }) table: TableComponent;

  get selectedRevisionValue() {
    // TODO: ideally support all field value types
    const value = this.field.value;
    if (isString(value)) {
      return value;
    }
    return '';
  }

  columns: TableColumn[] = facilityRevisionsTableColumnsConfig(
    this.locale,
    this.regionalSettings.dateFormat,
  );
  selectedRow: RevisionTableRow;
  selectedRevision: RevisionTableRow;
  userNames: Dictionary<User> = {};
  rows$: Observable<RevisionTableRow[]>;
  loading = false;
  facilityField: FacilityField;

  private allRevisions: RevisionTableRow[];

  dataGridConfig: DataGridConfig | null;
  DataGridViewMode = DataGridViewMode;

  constructor(
    private destroyRef: DestroyRef,
    private facilitiesControllerService: FacilitiesControllerService,
    private finToastService: FinToastService,
    private store: Store<BusinessCaseDashboardState>,
    private changeDetectorRef: ChangeDetectorRef,
    @Inject(LOCALE_ID) private locale: Locale,
    private regionalSettings: RegionalSettingsService,
  ) {}

  ngOnInit(): void {
    this.getAllRevisions();
    this.facilityField = cloneDeep(this.field);
  }

  getAllRevisions() {
    this.rows$ = combineLatest([
      this.facilitiesControllerService
        .getAllRevisionForCase({
          businessCaseId: this.businessCase.id,
          facilityFieldKey: this.field.key,
          facilityName: this.facility.name,
        })
        .pipe(
          tap((revisions) =>
            this.store.dispatch(
              StateLibUserPageActions.loadUsers({
                payload: uniq(revisions.map((r) => r.userId)),
              }),
            ),
          ),
          finalize(() => (this.loading = true)),
        ),
      this.store.select(selectUsersById),
    ]).pipe(
      map(([revisions, usersById]) => {
        this.userNames = usersById;
        return revisions.map((revision) =>
          this.mapRevisionToRevisionTableRow(
            revision,
            this.isCurrentVersion(revision),
          ),
        );
      }),
      tap((revisions) => {
        this.allRevisions = revisions;

        const currentRevision = revisions.find(
          (revision) => revision.isCurrentVersion,
        );
        if (currentRevision) {
          this.selectRevision(currentRevision);
        }
      }),
      takeUntilDestroyed(this.destroyRef),
    );
  }

  getUserName(item: { userId?: string }) {
    return [
      this.userNames[item.userId]?.firstName,
      this.userNames[item.userId]?.lastName,
    ]
      .filter(Boolean)
      .join(' ');
  }

  mapRevisionToRevisionTableRow(
    revision: FacilityRevision,
    isCurrentVersion: boolean,
  ): RevisionTableRow {
    return {
      date: revision.lastModifiedDate,
      revisionId: revision.id,
      information: revision.facilityField.value,
      isCurrentVersion,
      changedFrom: this.getUserName(revision),
      changedFromId: revision.userId,
    };
  }

  reactivateRevision() {
    this.facilitiesControllerService
      .restoreRevision1({
        businessCaseId: this.businessCase.id,
        revisionId: this.selectedRow.revisionId,
      })
      .pipe(
        tap(() => {
          this.allRevisions.forEach(
            (revision) => (revision.isCurrentVersion = false),
          );
          this.rows$ = of(this.allRevisions);
          this.selectedRow.isCurrentVersion = true;
          this.finToastService.show(Toast.success());
          this.changeDetectorRef.markForCheck();
          this.store.dispatch(
            StateLibFacilityPageActions.setIsRevisionApplied({
              payload: true,
            }),
          );
          this.store.dispatch(
            StateLibBusinessCasePageActions.loadBusinessCase({
              payload: this.businessCase.id,
            }),
          );
        }),
      )
      .subscribe();
  }

  showRevision(row: RevisionTableRow) {
    this.table.selectRow(row);
    this.selectRevision(row);
  }

  private selectRevision(row: RevisionTableRow) {
    this.selectedRow = row;
    this.selectedRevision = this.selectedRow;
    this.facilityField = { ...this.facilityField, value: row.information };

    if (this.isDataTable()) {
      this.dataGridConfig = DataGridConfig.buildFromInformationValue(
        this.selectedRevision.information
          ? JSON.parse(this.selectedRevision.information as string)
          : (this.selectedRevision.information as DataGridConfig),
      );
    } else {
      this.dataGridConfig = null;
    }
  }

  isDataTable() {
    return this.field?.fieldType === 'TABLE';
  }

  private isCurrentVersion(revision: FacilityRevision) {
    return this.field.restoredFromRevisionId === revision.id;
  }
}
