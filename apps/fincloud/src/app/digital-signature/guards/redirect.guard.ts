import { inject } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  GuardR<PERSON>ult,
  MaybeAsync,
  Router,
} from '@angular/router';
import { extractCustomerKeyFromUrl } from '@fincloud/state/router';
import { RedirectEvent } from '@fincloud/types/models';
import { signRedirectMapping } from '@fincloud/utils';

export function redirectGuard(
  childRoute: ActivatedRouteSnapshot,
): MaybeAsync<GuardResult> {
  const router = inject(Router);

  const signingProcessId = childRoute.params.signingProcessId as string;
  const event = childRoute.queryParams.event as RedirectEvent;
  const contractId = childRoute.queryParams.contractId as string;
  const redirectUrl =
    extractCustomerKeyFromUrl() + signRedirectMapping(signingProcessId)[event];

  return router.navigate([redirectUrl ?? ''], {
    queryParams: { contractId },
  });
}
