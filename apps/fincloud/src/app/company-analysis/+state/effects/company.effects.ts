import { Injectable } from '@angular/core';
import {
  CadrTemplateControllerService,
  Company,
  CompanyBranchControllerService,
  CompanyControllerService,
  CompanyProfileControllerService,
  InformationControllerService,
  NorthDataControllerService,
} from '@fincloud/swagger-generator/company';
import { ExchangeService } from '@fincloud/swagger-generator/exchange';
import {
  CommercialRegisterService,
  DocumentDownloadService,
} from '@fincloud/swagger-generator/handelsregister';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Subscription, forkJoin, of } from 'rxjs';
import {
  catchError,
  distinctUntilChanged,
  filter,
  map,
  switchMap,
  tap,
} from 'rxjs/operators';

import { Store } from '@ngrx/store';
import {
  cloneDeep,
  difference,
  flatten,
  isEmpty,
  isEqual,
  keyBy,
  uniq,
} from 'lodash-es';

import { retryCall } from '@fincloud/core/rxjs';
import { SocketService, SocketType } from '@fincloud/core/socket';
import { StateLibCompanyPageActions } from '@fincloud/state/company-analysis';
import { selectUserCustomerKey, selectUserId } from '@fincloud/state/user';
import { StateLibNoopPageActions } from '@fincloud/state/utils';
import { CustomerManagementControllerService } from '@fincloud/swagger-generator/authorization-server';
import {
  BusinessCaseControllerService,
  CaseContextInformationControllerService,
} from '@fincloud/swagger-generator/business-case-manager';
import { BusinessCasePermission } from '@fincloud/types/enums';
import {
  AppState,
  BusinessCaseCompanyAnalysis,
  RegisterCourt,
  RegisterType,
} from '@fincloud/types/models';
import { COMPANY_SOCKET_SUBSCRIPTION_DESTINATION } from '@fincloud/utils';
import { concatLatestFrom } from '@ngrx/operators';
import { CompanyApiActions, CompanyPageActions } from '../actions';
import { selectShareObjectsWithNames } from '../selectors';

@Injectable()
export class CompanyEffects {
  cadrMessageSubscription: Subscription;

  loadCompany$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibCompanyPageActions.loadCompany),
      switchMap((action) => {
        return this.companyService.getCompanyById({ id: action.payload }).pipe(
          map((res) => {
            return CompanyApiActions.loadCompanySuccess({ payload: res });
          }),
          catchError(() => of(CompanyApiActions.loadCompanyFailure())),
        );
      }),
    ),
  );

  reloadCompany$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibCompanyPageActions.reloadCompany),
      switchMap((action) => {
        return this.companyService.getCompanyById({ id: action.payload }).pipe(
          map((res) => {
            return CompanyApiActions.reloadCompanySuccess({ payload: res });
          }),
          catchError(() => of(CompanyApiActions.loadCompanyFailure())),
        );
      }),
    ),
  );

  loadCompanyBusinessCases$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        StateLibCompanyPageActions.loadCompany,
        CompanyPageActions.reloadCompanyBusinessCases,
      ),
      switchMap((action) =>
        this.businessCaseControllerService.getOtherBusinessCasesForCompany({
          companyId: action.payload,
        }),
      ),
      switchMap((caseIds) => {
        if (caseIds.length === 0) {
          return of(CompanyPageActions.loadCompanyBusinessCasesEmpty());
        }

        return forkJoin(
          caseIds.map((businessCaseId) => {
            return this.businessCaseContextService.getContextInformation({
              businessCaseId,
            });
          }),
        ).pipe(
          switchMap((businessCaseContexts) => {
            const accessToCasesContexts = businessCaseContexts.filter(
              (c) => !!c,
            );

            if (accessToCasesContexts.length === 0) {
              return of(CompanyPageActions.loadCompanyBusinessCasesEmpty());
            }

            const contextDictionary = keyBy(
              accessToCasesContexts,
              'businessCaseId',
            );

            return forkJoin(
              accessToCasesContexts.map((accessBusinessCaseContext) =>
                this.exchangeControllerService
                  .exchangeControllerFindById({
                    id: accessBusinessCaseContext.businessCaseId,
                    requiresPlatformManagerOrCurrentUserCheck: true,
                  })
                  .pipe(
                    catchError(() => {
                      return of(null);
                    }),
                  ),
              ),
            ).pipe(
              map((businessCases) => businessCases.filter(Boolean)),
              concatLatestFrom(() => this.store.select(selectUserId)),
              map(([businessCases, loggedInUserId]) => {
                return CompanyApiActions.loadCompanyBusinessCasesSuccess({
                  payload: businessCases.map((bCase) => {
                    return {
                      canManageCadrLink:
                        contextDictionary[bCase.id]?.permissions?.includes(
                          BusinessCasePermission.BCP_00059,
                        ) &&
                        flatten(
                          bCase.participants.map((p) =>
                            p.users.map((u) => u.userId),
                          ),
                        ).some((userId) => userId === loggedInUserId),
                      canSeeLinkedCase: contextDictionary[
                        bCase.id
                      ]?.permissions?.includes(
                        BusinessCasePermission.BCP_00058,
                      ),
                      ...bCase,
                    } as BusinessCaseCompanyAnalysis;
                  }),
                });
              }),
            );
          }),
        );
      }),
    ),
  );

  loadCompanyProfile$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibCompanyPageActions.loadCompany),
      switchMap((action) => {
        return this.companyProfileControllerService
          .getCompanyProfile({ companyId: action.payload })
          .pipe(
            map((res) => {
              return CompanyApiActions.loadCompanyProfileSuccess({
                payload: res,
              });
            }),
            catchError(() => of(CompanyApiActions.loadCompanyProfileFailure())),
          );
      }),
    ),
  );

  loadCompanyBranchesOnCompanyLoaded$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CompanyApiActions.loadCompanySuccess),
      map((action) =>
        CompanyPageActions.loadCompanyBranches({ payload: action.payload.id }),
      ),
    ),
  );

  loadCompanyCommercialRegisterDocuments$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CompanyApiActions.loadCompanySuccess),
      switchMap((action) => {
        const params = this.getCompanyRegisterInfo(action.payload);
        return this.documentDownloadService
          .documentControllerRequestCompanyDocumentFolders({
            body: params,
          })
          .pipe(
            map((res) => {
              if (isEmpty(res)) {
                // trigger retry when folders structure is not yet loaded
                throw Error('Folders structure not yet loaded in DB');
              }

              return res;
            }),
            retryCall({ maxRetries: 3, initialInterval: 10000 }),
            map((res) => {
              return CompanyApiActions.loadCompanyDocumentsSuccess({
                payload: res,
              });
            }),
            catchError(() =>
              of(CompanyApiActions.loadCompanyDocumentsFailure()),
            ),
          );
      }),
    ),
  );

  loadCompanyBranches$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CompanyPageActions.loadCompanyBranches),
      switchMap((action) => {
        return this.companyBranchControllerService
          .listAllBranchesForCompany({ companyId: action.payload })
          .pipe(
            map((res) => {
              return CompanyApiActions.loadCompanyBranchesSuccess({
                payload: res,
              });
            }),
            catchError(() =>
              of(CompanyApiActions.loadCompanyBranchesFailure()),
            ),
          );
      }),
    ),
  );

  loadSameAddressCompanies$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CompanyApiActions.loadCompanyBranchesSuccess),
      switchMap((action) => {
        return forkJoin(
          action.payload.map((b) => {
            return this.northDataControllerService
              .getCompaniesOnSameAddressForBranch({ branchId: b.id })
              .pipe(
                map((res) => ({ branchId: b.id, companiesOnSameAddress: res })),
                catchError(() => of(null)),
              );
          }),
        );
      }),
      map((res) => res.filter(Boolean)),
      map((res) =>
        CompanyApiActions.loadCompaniesOnSameAddressSuccess({ payload: res }),
      ),
    ),
  );

  loadSameAddressCompaniesOnNewCompany$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        CompanyPageActions.addCompanyBranch,
        CompanyPageActions.updateCompanyBranch,
      ),
      switchMap((action) => {
        return this.northDataControllerService
          .getCompaniesOnSameAddressForBranch({ branchId: action.payload.id })
          .pipe(
            map((res) => ({
              branchId: action.payload.id,
              companiesOnSameAddress: res,
            })),
            catchError(() => of(null)),
          );
      }),
      filter((res) => !!res),
      map((res) =>
        CompanyApiActions.loadCompaniesOnSameAddressSuccess({ payload: [res] }),
      ),
    ),
  );

  checkDocumentTypesAvailability$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CompanyApiActions.loadCompanySuccess),
      switchMap((action) => {
        const params = this.getCompanyRegisterInfo(action.payload);

        return this.commercialRegisterService
          .commercialRegisterControllerGetLinkAvailability({
            body: params,
          })
          .pipe(
            map((res) => {
              const isDocumentAvailabilityNotLoadedInDB = Object.values(
                res,
              )?.every((value) => !value);

              if (isDocumentAvailabilityNotLoadedInDB) {
                // trigger retry when document availability is not yet loaded
                throw Error('Document availability not loaded in DB');
              }

              return res;
            }),
            retryCall({ maxRetries: 3, initialInterval: 10000 }),
            map((res) =>
              CompanyPageActions.loadDocumentTypesAvailability({
                payload: res,
              }),
            ),
            catchError(() =>
              of(CompanyApiActions.loadDocumentTypesAvailabilityFailure()),
            ),
          );
      }),
    ),
  );

  hasCrawlError$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CompanyApiActions.loadCompanySuccess),
      switchMap((action) => {
        const params = this.getCompanyRegisterInfo(action.payload);
        return this.commercialRegisterService
          .commercialRegisterControllerGetHasCrawlError({ body: params })
          .pipe(
            map(({ hasCrawlError }) => {
              return CompanyApiActions.loadCompanyCrawlErrorSuccess({
                hasCrawlError,
              });
            }),
            catchError(() =>
              of(CompanyApiActions.loadCompanyCrawlErrorFailure()),
            ),
          );
      }),
    ),
  );

  loadInformation$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        CompanyApiActions.loadCompanySuccess,
        CompanyApiActions.reloadCompanySuccess,
      ),
      concatLatestFrom(() => this.store.select(selectUserCustomerKey)),
      switchMap(([action, customerKey]) => {
        if (action.payload.customerKey !== customerKey) {
          return of(StateLibNoopPageActions.noop());
        }

        return this.informationControllerService
          .getAllInformation({
            companyId: action.payload.id,
            includeDeleted: false,
          })
          .pipe(
            map((information) =>
              CompanyPageActions.setInformation({ payload: information }),
            ),
            catchError(() => of(StateLibNoopPageActions.noop())),
          );
      }),
    ),
  );

  loadCadrShareObjectWithNames$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        CompanyApiActions.loadCompanySuccess,
        CompanyApiActions.reloadCompanySuccess,
        CompanyPageActions.reloadCadrShareObjects,
      ),
      filter(Boolean),
      distinctUntilChanged(
        (prev, curr) =>
          isEqual(
            prev.payload.companyTemplate?.shareObjects,
            curr.payload.companyTemplate?.shareObjects,
          ) && prev.type !== curr.type,
      ),
      concatLatestFrom(() => this.store.select(selectShareObjectsWithNames)),
      switchMap(([action, storeShareObjects]) => {
        const company = action.payload;

        const customerKeys = uniq(
          company.companyTemplate?.shareObjects?.map((so) => so.customerKey),
        );

        const storeShareObjectsKeys = uniq(
          storeShareObjects?.map((sso) => sso.customerKey),
        );

        const newCustomerKeys = difference(customerKeys, storeShareObjectsKeys);

        if (!customerKeys?.length) {
          return of(StateLibNoopPageActions.noop());
        }

        return this.customerManagementControllerService
          .getMultipleCustomers({ customerKeys: newCustomerKeys })
          .pipe(
            map((customersNames) => {
              const customersByKey = keyBy(customersNames, 'key');
              const payload = company.companyTemplate.shareObjects.map((so) => {
                return {
                  ...so,
                  customerName:
                    customersByKey[so.customerKey]?.name ||
                    storeShareObjects.find(
                      (sso) => sso.customerKey === so.customerKey,
                    )?.customerName,
                };
              });
              return CompanyPageActions.setSharedObjectsWithNames({ payload });
            }),
          );
      }),
    ),
  );

  loadCADRtemplate$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        CompanyApiActions.loadCompanySuccess,
        CompanyApiActions.reloadCompanySuccess,
      ),
      switchMap((action) => {
        return this.cadrTemplateControllerService.getTemplate().pipe(
          tap((template) => {
            const company = cloneDeep(action.payload);
            if (company && !company.companyTemplate?.template && template) {
              company.companyTemplate = {
                ...company.companyTemplate,
                template,
              };
              this.store.dispatch(
                StateLibCompanyPageActions.setCompany({ payload: company }),
              );
            }
          }),
          map((template) => {
            return CompanyPageActions.setCadRtemplate({ payload: template });
          }),
          catchError(() => of(StateLibNoopPageActions.noop())),
        );
      }),
    ),
  );

  hookupCompanySocket$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(CompanyApiActions.loadCompanySuccess),
        tap((action) => {
          this.socketService.initializeSocket(SocketType.CADR);
          this.socketService.joinRoomAndReceiveMessagesByDestination(
            `${action.payload.id}`,
            COMPANY_SOCKET_SUBSCRIPTION_DESTINATION,
            SocketType.CADR,
          );
        }),
      ),
    { dispatch: false },
  );

  destroyCadrMessagesSub$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(CompanyPageActions.clearCompanyState),
        tap(() => {
          this.socketService.deactivateSocket(SocketType.CADR);
        }),
      ),
    { dispatch: false },
  );

  constructor(
    private actions$: Actions,
    private exchangeControllerService: ExchangeService,
    private businessCaseControllerService: BusinessCaseControllerService,
    private companyService: CompanyControllerService,
    private companyProfileControllerService: CompanyProfileControllerService,
    private commercialRegisterService: CommercialRegisterService,
    private documentDownloadService: DocumentDownloadService,
    private companyBranchControllerService: CompanyBranchControllerService,
    private northDataControllerService: NorthDataControllerService,
    private informationControllerService: InformationControllerService,
    private cadrTemplateControllerService: CadrTemplateControllerService,
    private store: Store<AppState>,
    private socketService: SocketService,
    private customerManagementControllerService: CustomerManagementControllerService,
    private businessCaseContextService: CaseContextInformationControllerService,
  ) {}

  private getCompanyRegisterInfo(company: Company) {
    const [registerType, ...registerNumbers] =
      company.companyInfo.register.id.split(' ');
    const registerNumber = registerNumbers.join(' ');

    const request = {
      registerCourt: company.companyInfo.register.city as RegisterCourt,
      registerNumber: registerNumber,
      registerType: registerType as RegisterType,
    };
    return request;
  }
}
