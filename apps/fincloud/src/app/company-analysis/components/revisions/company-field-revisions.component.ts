import {
  Component,
  Inject,
  Input,
  LOCALE_ID,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  DataGridConfig,
  DataGridViewMode,
} from '@fincloud/components/data-grid';
import { TableColumn, TableComponent } from '@fincloud/components/lists';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { Toast } from '@fincloud/core/toast';
import { RevisionTableRow } from '@fincloud/neoshare/business-case-fields';
import { StateLibCompanyPageActions } from '@fincloud/state/company-analysis';
import {
  User,
  UserManagementControllerService,
} from '@fincloud/swagger-generator/authorization-server';
import {
  Company,
  Information,
  InformationControllerService,
  InformationRevision,
} from '@fincloud/swagger-generator/company';
import { FieldType, Locale } from '@fincloud/types/enums';
import {
  AppState,
  Dictionary,
  DropdownInputItem,
} from '@fincloud/types/models';
import { FinToastService } from '@fincloud/ui/toast';
import { Store } from '@ngrx/store';
import { keyBy, uniq } from 'lodash-es';
import { Observable, finalize, forkJoin, map, of, switchMap, tap } from 'rxjs';
import { revisionsTableColumnsConfig } from '../../utils/revisions-table-columns';

@Component({
  selector: 'app-company-field-revisions',
  templateUrl: './company-field-revisions.component.html',
  styleUrls: ['./company-field-revisions.component.scss'],
})
export class CompanyFieldRevisionsComponent implements OnInit {
  @Input() information?: Information;
  @Input() company: Company;

  @ViewChild('table', { static: true }) table: TableComponent;

  columns: TableColumn[] = revisionsTableColumnsConfig(
    this.locale,
    this.regionalSettings.dateFormat,
  );
  selectedRow: RevisionTableRow;
  selectedRevision: RevisionTableRow;
  userNames: Dictionary<User> = {};
  rows$: Observable<RevisionTableRow[]>;
  loading = false;
  isFieldEditable = true;
  private allRevisions: RevisionTableRow[];

  dataGridConfig: DataGridConfig | null;
  DataGridViewMode = DataGridViewMode;

  constructor(
    private informationControllerService: InformationControllerService,
    private finToastService: FinToastService,
    private store: Store<AppState>,
    private userManagementControllerService: UserManagementControllerService,
    @Inject(LOCALE_ID) private locale: Locale,
    private regionalSettings: RegionalSettingsService,
  ) {}

  ngOnInit(): void {
    this.information = structuredClone(this.information);
    this.getAllRevisions();
    this.isFieldEditable = !!this.information.field.expression?.length;
  }

  getAllRevisions() {
    this.rows$ = this.informationControllerService
      .getAllRevisions({
        companyId: this.company?.id,
        informationIdentifierKey: this.information.field.key,
      })
      .pipe(
        switchMap((revisions) => {
          return forkJoin([
            of(revisions),
            forkJoin(
              uniq(revisions.map((r) => r.userId)).map((u) =>
                this.userManagementControllerService.getUserById({
                  userId: u,
                }),
              ),
            ),
          ]);
        }),
        map(([revisions, usersById]) => {
          this.userNames = keyBy<User>(usersById, 'id');
          return revisions.map((revision) =>
            this.mapRevisionToRevisionTableRow(
              revision,
              this.isCurrentVersion(revision),
            ),
          );
        }),
        tap((revisions) => {
          this.allRevisions = revisions;
          const currentRevision = revisions.find(
            (revision) => revision.isCurrentVersion,
          );
          if (currentRevision) {
            this.selectRevision(currentRevision);
          }
        }),
        finalize(() => (this.loading = true)),
      );
  }

  getUserName(item: { userId?: string }) {
    return [
      this.userNames[item.userId]?.firstName,
      this.userNames[item.userId]?.lastName,
    ]
      .filter(Boolean)
      .join(' ');
  }

  mapRevisionToRevisionTableRow(
    revision: InformationRevision,
    isCurrentVersion: boolean,
  ): RevisionTableRow {
    return {
      date: revision.creationDate,
      revisionId: revision.id,
      information: revision.information.value,
      isCurrentVersion,
      changedFrom: this.getUserName(revision),
      changedFromId: revision.userId,
    };
  }

  reactivateRevision() {
    this.informationControllerService
      .restoreRevision({
        companyId: this.company?.id,
        revisionId: this.selectedRow.revisionId,
      })
      .pipe(
        tap(() => {
          this.allRevisions.forEach(
            (revision) => (revision.isCurrentVersion = false),
          );
          this.rows$ = of(this.allRevisions);
          this.information.revisionId = this.selectedRow.revisionId;
          this.selectedRow.isCurrentVersion = true;
          this.finToastService.show(Toast.success());
          this.store.dispatch(
            StateLibCompanyPageActions.reloadCompany({
              payload: this.company.id,
            }),
          );
        }),
      )
      .subscribe();
  }

  showRevision(row: RevisionTableRow) {
    this.table.selectRow(row);
    this.selectRevision(row);
  }

  private selectRevision(row: RevisionTableRow) {
    this.selectedRow = row;
    this.selectedRevision = this.selectedRow;

    if (this.isDataTable()) {
      this.dataGridConfig = DataGridConfig.buildFromInformationValue(
        this.selectedRevision.information as DataGridConfig,
      );
    } else {
      this.dataGridConfig = null;
    }
  }

  isDataTable() {
    return this.information?.field?.fieldType === FieldType.TABLE;
  }

  isCompositeField() {
    return this.information?.field?.fieldType === FieldType.COMPOSITE;
  }

  isLocationField() {
    return this.information?.field?.fieldType === FieldType.LOCATION;
  }

  isEmpty(selectedInformation: unknown) {
    return (
      selectedInformation === null ||
      selectedInformation === undefined ||
      JSON.stringify(selectedInformation) === '{}' ||
      JSON.stringify(selectedInformation) === '[]'
    );
  }

  buildCompositeFieldValue(): string {
    let suffix: string;
    switch (
      (this.selectedRevision?.information as DropdownInputItem)?.dropdownOption
        .label
    ) {
      case 'Prozentsatz':
        suffix = '%';
        break;
      case 'Numerisch':
        suffix = '€';
        break;
      default:
        suffix = '';
        break;
    }

    return `${
      (this.selectedRevision?.information as DropdownInputItem)?.inputValue ??
      ''
    } ${suffix}`;
  }

  private isCurrentVersion(revision: InformationRevision) {
    return this.information?.revisionId === revision.id;
  }
}
