import {
  AfterContentInit,
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ScrollCommunicationService } from '@fincloud/core/scroll';
import {
  StateLibBusinessCaseDataRoomPageActions,
  selectBusinessCaseCadrTreeMenuData,
  selectHighlighted,
  selectIsBusinessCaseHeaderInView,
} from '@fincloud/state/business-case';
import { CadrGroup } from '@fincloud/swagger-generator/company';
import { DataRoomGroupTemplateName } from '@fincloud/types/enums';
import { FinButtonAppearance } from '@fincloud/ui/button';
import { FinTreeNode, FinTreeNodeAppearance } from '@fincloud/ui/tree-menu';
import { FinSize } from '@fincloud/ui/types';
import { SCROLL_TO_DELAY } from '@fincloud/utils';
import { Store } from '@ngrx/store';
import {
  Observable,
  asapScheduler,
  filter,
  shareReplay,
  subscribeOn,
  tap,
} from 'rxjs';

@Component({
  selector: 'app-business-case-cadr-sidebar',
  templateUrl: './business-case-cadr-sidebar.component.html',
  styleUrl: './business-case-cadr-sidebar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BusinessCaseCadrSidebarComponent implements AfterContentInit {
  readonly finButtonAppearance = FinButtonAppearance;
  readonly finTreeNodeAppearance = FinTreeNodeAppearance;
  readonly finSize = FinSize;
  readonly dataRoomGroupTemplateName = DataRoomGroupTemplateName;

  isBusinessCaseHeaderInView$ = this.store.select(
    selectIsBusinessCaseHeaderInView,
  );

  groupsTreeMenu$: Observable<FinTreeNode<CadrGroup>[]> = this.store.select(
    selectBusinessCaseCadrTreeMenuData,
  );
  highlighted$ = this.store
    .select(selectHighlighted)
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));

  constructor(
    private store: Store,
    public destroyRef: DestroyRef,
    private scrollCommunicationService: ScrollCommunicationService,
  ) {}

  goToField = (event: Event, node: FinTreeNode<any>) => {
    event.stopPropagation();

    this.store.dispatch(
      StateLibBusinessCaseDataRoomPageActions.highlightDataRoomItem({ node }),
    );
  };

  ngAfterContentInit(): void {
    this.highlighted$
      .pipe(
        filter(({ key }) => !!key),
        subscribeOn(asapScheduler, SCROLL_TO_DELAY),
        tap(({ key }) =>
          this.scrollCommunicationService.scrollToElementById(key),
        ),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
