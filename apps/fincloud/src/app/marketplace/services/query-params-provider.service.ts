/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import {
  CustomerFilter,
  FilterList,
  IndustryFilter,
  MinMaxFilter,
} from '@fincloud/swagger-generator/watchlist';
import { SortCriteria } from '@fincloud/types/models';
import { ObjectHelper } from '@fincloud/utils';
import { QueryParams } from '../models/query-params';
import { entries } from '../utils/entries';

@Injectable({
  providedIn: 'root',
})
export class QueryParamsProviderService {
  constructor(
    private router: Router,
    private route: ActivatedRoute,
  ) {}

  public updateQueryParams(
    filters: FilterList,
    sortCriteria: SortCriteria,
  ): Promise<boolean> {
    const activeFilters: {
      [key: string]: CustomerFilter | IndustryFilter | MinMaxFilter;
    } = {};
    entries(filters)
      .filter(([, value]) => value.isActive === true)
      .forEach(([key, filter]) => {
        activeFilters[key] = filter;
      });
    return this.setQueryParams({
      filters: activeFilters,
      ...sortCriteria,
    });
  }

  public mergeQueryParamsToUrl(
    query: Record<string, unknown>,
  ): Promise<boolean> {
    return this.router.navigate([], {
      queryParams: this.getQueryParams(query),
      queryParamsHandling: 'merge',
      skipLocationChange: false,
    });
  }

  public createObjectOfTypeFromQueryParams<T = Record<string, unknown>>(
    queryParams: Params,
    keysOfTypeArray: string[] = ['customerEntries', 'industries'],
  ): T {
    const result = {};

    if (queryParams) {
      // For each object path (property key) in the object
      for (const queryParam in queryParams) {
        // Split path into component parts
        const parts = queryParam.split('.');

        // Create sub-objects along path as needed
        let target: Record<string, any> = result;
        while (parts.length > 1) {
          const part = parts.shift();
          target = target[part] =
            target[part] || (keysOfTypeArray.find((e) => e === part) ? [] : {});
        }

        // Set value at end of path
        target[parts[0]] = ObjectHelper.parseString(queryParams[queryParam]);
      }
    }

    return result as T;
  }

  public filtersPresentInQueryParams(queryParams: Params) {
    return Boolean(
      Object.keys(queryParams).find((key) => key.indexOf('filters') !== -1),
    );
  }

  public sortCriteriaPresentInQueryParams(queryParams: Params) {
    return Boolean(
      Object.keys(queryParams).find(
        (key) =>
          key.indexOf('sortOrder') !== -1 || key.indexOf('sortBy') !== -1,
      ),
    );
  }

  public getQueryParams(query: Record<string, unknown>) {
    const queryParams: QueryParams = {};

    if (query) {
      for (const propName in query) {
        this.addValueParams(query[propName], propName, queryParams);
      }
    }

    return queryParams;
  }

  public clearQueryParams(): Promise<boolean> {
    return this.router.navigate([], {
      queryParams: {},
      relativeTo: this.route,
    });
  }

  private getObjectParams(obj: QueryParams, parentKey: string) {
    const flatParams: { [id: string]: string } = {};
    for (const key in obj) {
      const value = obj[key];
      const newKey = parentKey ? parentKey + '.' + key : key; // joined key with dot
      this.addValueParams(value, newKey, flatParams);
    }

    return flatParams;
  }

  private addValueParams(
    value: unknown,
    currentKey: string,
    currentParams: QueryParams,
  ) {
    // Using != instead of !== because it catches also undefined - null != undefined is false and null !== undefined is true
    if (value != null) {
      if (value instanceof Object && !(value instanceof Date)) {
        const childObjectParams = this.getObjectParams(
          <QueryParams>value,
          currentKey,
        );
        Object.assign(currentParams, childObjectParams);
      } else {
        currentParams[currentKey] = value.toString(); // it's not an object, so set the property
      }
    }
  }

  private setQueryParams(query: Record<string, unknown>): Promise<boolean> {
    return this.router.navigate([], {
      queryParams: this.getQueryParams(query),
      skipLocationChange: false,
    });
  }
}
