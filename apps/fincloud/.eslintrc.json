{"extends": ["../../.eslintrc.json"], "ignorePatterns": ["!**/*"], "overrides": [{"files": ["*.ts"], "extends": ["plugin:@nx/angular", "plugin:@angular-eslint/template/process-inline-templates", "plugin:prettier/recommended"], "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "app", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "app", "style": "kebab-case"}], "prettier/prettier": ["error", {"endOfLine": "auto"}]}}, {"files": ["*.ts"], "excludedFiles": ["**/app.component.ts"], "extends": ["plugin:@nx/angular", "plugin:@angular-eslint/template/process-inline-templates", "plugin:prettier/recommended"], "rules": {"check-file/folder-match-with-fex": ["error", {"**/*.component.{ts,html,scss}": "**/components/**/"}]}}, {"files": ["**/utils/*.ts", "**/models/*.ts"], "extends": ["plugin:@nx/typescript"], "rules": {"@fincloud/ns/no-multiple-exports": "error"}}, {"files": ["**/utils/*.ts", "**/models/*.ts"], "excludedFiles": ["**/jointjs/utils/*.ts"], "extends": ["plugin:@nx/typescript"], "rules": {"check-file/filename-naming-convention": ["error", {"**/*.ts": "KEBAB_CASE"}]}}, {"files": ["*.ts"], "excludedFiles": ["*.spec.ts"], "extends": ["plugin:@nx/typescript"], "rules": {"@fincloud/ns/no-cross-module-imports": "error"}}, {"files": ["**/+state/actions/*.actions.ts"], "rules": {"@fincloud/ns/no-ngrx-actions-missing-format": "error", "@typescript-eslint/naming-convention": ["error", {"selector": "variable", "modifiers": ["const", "exported"], "types": ["function"], "format": ["camelCase"]}]}}, {"files": ["**/+state/actions/index.ts"], "extends": ["plugin:@nx/typescript"], "rules": {"@fincloud/ns/no-ngrx-incorrect-actions-barrel-exports": "error", "@fincloud/ns/no-ngrx-actions-exports-mismatch": "error"}}, {"files": ["**/+state/**/*.ts"], "excludedFiles": ["**/index.ts", "**/*.spec.ts"], "extends": ["plugin:@nx/typescript", "plugin:@ngrx/recommended"], "rules": {"check-file/filename-naming-convention": ["error", {"**/*.ts": "**/[a-z0-9-]*.@(actions|reducer|metareducer|effects|selectors)"}], "check-file/folder-naming-convention": ["error", {"[+]state/*": "@(actions|reducers|metareducers|effects|selectors)"}], "@ngrx/prefer-effect-callback-in-block-statement": "off", "@ngrx/prefix-selectors-with-select": "error", "@ngrx/on-function-explicit-return-type": "error"}}, {"files": ["*.html"], "extends": ["plugin:@nx/angular-template", "plugin:prettier/recommended"], "rules": {"@angular-eslint/template/prefer-control-flow": "error", "prettier/prettier": ["error", {"endOfLine": "auto"}]}}]}