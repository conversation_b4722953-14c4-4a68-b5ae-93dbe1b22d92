import path from 'path';
import { Project } from 'ts-morph';

// create a project
const project = new Project({
  tsConfigFilePath: 'tsconfig.base.json',
});

// add source files
project.addSourceFilesAtPaths('apps/fincloud/**/*.ts');
project.addSourceFilesAtPaths('libs/**/*.ts');

project
  .getSourceFiles('libs/types/models/src/lib/utils/**/*.ts')
  .forEach((sourceFile) => {
    const fileBaseName = path.basename(sourceFile.getFilePath());
    if (fileBaseName.includes('.module')) {
      return;
    }
    console.log(fileBaseName);
    // ----- exported functions -----
    const exportedFunctionNames = sourceFile
      .getFunctions()
      .filter((fn) => fn.isExported())
      .map((fn) => fn.getName())
      .filter(Boolean);

    // ----- exported constants (e.g. `export const FOO = 123;`) -----
    const exportedConstantNames = sourceFile
      .getVariableStatements()
      .filter(
        (stmt) => stmt.isExported() && stmt.getDeclarationKind() === 'const',
      )
      .flatMap((stmt) => stmt.getDeclarations())
      .map((decl) => decl.getName())
      .filter(Boolean);

    // If you only need a single name, grab the first one;
    // otherwise use `exportedFunctionNames` / `exportedConstantNames` arrays directly.
    const exportFunctionName = exportedFunctionNames[0] ?? '';
    const exportConstantName = exportedConstantNames[0] ?? '';

    updateImports(exportFunctionName || exportConstantName);
  });

// get source files

// save changes
project.saveSync();
function updateImports(importName) {
  const sourceFiles = project.getSourceFiles();

  // iterate over each source file
  sourceFiles.forEach((sourceFile) => {
    // get all import declarations
    const importDeclarations = sourceFile.getImportDeclarations();

    // iterate over each import declaration
    importDeclarations.forEach((importDeclaration) => {
      // check if import declaration is from '@fincloud/types/models'
      if (
        importDeclaration.getModuleSpecifierValue() === '@fincloud/types/models'
      ) {
        // get named imports
        const namedImports = importDeclaration.getNamedImports();

        // check if the named import is imported
        const businessCasePermissionImport = namedImports.find(
          (namedImport) => namedImport.getName() === importName,
        );

        if (businessCasePermissionImport) {
          // if 'BusinessCasePermission' is the only import, change the module specifier
          if (namedImports.length === 1) {
            importDeclaration.setModuleSpecifier('@fincloud/utils');
          } else {
            // if there are other imports, remove 'BusinessCasePermission' and add a new import declaration
            businessCasePermissionImport.remove();
            sourceFile.addImportDeclaration({
              namedImports: [importName],
              moduleSpecifier: '@fincloud/utils',
            });
          }
        }
      }
    });
  });
}
