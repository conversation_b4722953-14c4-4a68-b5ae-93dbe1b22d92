import fs from 'fs/promises';
import { glob } from 'glob';
import { Project } from 'ts-morph';

const DIRECTORY = 'libs/types/utils/src/lib/';

async function main() {
  try {
    await fs.access(DIRECTORY);

    const tsFiles = await glob(`${DIRECTORY}/**/*.ts`);
    if (tsFiles.length === 0) {
      console.log('No TypeScript files found in the specified directory.');
      return;
    }

    const project = new Project({
      tsConfigFilePath: 'tsconfig.base.json',
    });

    project.addSourceFilesAtPaths(tsFiles);

    const singleUsedExports = [];

    for (const filePath of tsFiles) {
      const sourceFile = project.getSourceFile(filePath);
      if (!sourceFile) continue;

      // Find exported constants or functions
      const exports = [
        ...sourceFile.getFunctions().filter((fn) => fn.isExported()),
        ...sourceFile
          .getVariableStatements()
          .flatMap((vs) =>
            vs.getDeclarations().filter((decl) => vs.isExported()),
          ),
      ];

      if (exports.length === 0) {
        continue; // No exports found
      }
      if (exports.length > 1) {
        console.warn(
          `Warning: ${filePath} has multiple exports. Only the first will be checked.`,
        );
      }

      const exportedItem = exports[0];
      const exportedName = exportedItem.getName
        ? exportedItem.getName()
        : undefined;
      if (!exportedName) continue;

      // Find references to the exported item
      const refs = exportedItem.findReferences();
      const referencingFiles = new Set();

      refs.forEach((ref) => {
        ref.getReferences().forEach((refNode) => {
          const refSourceFile = refNode.getSourceFile();
          const refFilePath = refSourceFile.getFilePath();
          if (refFilePath !== sourceFile.getFilePath()) {
            referencingFiles.add(refFilePath);
          }
        });
      });

      if (referencingFiles.size === 1) {
        singleUsedExports.push({
          file: filePath,
          export: exportedName,
          referencedIn: Array.from(referencingFiles),
        });
      }
    }

    if (singleUsedExports.length === 0) {
      console.log(
        'No exported constants or functions are referenced in exactly one place.',
      );
    } else {
      console.log('Exports referenced in exactly one place:');
      for (const entry of singleUsedExports) {
        console.log(
          `- ${entry.file} (export: ${entry.export}) is referenced in:`,
        );
        entry.referencedIn.forEach((f) => console.log(`    ${f}`));
      }
    }
  } catch (err) {
    if (err.code === 'ENOENT') {
      console.error(`Directory "${DIRECTORY}" does not exist.`);
    } else {
      console.error('Error:', err.message);
    }
    process.exit(1);
  }
}

main();
