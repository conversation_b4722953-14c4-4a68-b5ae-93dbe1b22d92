// reorganize-files-sync.mjs
// -----------------------------------------------------------
// Synchronous version
// • *.validator.ts  → moved into sibling “validators” folder
// • *.state.ts      → renamed to *-state.ts
// • invalid suffix  → stripped entirely
//
// Requires:  npm i ts-morph glob
//
// Run with Node ≥ 14:
//   node reorganize-files-sync.mjs
// -----------------------------------------------------------

import fs from 'fs';
import { globSync } from 'glob';
import path from 'path';
import { Project } from 'ts-morph';
import { fileURLToPath } from 'url';

// ---------------------------------------------------------------------------
// Setup
// ---------------------------------------------------------------------------

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/** Folders to scan, relative to repo root. */
const SEARCH_PATHS = ['./libs', './apps/fincloud/src/app'];

/** Glob patterns that should be skipped entirely. */
const IGNORE_PATTERNS = [
  '**/shapes/**',
  '**/locales/**',
  '**/swagger-generator/**',
  '**/jointjs/**',
];

/** Ensure a directory exists (mkdir -p). */
function ensureDirSync(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

/** ts-morph project (keeps import paths in sync when files move/rename). */
const project = new Project({
  tsConfigFilePath: './tsconfig.base.json',
});

// ---------------------------------------------------------------------------
// Tasks
// ---------------------------------------------------------------------------

/** Move every *.validator.ts file into a sibling “validators” folder. */
function relocateValidatorFilesSync() {
  for (const base of SEARCH_PATHS) {
    const pattern = path.join(base, '**', '*.validator.ts');
    for (const file of globSync(pattern, {
      nodir: true,
      ignore: IGNORE_PATTERNS,
    })) {
      const abs = path.resolve(file);
      const parentDir = path.resolve(path.dirname(abs), '../');

      // Already in …/validators → nothing to do.
      if (path.basename(parentDir) === 'validators') continue;

      const targetDir = path.join(parentDir, 'validators');
      const targetPath = path.join(targetDir, path.basename(abs));

      try {
        ensureDirSync(targetDir);
        const src = project.addSourceFileAtPath(abs);
        src.move(targetPath); // updates imports
      } catch (err) {
        console.error(`❌  Moving ${file} → ${targetPath} failed:`, err);
      }
    }
  }
}

/** Rename every *.state.ts file to *-state.ts. */
function renameStateFilesSync() {
  for (const base of SEARCH_PATHS) {
    const pattern = path.join(base, '**', '*.{state,config}.ts');
    for (const file of globSync(pattern, {
      nodir: true,
      ignore: IGNORE_PATTERNS,
    })) {
      const abs = path.resolve(file);
      const filename = path.basename(abs);

      // Skip if it already ends with “-state.ts”.
      if (filename.endsWith('-state.ts')) continue;

      const newFilename = filename.replace(/\.state\.ts$/, '-state.ts');
      const newPath = path.join(path.dirname(abs), newFilename);

      try {
        const src = project.addSourceFileAtPath(abs);
        src.move(newPath); // updates imports
      } catch (err) {
        console.error(`❌  Renaming ${file} → ${newPath} failed:`, err);
      }
    }
  }
}

// ---------------------------------------------------------------------------
// Invalid suffix fixer
// ---------------------------------------------------------------------------

/** Set of suffixes that are allowed to remain unchanged. */
const VALID_SUFFIXES = new Set([
  '.component.',
  '.directive.',
  '.pipe.',
  '.service.',
  '.module.',
  '.guard.',
  '.interceptor.',
  '.spec.',
  '.actions.',
  '.reducer.',
  '.effec.',
  '.selectors.',
  '.state.',
  '.facade.',
  '.adapter.',
  '.entity.',
  '.routes.',
  '.metareducer.',
  '.validator.',
  '.resolver.',
]);

/** Arrays to track outcomes. */
const renamedFiles = [];
const errorLog = [];

/**
 * Rename files whose final suffix (portion from the last dot onward) is
 * *not* in VALID_SUFFIXES by stripping the suffix entirely.
 */
function fixInvalidSuffixesSync() {
  for (const base of SEARCH_PATHS) {
    // Any file that contains at least one dot.
    const pattern = path.join(base, '**/{models,utils,enums}/**', '*.ts');
    for (const file of globSync(pattern, {
      nodir: true,
      ignore: IGNORE_PATTERNS,
    })) {
      const abs = path.resolve(file);
      const basename = path.basename(abs);

      // Skip hidden dotfiles such as .gitignore
      if (basename.startsWith('.')) continue;

      const lastDot = basename.lastIndexOf('.');
      if (lastDot === -1) continue; // no suffix

      // const suffix = basename.slice(lastDot);
      const regex = /\.(.*)\./gm;
      // const filePath = sf.getFilePath();
      const suffix = regex.exec(basename);

      if (suffix && VALID_SUFFIXES.has(suffix[0])) continue; // valid suffix – leave as is

      if (!suffix) continue;

      const newBasename = basename.replace(suffix[0], '.');
      const newPath = path.join(path.dirname(abs), newBasename);

      try {
        if (fs.existsSync(newPath)) {
          throw new Error('target already exists');
        }
        const src = project.addSourceFileAtPath(abs);
        src.move(newPath); // updates imports
        renamedFiles.push(`${file} → ${newBasename}`);
      } catch (err) {
        errorLog.push(`Failed to rename ${file}: ${err.message}`);
        console.error(`❌  Failed to rename ${file}:`, err);
      }
    }
  }
}

// ---------------------------------------------------------------------------
// Main
// ---------------------------------------------------------------------------

function main() {
  try {
    relocateValidatorFilesSync();
    project.saveSync();
    renameStateFilesSync();
    project.saveSync();
    fixInvalidSuffixesSync();
    project.saveSync();
    console.log('✅  File re-organisation complete.');
    console.log(`🔄  Renamed invalid suffix files: ${renamedFiles.length}`);
    renamedFiles.forEach((f) => console.log(`   • ${f}`));
    if (errorLog.length) {
      console.log(`⚠️  Errors encountered: ${errorLog.length}`);
      errorLog.forEach((e) => console.error(`   • ${e}`));
    }
  } catch (err) {
    console.error('💥  Unexpected error:', err);
    process.exitCode = 1;
  }
}

main();
