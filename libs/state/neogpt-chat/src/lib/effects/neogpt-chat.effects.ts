import { DOCUMENT } from '@angular/common';
import { Inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { FileService } from '@fincloud/core/files';
import { ModalService } from '@fincloud/core/modal';
import { LocaleService } from '@fincloud/core/services';
import {
  StateLibBusinessCasePageActions,
  selectBusinessCaseId,
} from '@fincloud/state/business-case';
import {
  StateLibDocumentApiActions,
  StateLibDocumentPageActions,
} from '@fincloud/state/document';
import { StateLibDocumentInboxPageActions } from '@fincloud/state/document-inbox';
import { selectRouteCustomerKey } from '@fincloud/state/router';
import { selectUser } from '@fincloud/state/user';
import {
  ChatService,
  IngestionService,
  ModelLanguages,
  QueryContext,
  QueryService,
} from '@fincloud/swagger-generator/neo-gpt';
import {
  DocumentStatus,
  Locale,
  NeoGptActiveSession,
  NeoGptChatContext,
} from '@fincloud/types/enums';
import { NeoGptDocumentStatus } from '@fincloud/types/models';
import { canPreviewDocument } from '@fincloud/utils';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import {
  catchError,
  exhaustMap,
  filter,
  fromEvent,
  map,
  merge,
  of,
  switchMap,
  takeUntil,
  tap,
  timer,
} from 'rxjs';
import {
  StateLibNeogptChatApiActions,
  StateLibNeogptChatPageActions,
} from '../actions';
import {
  selectChatSessionId,
  selectDocumentId,
  selectDocumentState,
  selectGetHistoryRequest,
  selectHasUnreadMessages,
  selectIsSwitchContextPromptVisible,
  selectNeoGptActiveSession,
  selectSendBusinessQueryRequest,
} from '../selectors/neogpt-chat.selectors';

@Injectable({
  providedIn: 'root',
})
export class StateLibNeogtpChatEffects {
  constructor(
    private actions$: Actions,
    private store: Store,
    private router: Router,
    private ingestionService: IngestionService,
    private queryService: QueryService,
    private localeService: LocaleService,
    private chatService: ChatService,
    private modalService: ModalService,
    private fileService: FileService,

    @Inject(DOCUMENT) private document: Document,
  ) {}

  getDocumentStatus$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(StateLibDocumentPageActions.documentIsLoaded),
      concatLatestFrom(() => [
        this.store.select(selectDocumentState),
        this.store.select(selectNeoGptActiveSession),
      ]),
      filter(
        ([, , activeSession]) =>
          activeSession !== NeoGptActiveSession.NON_SESSION,
      ),
      switchMap(([{ documentId }]) => {
        let currentDocumentState: DocumentStatus;
        return timer(0, 5000).pipe(
          switchMap(() =>
            this.ingestionService
              .getDocumentStatusDocumentStatusDocIdGet({
                doc_id: documentId,
              })
              .pipe(
                map((documentStatus) => {
                  const chatState = (documentStatus as NeoGptDocumentStatus)
                    .chat_state;
                  // According to ML team (a.k.a Misho) there should not be differences
                  // betweem PENDING & DOCUMENT_PENDING for the UI. They use them internally,
                  // but still send them to us.
                  if (
                    chatState === DocumentStatus.DOCUMENT_PENDING &&
                    currentDocumentState !== DocumentStatus.DOCUMENT_PENDING
                  ) {
                    return StateLibNeogptChatApiActions.getDocumentStatusPendingSuccess(
                      {
                        documentStatus: documentStatus as NeoGptDocumentStatus,
                      },
                    );
                  }
                  if (
                    chatState === DocumentStatus.PENDING &&
                    currentDocumentState !== DocumentStatus.PENDING
                  ) {
                    currentDocumentState = chatState;
                    return StateLibNeogptChatApiActions.getDocumentStatusPendingSuccess(
                      {
                        documentStatus: documentStatus as NeoGptDocumentStatus,
                      },
                    );
                  }
                  if (
                    chatState === DocumentStatus.LIMITED_READY &&
                    currentDocumentState !== DocumentStatus.LIMITED_READY
                  ) {
                    currentDocumentState = chatState;
                    return StateLibNeogptChatApiActions.getDocumentStatusLimitedReadySuccess(
                      {
                        documentStatus: documentStatus as NeoGptDocumentStatus,
                      },
                    );
                  }
                  if (
                    chatState === DocumentStatus.READY &&
                    currentDocumentState !== DocumentStatus.READY
                  ) {
                    currentDocumentState = chatState;
                    return StateLibNeogptChatApiActions.getDocumentStatusReadySuccess(
                      {
                        documentStatus: documentStatus as NeoGptDocumentStatus,
                      },
                    );
                  }
                  return StateLibNeogptChatApiActions.getDocumentStatusSuccess({
                    documentStatus: documentStatus as NeoGptDocumentStatus,
                  });
                }),
                catchError(() =>
                  of(StateLibNeogptChatApiActions.getDocumentStatusFailure()),
                ),
              ),
          ),
          takeUntil(
            this.actions$.pipe(
              ofType(
                StateLibNeogptChatApiActions.getDocumentStatusReadySuccess,
                StateLibNeogptChatApiActions.getDocumentStatusFailure,
                StateLibNeogptChatPageActions.closeChat,
                StateLibDocumentPageActions.documentPreviewClosed,
              ),
            ),
          ),
        );
      }),
    );
  });

  setChatMessageAsSeen$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        StateLibNeogptChatPageActions.setChatMessageAsSeenOnScrollToBottom,
      ),
      concatLatestFrom(() => [
        this.store.select(selectChatSessionId),
        this.store.select(selectHasUnreadMessages),
      ]),
      filter(
        ([_, selectChatSessionId, hasUnreadMessages]) =>
          hasUnreadMessages && !!selectChatSessionId,
      ),
      switchMap(([_, sessionId]) => {
        return this.chatService.setLastSeenMessageChatSetLastSeenMessagePost({
          session_id: sessionId,
        });
      }),
      map((chatHistory) =>
        StateLibNeogptChatPageActions.setChatMessageOnSeen({
          // We are interesting only of the very last message
          // assuming if last one is seen, rest are also
          message: chatHistory.pop(),
        }),
      ),
    );
  });

  startDocumentSession$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        StateLibNeogptChatApiActions.getDocumentStatusReadySuccess,
        StateLibNeogptChatApiActions.getDocumentStatusLimitedReadySuccess,
      ),
      concatLatestFrom(() => [
        this.store.select(selectDocumentId),
        this.store.select(selectChatSessionId),
        this.store.select(selectUser),
      ]),

      switchMap(([, documentId, sessionId, user]) => {
        const locale = this.localeService.getUserPreferredLocale(user);
        const lang =
          locale === Locale.DE ? ModelLanguages.German : ModelLanguages.English;

        return this.queryService
          .startChatStartDocIdPost({
            lang,
            doc_id: documentId,
            session_id: sessionId,
            user_id: user.id,
          })
          .pipe(
            map((chatInitialData) =>
              StateLibNeogptChatApiActions.startDocumentSessionSuccess({
                chatInitialData,
              }),
            ),
            catchError((error) =>
              of(
                StateLibNeogptChatApiActions.startDocumentSessionFailure(error),
              ),
            ),
          );
      }),
    );
  });

  sendDocumentQuery$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(StateLibNeogptChatPageActions.sendQuery),
      concatLatestFrom(() => [
        this.store.select(selectSendBusinessQueryRequest),
      ]),
      filter(
        ([action, { documentId, activeSession }]) =>
          activeSession === NeoGptActiveSession.DATA_ROOM &&
          documentId.length > 0,
      ),
      exhaustMap(
        ([
          action,
          { documentId, sessionId, user, businessCaseId, userQuery },
        ]) => {
          const locale = this.localeService.getUserPreferredLocale(user);
          const language =
            locale === Locale.DE
              ? ModelLanguages.German
              : ModelLanguages.English;

          return this.queryService
            .queryDocumentsDocumentsQueryDocIdPost({
              language,
              doc_id: documentId,
              document_query: userQuery,
              session_id: sessionId,
              business_case_id: businessCaseId,
            })
            .pipe(
              map((neogptAnswer) =>
                StateLibNeogptChatApiActions.sendDocumentQuerySuccess({
                  neogptAnswer,
                }),
              ),
              catchError((error) =>
                of(
                  StateLibNeogptChatApiActions.sendDocumentQueryFailure(error),
                ),
              ),
            );
        },
      ),
    );
  });

  sendBusinessCaseQuery$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        StateLibNeogptChatPageActions.switchContext,
        StateLibNeogptChatPageActions.sendQuery,
      ),
      concatLatestFrom(() => [
        this.store.select(selectSendBusinessQueryRequest),
        this.store.select(selectIsSwitchContextPromptVisible),
      ]),
      filter(
        ([action, { documentId, activeSession }, context]) =>
          activeSession === NeoGptActiveSession.DATA_ROOM &&
          (documentId === '' || context === NeoGptChatContext.DATA_ROOM),
      ),
      exhaustMap(([action, { businessCaseId, sessionId, user, userQuery }]) => {
        const locale = this.localeService.getUserPreferredLocale(user);
        const language =
          locale === Locale.DE ? ModelLanguages.German : ModelLanguages.English;

        return this.queryService
          .queryBusinessCaseV2BusinessCasesQueryBusinessCaseIdPost({
            language,
            document_query: userQuery,
            session_id: sessionId,
            business_case_id: businessCaseId,
            context: QueryContext.DataRoom,
          })
          .pipe(
            map((neogptAnswer) =>
              StateLibNeogptChatApiActions.sendBusinessCaseQuerySuccess({
                neogptAnswer,
              }),
            ),
            catchError((error) =>
              of(
                StateLibNeogptChatApiActions.sendBusinessCaseQueryFailure(
                  error,
                ),
              ),
            ),
          );
      }),
    );
  });

  getHistory$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        StateLibNeogptChatPageActions.setActiveSession,
        StateLibDocumentApiActions.documentLoadedSuccess,
      ),
      concatLatestFrom(() => [this.store.select(selectGetHistoryRequest)]),
      filter(([action, { activeSession, chatMessages, sessionId }]) => {
        return (
          activeSession !== NeoGptActiveSession.NON_SESSION &&
          chatMessages.length === 0
        );
      }),
      switchMap(([action, { sessionId }]) => {
        return this.chatService
          .fetchHistoryChatHistoryGet({
            session_id: sessionId,
            start: 0,
            stop: -1,
          })
          .pipe(
            map((chatHistory) =>
              StateLibNeogptChatApiActions.getChatHistorySuccess({
                chatHistory,
              }),
            ),
            catchError((error) =>
              of(StateLibNeogptChatApiActions.getChatHistoryFailure(error)),
            ),
          );
      }),
    );
  });

  selectField$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(StateLibNeogptChatPageActions.selectField),
      concatLatestFrom(() => [
        this.store.select(selectRouteCustomerKey),
        this.store.select(selectBusinessCaseId),
      ]),
      filter(([action]) => action.session === NeoGptActiveSession.DATA_ROOM),
      tap(([, customerKey, businessCaseId]) =>
        this.router.navigate([
          customerKey,
          'business-case',
          businessCaseId,
          'data-room',
          'case',
        ]),
      ),
      switchMap(([{ selectedFieldId }]) =>
        merge(
          fromEvent(this.document, 'click').pipe(
            map((event) => {
              const elementRef = event.target as HTMLElement;

              if (!Object.keys(elementRef.dataset).length) {
                return elementRef.parentElement;
              }

              return elementRef;
            }),
            filter((elementRef) => {
              return (
                (elementRef.dataset.fieldId ||
                  elementRef.parentElement.dataset.fieldId) !== selectedFieldId
              );
            }),
            map(() => {
              return StateLibNeogptChatPageActions.deselectField();
            }),
          ),
        ).pipe(
          takeUntil(
            this.actions$.pipe(
              ofType(StateLibNeogptChatPageActions.deselectField),
            ),
          ),
        ),
      ),
    );
  });

  goToDocumentSource$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibNeogptChatPageActions.goToDocumentSource),
      concatLatestFrom(() => [this.store.select(selectNeoGptActiveSession)]),
      filter(
        ([, activeSession]) => activeSession === NeoGptActiveSession.DATA_ROOM,
      ),
      map(([documentData]) => {
        if (documentData.inbox) {
          return StateLibDocumentInboxPageActions.activateInboxDocument(
            documentData,
          );
        }
        return StateLibDocumentPageActions.openPdfViewer(documentData);
      }),
    ),
  );

  activateInboxDocument$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibDocumentInboxPageActions.activateInboxDocument),
      concatLatestFrom(() => this.store.select(selectBusinessCaseId)),
      switchMap(([documentData, businessCaseId]) => {
        if (canPreviewDocument(documentData.fileExt)) {
          return [
            StateLibDocumentInboxPageActions.loadDocumentInbox({
              payload: { businessCaseId },
            }),
            StateLibBusinessCasePageActions.updateEditTemplateMode({
              payload: true,
            }),
            StateLibDocumentPageActions.openPdfViewer(documentData),
          ];
        }

        return [
          StateLibDocumentInboxPageActions.loadDocumentInbox({
            payload: { businessCaseId },
          }),
          StateLibBusinessCasePageActions.updateEditTemplateMode({
            payload: true,
          }),
        ];
      }),
    ),
  );

  closeDocumentViewer$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(StateLibNeogptChatPageActions.selectField),
        tap(() => {
          this.modalService.closeActiveModals();
        }),
      );
    },
    { dispatch: false },
  );

  downloadDocument$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(StateLibNeogptChatPageActions.downloadDocument),
        tap(({ documentId }) => {
          this.fileService.downloadDocument(documentId);
        }),
      );
    },
    { dispatch: false },
  );
}
