import { Injectable } from '@angular/core';
import { selectUserId } from '@fincloud/state/user';
import { ChatService } from '@fincloud/swagger-generator/neo-gpt';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { catchError, filter, map, of, switchMap } from 'rxjs';
import {
  StateLibNeogptChatSettingsApiActions,
  StateLibNeogptChatSettingsPageActions,
} from '../actions';
import {
  selectChatSessionId,
  selectChatSettingsVisibility,
} from '../selectors/neogpt-chat.selectors';

@Injectable({
  providedIn: 'root',
})
export class StateLibNeogtpChatSettingsEffects {
  constructor(
    private actions$: Actions,
    private store: Store,
    private chatService: ChatService,
  ) {}

  toggleChatHistory$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(StateLibNeogptChatSettingsPageActions.toggleChatHistory),
      concatLatestFrom(() => [
        this.store.select(selectChatSessionId),
        this.store.select(selectUserId),
      ]),
      switchMap(([{ isChatHistoryEnabled }, sessionId, userId]) => {
        return this.chatService
          .updateUserChatPreferencesChatUserSettingsUpdatePatch({
            session_id: sessionId,
            body: { keep_history: isChatHistoryEnabled },
          })
          .pipe(
            map(() =>
              StateLibNeogptChatSettingsApiActions.toggleChatHistorySuccess(),
            ),
            catchError((error) =>
              of(
                StateLibNeogptChatSettingsApiActions.toggleChatHistoryFailure(
                  error,
                ),
              ),
            ),
          );
      }),
    );
  });

  deleteChatHistory$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(StateLibNeogptChatSettingsPageActions.deleteChatHistory),
      concatLatestFrom(() => [
        this.store.select(selectChatSessionId),
        this.store.select(selectUserId),
      ]),
      switchMap(([action, sessionId, userId]) => {
        return this.chatService
          .deleteChatHistoryChatHistoryDelete({
            session_id: sessionId,
          })
          .pipe(
            map(() =>
              StateLibNeogptChatSettingsApiActions.deleteChatHistorySuccess(),
            ),
            catchError((error) =>
              of(
                StateLibNeogptChatSettingsApiActions.deleteChatHistoryFailure(
                  error,
                ),
              ),
            ),
          );
      }),
    );
  });

  getChatSettings$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(StateLibNeogptChatSettingsPageActions.toggleSettingsVisibility),
      concatLatestFrom(() => [
        this.store.select(selectChatSettingsVisibility),
        this.store.select(selectChatSessionId),
      ]),
      filter(
        ([action, isChatSettingsPanelVisible]) => isChatSettingsPanelVisible,
      ),
      switchMap(([action, isChatSettingsPanelVisible, sessionId]) => {
        return this.chatService
          .getUserChatPreferenceChatUserSettingsGetGet({
            session_id: sessionId,
          })
          .pipe(
            map((chatSettings) =>
              StateLibNeogptChatSettingsApiActions.getChatSettingsSuccess({
                chatSettings,
              }),
            ),
            catchError((error) =>
              of(
                StateLibNeogptChatSettingsApiActions.getChatSettingsFailure(
                  error,
                ),
              ),
            ),
          );
      }),
    );
  });
}
