import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { LocaleService } from '@fincloud/core/services';
import { selectBusinessCaseId } from '@fincloud/state/business-case';
import { StateLibScrollToSectionPageActions } from '@fincloud/state/business-case-real-estate';
import { StateLibDocumentPageActions } from '@fincloud/state/document';
import { StateLibDocumentInboxPageActions } from '@fincloud/state/document-inbox';
import { selectRouteCustomerKey } from '@fincloud/state/router';
import { StateLibNoopPageActions } from '@fincloud/state/utils';
import {
  ChunkType,
  MessageType,
  ModelLanguages,
  QueryContext,
  QueryService,
} from '@fincloud/swagger-generator/neo-gpt';
import {
  FinancingDetailsPath,
  Locale,
  NeoGptActiveSession,
  NeoGptGroupTypeName,
} from '@fincloud/types/enums';
import {
  CustomFinStructureField,
  NeoGptChatFieldSource,
} from '@fincloud/types/models';
import { HIGHLIGHT_PREFIX } from '@fincloud/utils';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { isNil } from 'lodash-es';
import { catchError, filter, map, mergeMap, of, switchMap, tap } from 'rxjs';
import {
  StateLibNeogptChatApiActions,
  StateLibNeogptChatPageActions,
} from '../actions';
import {
  selectChatMessages,
  selectNeoGptActiveSession,
  selectSendFinancingQueryRequest,
} from '../selectors/neogpt-chat.selectors';

@Injectable({
  providedIn: 'root',
})
export class StateLibNeogptFinancingCaseChatEffects {
  constructor(
    private actions$: Actions,
    private store: Store,
    private router: Router,
    private queryService: QueryService,
    private localeService: LocaleService,
  ) {}

  sendFinancingDetailsQuery$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibNeogptChatPageActions.sendQuery),
      concatLatestFrom(() => [
        this.store.select(selectSendFinancingQueryRequest),
      ]),
      filter(
        ([_, { activeSession }]) =>
          activeSession === NeoGptActiveSession.FINANCING_DETAILS,
      ),
      mergeMap(([{ userQuery }, { chatSessionId, user, businessCaseId }]) => {
        const locale = this.localeService.getUserPreferredLocale(user);
        const language =
          locale === Locale.DE ? ModelLanguages.German : ModelLanguages.English;
        return this.queryService
          .queryBusinessCaseV2BusinessCasesQueryBusinessCaseIdPost({
            document_query: userQuery,
            session_id: chatSessionId,
            business_case_id: businessCaseId,
            language,
            context: QueryContext.FinancingStructure,
          })
          .pipe(
            map((neogptAnswer) =>
              StateLibNeogptChatApiActions.sendFinancingCaseQuerySuccess({
                neogptAnswer,
              }),
            ),
            catchError((error) =>
              of(
                StateLibNeogptChatApiActions.sendFinancingCaseQueryFailure(
                  error,
                ),
              ),
            ),
          );
      }),
    ),
  );

  selectField$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibNeogptChatPageActions.selectField),
      concatLatestFrom(() => [
        this.store.select(selectChatMessages),
        this.store.select(selectRouteCustomerKey),
        this.store.select(selectBusinessCaseId),
      ]),
      filter(
        ([action]) => action.session === NeoGptActiveSession.FINANCING_DETAILS,
      ),
      tap(([, , customerKey, businessCaseId]) =>
        this.router.navigate([
          customerKey,
          'business-case',
          businessCaseId,
          FinancingDetailsPath.FINANCING_DETAILS,
          FinancingDetailsPath.FINANCING_STRUCTURE,
        ]),
      ),
      map(([action, chatMessages]) => {
        const assistMessages = chatMessages.filter(
          (msg) => msg.source === MessageType.Assistant,
        );
        const sourceId = action.selectedFieldId.split(HIGHLIGHT_PREFIX)[1];

        const message = assistMessages.find((msg) =>
          msg.content?.groupedSources?.fields
            ?.find(
              (source: { key: ChunkType; values: NeoGptChatFieldSource[] }) =>
                source.key === ChunkType.FinStructureField,
            )
            ?.values?.find(
              (field: CustomFinStructureField) => field.id === sourceId,
            ),
        );
        const finStructureFields: NeoGptChatFieldSource[] =
          message?.content?.groupedSources?.fields.find(
            (source: { key: ChunkType; values: NeoGptChatFieldSource[] }) =>
              source.key === ChunkType.FinStructureField,
          )?.values || [];

        const selectedField = finStructureFields.find(
          (field) => field.id === sourceId,
        );
        if (isNil(selectedField)) {
          return StateLibNoopPageActions.noop();
        }

        if (selectedField.groupType === NeoGptGroupTypeName.DYNAMIC) {
          return StateLibScrollToSectionPageActions.scrollToFinancingBuildingBlocks(
            {
              buldingBlockId: sourceId,
            },
          );
        }
        const [firstId, secondId, thirdId] = selectedField.groupIds || [];

        return StateLibScrollToSectionPageActions.scrollToSection({
          payload: {
            firstId,
            secondId,
            thirdId,
            highlightId: action.selectedFieldId,
          },
        });
      }),
    ),
  );

  goToDocumentSource$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibNeogptChatPageActions.goToDocumentSource),
      concatLatestFrom(() => [
        this.store.select(selectRouteCustomerKey),
        this.store.select(selectBusinessCaseId),
        this.store.select(selectNeoGptActiveSession),
      ]),
      filter(
        ([, , , activeSession]) =>
          activeSession === NeoGptActiveSession.FINANCING_DETAILS,
      ),
      switchMap(([documentData, customerKey, businessCaseId]) =>
        this.router
          .navigate([
            customerKey,
            'business-case',
            businessCaseId,
            'data-room',
            'case',
          ])
          .then(() => {
            if (documentData.inbox) {
              return StateLibDocumentInboxPageActions.activateInboxDocument(
                documentData,
              );
            }
            return StateLibDocumentPageActions.openPdfViewer(documentData);
          }),
      ),
    ),
  );
}
