import { Injectable } from '@angular/core';
import { Router } from '@angular/router';

import {
  StateLibCustomerPageActions,
  selectCustomerKey,
  selectCustomersByKey,
} from '@fincloud/state/customer';
import { StateLibUserPageActions } from '@fincloud/state/user';
import { InvitationControllerService } from '@fincloud/swagger-generator/application';
import { CustomerManagementControllerService } from '@fincloud/swagger-generator/authorization-server';
import { CUSTOMER_NOT_A_COLLABORATOR } from '@fincloud/utils';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { flatten, uniq } from 'lodash-es';
import { EMPTY, catchError, map, of, switchMap } from 'rxjs';
import {
  StateLibInvitationApiActions,
  StateLibInvitationPageActions,
} from '../actions';
import { selectSuccessfulAcceptedInvitations } from '../selectors/invitation.selectors';

@Injectable()
export class StateLibInvitationEffects {
  // TODO: Use invitations from store for dashboard
  loadInvitationOnUserLoaded$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibUserPageActions.setUserToken),
      switchMap(() => {
        return this.invitationControllerService
          .getAllInvitationsForCustomer()
          .pipe(
            map((res) => {
              return StateLibInvitationPageActions.setInvitations({
                payload: res,
              });
            }),
            catchError(() =>
              of(StateLibInvitationPageActions.setInvitations({ payload: [] })),
            ),
          );
      }),
    ),
  );

  getInvitationFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(StateLibInvitationApiActions.getInvitationFailure),
        concatLatestFrom(() => [
          this.store.select(selectSuccessfulAcceptedInvitations),
          this.store.select(selectCustomerKey),
        ]),
        map(([{ err }, acceptedInvitations, userCustomerKey]) => {
          if (err.error.code === CUSTOMER_NOT_A_COLLABORATOR) {
            const acceptedInvitation = acceptedInvitations[0];
            this.router.navigate([
              '/',
              userCustomerKey,
              'business-case',
              acceptedInvitation?.subBusinessCaseId,
            ]);
          }
        }),
      ),
    { dispatch: false },
  );

  loadNewCustomerNamesOnInvitationsLoaded$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        StateLibInvitationApiActions.fetchNewInvitationsSuccess,
        StateLibInvitationPageActions.setInvitations,
      ),
      concatLatestFrom(() => this.store.select(selectCustomersByKey)),
      switchMap(([action, customerNamesByKey]) => {
        const customerKeysToLoad = flatten(
          action.payload.map((i) => [
            i.callerCustomerKey,
            i.invitedCustomerKey,
          ]),
        ).filter((key) => !customerNamesByKey[key]);

        if (customerKeysToLoad.length) {
          return this.customerManagementControllerService
            .getMultipleCustomers({ customerKeys: uniq(customerKeysToLoad) })
            .pipe(
              map((res) => {
                return StateLibCustomerPageActions.setCustomersInfo({
                  payload: res,
                });
              }),
            );
        } else {
          return EMPTY;
        }
      }),
    ),
  );

  constructor(
    private actions$: Actions,
    private invitationControllerService: InvitationControllerService,
    private customerManagementControllerService: CustomerManagementControllerService,
    private store: Store,
    private router: Router,
  ) {}
}
