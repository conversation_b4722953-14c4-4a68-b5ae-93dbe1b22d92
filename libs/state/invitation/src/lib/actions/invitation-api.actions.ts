import { HttpErrorResponse } from '@angular/common/http';
import {
  BulkOperationResult,
  Invitation,
} from '@fincloud/swagger-generator/application';
import { createAction, props } from '@ngrx/store';

export const acceptInvitationSuccess = createAction(
  '[Invitation API] Accept Invitation Success',
  props<{ result: BulkOperationResult }>(),
);

export const acceptInvitationFailure = createAction(
  '[Invitation API] Accept Invitation Failure',
  props<{ err: HttpErrorResponse }>(),
);

export const declineInvitationSuccess = createAction(
  '[Invitation API] Decline Invitation Success',
  props<{ payload: Invitation }>(),
);

export const declineInvitationFailure = createAction(
  '[Invitation API] Decline Invitation Failure',
  props<{ err: HttpErrorResponse }>(),
);

export const getInvitationSuccess = createAction(
  '[Invitation API] Get Invitation Success',
  props<{ invitation: Invitation }>(),
);

export const getInvitationFailure = createAction(
  '[Invitation API] Get Invitation Failure',
  props<{ err: HttpErrorResponse }>(),
);

export const fetchNewInvitationsSuccess = createAction(
  '[Invitation API] Fetch new invitations Success',
  props<{ payload: Invitation[] }>(),
);

export const fetchNewInvitationsFailure = createAction(
  '[Invitation API] Fetch new invitations Failure',
);
