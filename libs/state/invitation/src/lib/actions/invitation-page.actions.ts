import { Invitation } from '@fincloud/swagger-generator/application';
import { createAction, props } from '@ngrx/store';

export const acceptInvitation = createAction(
  '[Invitation Page] Accept Invitation',
);

export const declineInvitation = createAction(
  '[Invitation Page] Decline Invitation',
);

export const getInvitationOnSuccessDecline = createAction(
  '[Invitation Page] Get Invitation On Success Decline',
);

export const setInvitations = createAction(
  '[Invitation Page] Set invitations',
  props<{ payload: Invitation[] }>(),
);
export const acceptGuestInvitation = createAction(
  '[Invitation Page] Accept guest invitation',
  props<{ payload: { invitation: Invitation } }>(),
);
export const rejectGuestInvitation = createAction(
  '[Invitation Page] Reject guest invitation',
  props<{ payload: Invitation }>(),
);

export const clearAcceptedInvitations = createAction(
  '[Invitation Page] Clear Accepted Invitations',
);

export const setBusinessCaseIdFromUrl = createAction(
  '[Invitation Page] Set Guest Business Case ID from URL',
  props<{ businessCaseIdFromUrl: string | null }>(),
);
