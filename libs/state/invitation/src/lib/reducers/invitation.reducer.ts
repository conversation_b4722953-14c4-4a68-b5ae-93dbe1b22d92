import { Invitation } from '@fincloud/swagger-generator/application';
import { InvitationState } from '@fincloud/types/models';
import { Action, ActionReducer, createReducer, on } from '@ngrx/store';
import {
  StateLibInvitationApiActions,
  StateLibInvitationPageActions,
} from '../actions';

export const initialInvitationState: InvitationState = {
  invitations: null,
  isInvitationWithNda: false,
  acceptedInvitations: {
    failed: [],
    successful: [],
  },
};

export const stateLibInvitationReducer: ActionReducer<InvitationState, Action> =
  createReducer(
    initialInvitationState,
    on(
      StateLibInvitationPageActions.setInvitations,
      (state, action): InvitationState => {
        return {
          ...state,
          invitations: action.payload,
        };
      },
    ),
    on(
      StateLibInvitationApiActions.fetchNewInvitationsSuccess,
      (state, action): InvitationState => {
        return {
          ...state,
          invitations: [...state.invitations, ...action.payload],
        };
      },
    ),
    on(StateLibInvitationPageActions.acceptGuestInvitation, (state, action) => {
      return {
        ...state,
        invitations: state.invitations.map((i) => {
          if (i.id === action.payload.invitation.id) {
            return action.payload.invitation;
          }
          return i;
        }),
      };
    }),
    on(
      StateLibInvitationPageActions.rejectGuestInvitation,
      (state, action): InvitationState => {
        return {
          ...state,
          invitations: state.invitations.map((i) => {
            if (i.id === action.payload.id) {
              return action.payload;
            }
            return i;
          }),
        };
      },
    ),
    on(
      StateLibInvitationApiActions.acceptInvitationSuccess,
      (state, action): InvitationState => {
        const acceptedInvitation: Invitation = action.result.successful[0];
        return {
          ...state,
          invitations: state.invitations.filter(
            (invitation) => invitation.id !== acceptedInvitation.id,
          ),
          acceptedInvitations: action.result,
        };
      },
    ),
    on(
      StateLibInvitationPageActions.clearAcceptedInvitations,
      (state): InvitationState => {
        return {
          ...state,
          acceptedInvitations: initialInvitationState.acceptedInvitations,
        };
      },
    ),
    on(
      StateLibInvitationPageActions.setBusinessCaseIdFromUrl,
      (state, action): InvitationState => {
        return {
          ...state,
          businessCaseIdFromUrl: action.businessCaseIdFromUrl,
        };
      },
    ),
  );
