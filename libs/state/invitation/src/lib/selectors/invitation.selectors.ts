import { InvitationStatus } from '@fincloud/types/enums';
import { InvitationState } from '@fincloud/types/models';
import { createFeatureSelector, createSelector } from '@ngrx/store';

const selectInvitationsState =
  createFeatureSelector<InvitationState>('invitations');

export const selectValidGuestCustomerInvitations = createSelector(
  selectInvitationsState,
  (state) => {
    if (!state?.invitations) {
      return null;
    }
    // These statuses grant a guest customer access to the platform
    return (
      state?.invitations?.filter(
        (i) =>
          i.invitationStatus === InvitationStatus.ACCEPTED ||
          i.invitationStatus === InvitationStatus.DECLINED ||
          i.invitationStatus === InvitationStatus.PENDING,
      ) ?? []
    );
  },
);

export const selectAllCustomerInvitations = createSelector(
  selectInvitationsState,
  (state) => {
    return state.invitations;
  },
);

export const selectAcceptedInvitations = createSelector(
  selectInvitationsState,
  (state) => state.acceptedInvitations,
);

export const selectSuccessfulAcceptedInvitations = createSelector(
  selectAcceptedInvitations,
  (invitations) => invitations.successful,
);

export const selectFailedAcceptedInvitations = createSelector(
  selectAcceptedInvitations,
  (invitations) => invitations.failed,
);

export const selectBusinessCaseIdFromUrl = createSelector(
  selectInvitationsState,
  (state) => state.businessCaseIdFromUrl,
);
