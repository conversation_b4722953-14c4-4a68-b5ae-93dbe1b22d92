import { LoginState } from '@fincloud/types/models';
import { createFeatureSelector, createSelector } from '@ngrx/store';

export const selectLoginState = createFeatureSelector<LoginState>('login');

export const selectEmail = createSelector(
  selectLoginState,
  (state) => state.email,
);

export const selectEmailOnErrorPage = createSelector(
  selectLoginState,
  (state) => state.emailOnErrorPage,
);

export const selectUser = createSelector(
  selectLoginState,
  (state) => state.user,
);

export const selectPublicUserId = createSelector(
  selectLoginState,
  (state) => state?.user?.id,
);

export const selectUserPublicProfileId = createSelector(
  selectLoginState,
  (state) => state.user?.id,
);

export const selectLoginFlowLocale = createSelector(
  selectLoginState,
  (state) => state.locale,
);

export const selectCustomerData = createSelector(selectLoginState, (state) => ({
  organization: state?.customers?.[state?.selectedCustomerKey]?.name ?? '',
  image: state?.customers?.[state?.selectedCustomerKey]?.imageUrl || '',
}));
