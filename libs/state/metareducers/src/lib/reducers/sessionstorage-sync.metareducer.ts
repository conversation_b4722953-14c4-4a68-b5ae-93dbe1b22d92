import { ActionReducer } from '@ngrx/store';
import { localStorageSync } from 'ngrx-store-localstorage';

export function sessionStorageSyncReducer(
  reducer: ActionReducer<unknown>,
): ActionReducer<unknown> {
  return localStorageSync({
    keys: [
      {
        neoGptChat: ['isChatRatingSeen'],
      },
      {
        nextFolder: ['businessCaseId'],
      },
    ],
    rehydrate: true,
    storage: sessionStorage,
    storageKeySerializer: (key) => `neoshare-store-${key}`,
  })(reducer);
}
