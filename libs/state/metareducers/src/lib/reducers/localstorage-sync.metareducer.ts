import { ActionReducer } from '@ngrx/store';
import { localStorageSync } from 'ngrx-store-localstorage';

export function localStorageSyncReducer(
  reducer: ActionReducer<any>,
): ActionReducer<any> {
  return (state, action) => {
    return localStorageSync({
      keys: [
        'login',
        {
          'account-management': [
            'viewMode',
            'selectedRegularCustomersSorting',
            'selectedGuestCustomersSorting',
            'selectedActiveUsersSorting',
            'selectedInactiveUsersSorting',
            'customerFilters',
            'userFilters',
            'customerStatus',
            'userState',
          ],
          neoGptChat: [
            'isChatBtnVisible',
            'isChatVisible',
            'isDocumentStatusVisible',
          ],
        },
      ],
      rehydrate: true,
      storageKeySerializer: (key) => `neoshare-store-${key}`,
    })(reducer)(state, action);
  };
}
