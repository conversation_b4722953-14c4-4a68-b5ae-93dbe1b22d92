import { FinTableColumn } from '@fincloud/ui/table';

export const revisionsTableColumnsConfig = (): FinTableColumn[] => [
  {
    prop: 'date',
    name: $localize`:@@dashboard.businessCase.collaboration.applications.table.columns.startedOn:Datum`,
    isSortable: true,
    templateName: 'date',
    flexGrow: 1,
  },
  {
    prop: 'time',
    name: $localize`:@@facilityField.revisionsTable.columns.hours:Uhrz<PERSON>`,
    isSortable: true,
    templateName: 'timeInDay',
    flexGrow: 1,
  },
  {
    prop: 'changedFrom',
    name: $localize`:@@contractManagement.template.templateCard.updatedBy:<PERSON><PERSON><PERSON><PERSON><PERSON> von`,
    isSortable: true,
    templateName: 'changedFrom',
    flexGrow: 2,
  },
  {
    prop: 'action',
    name: '',
    templateName: 'action',
    isSortable: false,
    flexGrow: 3,
  },
];
