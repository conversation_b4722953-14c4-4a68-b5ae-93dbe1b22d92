import { FieldDto } from '@fincloud/swagger-generator/business-case-manager';
import {
  BusinessCaseGroup,
  BusinessCaseInformation,
} from '@fincloud/swagger-generator/exchange';

export interface FieldModalInformation {
  information?: BusinessCaseInformation;
  field?: FieldDto;
  orderedGroups: BusinessCaseGroup[];
  fieldGroup?: BusinessCaseGroup;
  existingChatId?: string;
  canSeeRevisionsTab?: boolean;
  isMirrored?: boolean;
  isLead?: boolean;
  isFieldEditable?: boolean;
}
