import {
  AbstractControl,
  AsyncValidatorFn,
  ValidationErrors,
} from '@angular/forms';
import { selectBusinessCaseInformation } from '@fincloud/state/business-case';
import { folderStructureFeature } from '@fincloud/state/folder-structure';
import { InformationRecord } from '@fincloud/swagger-generator/exchange';
import { Store } from '@ngrx/store';
import { Observable, combineLatest, of } from 'rxjs';
import { map, take } from 'rxjs/operators';

export const documentNameUniqueValidator = (
  store: Store,
  groupKey: string,
  originalValue: string,
): AsyncValidatorFn => {
  return (control: AbstractControl): Observable<ValidationErrors | null> => {
    const newValue = control.value?.toLocaleLowerCase();
    if (!newValue || newValue === originalValue.toLocaleLowerCase()) {
      return of(null);
    }

    return combineLatest([
      store.select(folderStructureFeature.selectCurrentFolderPerGroup),
      store.select(selectBusinessCaseInformation),
    ]).pipe(
      take(1),
      map(([currentFolderPerGroup, businessCaseInformation]) => {
        const documentFields = currentFolderPerGroup?.[groupKey]?.fields || [];
        const isDuplicate = documentFields.some(
          (documentKey) =>
            businessCaseInformation[
              documentKey as keyof InformationRecord
            ]?.field?.label?.toLocaleLowerCase() === newValue,
        );

        return isDuplicate ? { documentNameNotUnique: true } : null;
      }),
    );
  };
};
