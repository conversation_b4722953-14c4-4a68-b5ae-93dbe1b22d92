import { AppState } from '@fincloud/types/models';

import { inject } from '@angular/core';
import { GuardResult, MaybeAsync, Router } from '@angular/router';
import {
  selectUser,
  selectUserHasBeenPresentedTerms,
} from '@fincloud/state/user';
import { User } from '@fincloud/swagger-generator/authorization-server';
import { Store } from '@ngrx/store';
import { isEmpty } from 'lodash-es';
import { Observable, filter, map, switchMap } from 'rxjs';

function waitForData$(store: Store<AppState>): Observable<User> {
  return store.select(selectUser).pipe(filter((user) => !isEmpty(user)));
}

export function alreadyAcceptedTermsAndConditionsGuard(): MaybeAsync<GuardResult> {
  const router = inject(Router);
  const store = inject(Store);

  return waitForData$(store).pipe(
    switchMap(() => store.select(selectUserHasBeenPresentedTerms)),
    map((hasBeenPresented) => {
      return hasBeenPresented ? router.createUrlTree(['/']) : true;
    }),
  );
}
