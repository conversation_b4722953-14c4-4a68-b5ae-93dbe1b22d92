import { inject } from '@angular/core';
import { GuardResult, MaybeAsync, Router } from '@angular/router';
import {
  StateLibEnvironmentPageActions,
  selectIsDemoEnvironment,
  selectIsDemoEnvironmentChecked,
} from '@fincloud/state/environment';
import { AppState } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { filter, map, switchMap, tap } from 'rxjs';

export function demoEnvironmentGuard(): MaybeAsync<GuardResult> {
  const router = inject(Router);
  const store = inject(Store<AppState>);

  return store.select(selectIsDemoEnvironmentChecked).pipe(
    tap((checked) => {
      if (!checked) {
        store.dispatch(StateLibEnvironmentPageActions.checkIsDemoEnvironment());
      }
    }),
    filter((checked) => checked),
    switchMap(() => store.select(selectIsDemoEnvironment)),
    map((isDemoEnvironment) => {
      if (!isDemoEnvironment) {
        return router.createUrlTree(['/']);
      }

      return true;
    }),
  );
}
