import { Location } from '@angular/common';
import { inject } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  GuardResult,
  MaybeAsync,
  Router,
} from '@angular/router';
import {
  LOGIN_REDIRECT_URL,
  SESSION_CUSTOMER_KEY,
  SESSION_STORAGE_UNIQUE_INSTANCE_ID,
  TokenManagementService,
} from '@fincloud/core/auth';
import { StateLibLoginPageActions } from '@fincloud/state/login';
import { selectRouteCustomerKey } from '@fincloud/state/router';
import { selectUser, selectUserPermissionsCodes } from '@fincloud/state/user';
import { AppState } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { isEmpty } from 'lodash-es';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import {
  catchError,
  combineLatest,
  filter,
  map,
  of,
  switchMap,
  tap,
} from 'rxjs';

function waitForData$(store: Store<AppState>) {
  return combineLatest([
    store.select(selectUser),
    store.select(selectUserPermissionsCodes),
    store.select(selectRouteCustomerKey),
  ]).pipe(
    filter(([user, codes]) => {
      return !!user && !isEmpty(codes);
    }),
  );
}

export function authGuard(
  route: ActivatedRouteSnapshot,
): MaybeAsync<GuardResult> {
  const router = inject(Router);
  const store = inject(Store<AppState>);
  const sessionStorageService = inject(SessionStorageService);
  const localStorageService = inject(LocalStorageService);
  const location = inject(Location);
  const tokenManagementService = inject(TokenManagementService);
  const routeCustomerKey = route.paramMap.get('customerKey');
  const sessionCustomerKey =
    sessionStorageService.retrieve(SESSION_CUSTOMER_KEY);

  if (sessionCustomerKey && routeCustomerKey !== sessionCustomerKey) {
    return router.createUrlTree(['/', sessionCustomerKey]);
  }

  const rawToken = tokenManagementService.getToken(routeCustomerKey)?.tokenRaw;

  return of(rawToken).pipe(
    map((token) => {
      if (!token) {
        localStorageService.store(LOGIN_REDIRECT_URL, location.path());

        router.navigate(['/', 'login']);

        return false;
      }

      return true;
    }),
    switchMap(() =>
      waitForData$(store).pipe(
        tap(([user, codes, customerKey]) => {
          const openedAngularInstances =
            tokenManagementService.getToken(
              customerKey,
            )?.openedAngularInstances;

          const uniqueInstanceId = sessionStorageService.retrieve(
            SESSION_STORAGE_UNIQUE_INSTANCE_ID,
          );

          const doesAllSessionsInactive = Object.values(
            openedAngularInstances ?? {},
          ).every((value) => value === 'inactive');

          if (
            !openedAngularInstances[uniqueInstanceId] &&
            doesAllSessionsInactive &&
            Object.keys(openedAngularInstances ?? {}).length > 0
          ) {
            store.dispatch(StateLibLoginPageActions.logout());
          }
        }),
        switchMap(() => {
          return of(true);
        }),
        catchError(() => of(false)),
      ),
    ),
  );
}
