import { inject } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  GuardResult,
  MaybeAsync,
  Router,
} from '@angular/router';
import { Store } from '@ngrx/store';

import { StateLibCompanyPageActions } from '@fincloud/state/company-analysis';
import { selectUserCustomerKey } from '@fincloud/state/user';
import { CompanyControllerService } from '@fincloud/swagger-generator/company';
import { AppState } from '@fincloud/types/models';
import { combineLatest, map } from 'rxjs';

export function companyGuard(
  route: ActivatedRouteSnapshot,
): MaybeAsync<GuardResult> {
  const router = inject(Router);
  const store = inject(Store<AppState>);
  const companyControllerService = inject(CompanyControllerService);

  const companyId = route.params.id as string;

  store.dispatch(
    StateLibCompanyPageActions.loadCompany({ payload: companyId }),
  );

  return combineLatest([
    companyControllerService.getCompanyById({
      id: companyId,
    }),
    store.select(selectUserCustomerKey),
  ]).pipe(
    map(([company, customerKey]) => {
      if (!company) {
        return void router.navigate([customerKey, 'company-management']);
      }
      return true;
    }),
  );
}
