import { inject } from '@angular/core';
import { GuardResult, MaybeAsync, Router } from '@angular/router';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';

import {
  LOCAL_STORAGE_USER_PERMISSIONS_KEY,
  SESSION_CUSTOMER_KEY,
} from '@fincloud/core/auth';
import { selectRouteCustomerKey } from '@fincloud/state/router';
import { Permission, UserRole } from '@fincloud/types/enums';
import { Store } from '@ngrx/store';
import { map } from 'rxjs';

function getDefaultRedirectUrl(permissionsCodes: string[]): string {
  if (permissionsCodes.includes(Permission.PERM_0059)) {
    if (
      permissionsCodes.includes(UserRole.PLATFORM_USER) &&
      !permissionsCodes.includes(UserRole.PLATFORM_MANAGER)
    ) {
      return 'cases';
    } else {
      return 'dashboard';
    }
  }

  if (
    permissionsCodes.some((permission) =>
      [Permission.PERM_0001, Permission.PERM_0002].includes(
        permission as Permission,
      ),
    )
  ) {
    return 'cases';
  }

  if (permissionsCodes.includes(UserRole.ACCOUNT_MANAGER)) {
    return 'account-management';
  }
  if (permissionsCodes.includes(UserRole.LEGAL_OFFICER)) {
    return 'contract-management';
  }
  if (permissionsCodes.includes(UserRole.USAGE_CONTRACT_SIGNER)) {
    return 'digital-signature';
  }

  return 'digital-signature';
}

export function dashboardRedirectGuard(): MaybeAsync<GuardResult> {
  const router = inject(Router);
  const localStorageService = inject(LocalStorageService);
  const sessionStorageService = inject(SessionStorageService);
  const store = inject(Store);

  const permissionsMap = localStorageService.retrieve(
    LOCAL_STORAGE_USER_PERMISSIONS_KEY,
  );

  return store.select(selectRouteCustomerKey).pipe(
    map((customerKey) => {
      const userPermissions = permissionsMap[customerKey];
      const defaultRedirectUrl = getDefaultRedirectUrl(userPermissions);

      if (!defaultRedirectUrl) {
        return true;
      }

      return router.createUrlTree([
        '/',
        customerKey ?? sessionStorageService.retrieve(SESSION_CUSTOMER_KEY),
        defaultRedirectUrl,
      ]);
    }),
  );
}
