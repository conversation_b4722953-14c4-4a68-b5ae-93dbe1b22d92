import {
  Component,
  DestroyRef,
  Inject,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  AbstractControl,
  FormGroup,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { FileService } from '@fincloud/core/files';
import { emailValidator } from '@fincloud/core/form';
import { Toast } from '@fincloud/core/toast';
import {
  StateLibInvitationsPageActions,
  selectIsCollaborationsInvitationsModalLoading,
  selectUsersById,
} from '@fincloud/state/business-case';
import { StateLibContractsPageActions } from '@fincloud/state/contracts';
import { selectCustomer } from '@fincloud/state/customer';
import { UserData } from '@fincloud/swagger-generator/application';
import {
  Customer,
  User,
  UserManagementControllerService,
} from '@fincloud/swagger-generator/authorization-server';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import {
  CustomerStatus,
  CustomerType,
  FinancingStructureType,
  InvitationFlowStrategy,
  InvitationStatus,
  ParticipationType,
} from '@fincloud/types/enums';
import {
  ApplicationOrInvitationInfo,
  UserInfoExtended,
} from '@fincloud/types/models';
import { FinButtonAppearance } from '@fincloud/ui/button';
import {
  FIN_MODAL_DATA,
  FIN_MODAL_DEFAULT_OPTIONS,
  FIN_MODAL_REF,
  FIN_MODAL_REF_PROVIDER,
  FinModalRef,
  FinModalService,
} from '@fincloud/ui/modal';
import { FinToastService } from '@fincloud/ui/toast';
import { FinSize } from '@fincloud/ui/types';
import { EMPTY_OBJECT, PARTICIPATION_TYPE_LABEL } from '@fincloud/utils';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { EMPTY, catchError, finalize, shareReplay, tap } from 'rxjs';
import { InvitationStep } from '../../enums/invitation-step';
import { NdaOptionValue } from '../../enums/nda-option-values';
import { ApplicationsInvitationsHelperService } from '../../services/applicatons-invitations-helper.service';
import { alreadyAddedUserValidator } from '../../utils/already-added-user-validator';
import { INVITED_USERS_WITHOUT_PERMISSIONS_ERROR } from '../../utils/invited-users-without-permissions-error';
import { noAddedSignersFromBothOrganizationsValidator } from '../../utils/no-added-signers-from-both-organizations-validator';
import { setModalHeader } from '../../utils/set-modal-header';
import { setNextStepButtonText } from '../../utils/set-next-step-button-text';

@Component({
  selector: 'ui-collaboration-invitation-modal',
  templateUrl: './collaboration-invitation-modal.component.html',
  styleUrls: ['./collaboration-invitation-modal.component.scss'],
  providers: [
    ApplicationsInvitationsHelperService,
    FIN_MODAL_REF_PROVIDER,
    FinModalService,
    { provide: FIN_MODAL_DEFAULT_OPTIONS, useValue: {} },
  ],
})
export class CollaborationInvitationModalComponent
  implements OnInit, OnDestroy
{
  readonly isLoading$ = this.store
    .select(selectIsCollaborationsInvitationsModalLoading)
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));

  readonly invitationStep = InvitationStep;
  readonly finButtonAppearance = FinButtonAppearance;
  readonly finSize = FinSize;

  currentUploadedFile: File;
  selectedUsers: UserInfoExtended[] = [];
  currentInvitationStep = InvitationStep.SELECT_ORGANISATION;
  ndaSignersInvitingUsers: UserInfoExtended[] = [];
  ndaSignersInvitedUsers: UserInfoExtended[] = [];

  currentCustomer: Customer;

  ndaOption: NdaOptionValue;
  mostUsedEmailDomain = '';
  invitationModalForms = new FormGroup({});
  showGuestFirstAndLastNameControls = false;
  readonly financingStructureType = FinancingStructureType;

  private customer$ = this.store.select(selectCustomer);
  private _hasCustomerPreselected = false;
  private invitationFlowStrategy: InvitationFlowStrategy = null;

  constructor(
    private destroyRef: DestroyRef,
    private store: Store,
    private finToastService: FinToastService,
    private applicationInvitationsHelperService: ApplicationsInvitationsHelperService,
    private userManagementControllerService: UserManagementControllerService,
    private fileService: FileService,
    @Inject(FIN_MODAL_REF)
    private finModalRef: FinModalRef<CollaborationInvitationModalComponent>,
    @Inject(FIN_MODAL_DATA)
    public invitationModalData: {
      businessCase: ExchangeBusinessCase;
      isOpenViaKebabMenu: boolean;
      addUsersForAcceptedGuest: boolean;
      isOpenViaInvitedOrganization?: boolean;
      applicationOrInvitationInfo?: ApplicationOrInvitationInfo;
      alreadyAddedOrganisation?: ApplicationOrInvitationInfo[];
    },
  ) {}

  get isRegular() {
    return (
      this.invitationModalData.applicationOrInvitationInfo &&
      this.invitationModalData.applicationOrInvitationInfo.customerStatus ===
        CustomerStatus.REGULAR
    );
  }

  get secondaryFooterButtonText() {
    const isBackButton =
      (!this._hasCustomerPreselected &&
        this.currentInvitationStep !== InvitationStep.SELECT_ORGANISATION) ||
      (this._hasCustomerPreselected &&
        this.currentInvitationStep === InvitationStep.PREVIEW);

    return isBackButton
      ? $localize`:@@contract.create.modal.backButton:Zurück`
      : $localize`:@@contract.create.modal.closeButton:Schließen`;
  }

  get isNDAOptionSelected() {
    return this.ndaOption === NdaOptionValue.WITH;
  }

  get primaryFooterButtonText() {
    return setNextStepButtonText(this.currentInvitationStep);
  }

  get getModalHeaderText() {
    return setModalHeader(this.currentInvitationStep);
  }

  get invitationForms() {
    const {
      selectCustomerAndNdaForm: selectCustomerAndNdaForm,
      uploadNdaDocumentForm: uploadNdaDocumentForm,
      regularUsersForm: regularUsersForm,
      guestUsersForm: guestUsersForm,
    } = (this.invitationModalForms as FormGroup).controls;
    return {
      selectCustomerAndNdaForm,
      uploadNdaDocumentForm,
      regularUsersForm,
      guestUsersForm,
    };
  }

  ngOnInit() {
    this.store.dispatch(StateLibContractsPageActions.loadBrands());
    if (this.invitationModalData.applicationOrInvitationInfo?.customerName) {
      this._hasCustomerPreselected = true;
      this.onSetCustomerDomain()
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe();
      this.setSelectedUsers()
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe();
      this.currentInvitationStep = InvitationStep.ADD_USERS;
    }
    this.customer$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((customer) => (this.currentCustomer = customer));
  }

  ngOnDestroy() {
    this.store.dispatch(
      StateLibInvitationsPageActions.setCollaborationsInvitationsModalLoading({
        payload: false,
      }),
    );
  }

  private onSetCustomerDomain() {
    return this.applicationInvitationsHelperService
      .checkCustomerDomain(
        this.invitationModalData.applicationOrInvitationInfo.customerKey,
      )
      .pipe(
        tap(
          (response) => (this.mostUsedEmailDomain = `@${response['domain']}`),
        ),
      );
  }

  private submitInvitationModalForms() {
    const formValidations = {
      guestUsersForm: (guestForm: FormGroup) => {
        this.validateGuestUsersForm(guestForm);
        this.nextStep();
      },
      regularUsersForm: () => this.nextStep(),
      selectCustomerAndNdaForm:
        this.validateSelectCustomerAndNdaForm.bind(this),
      uploadNdaDocumentForm: () => {
        this.validateUploadNdaForm();
        this.nextStep();
      },
    };

    for (const [formName, validationFn] of Object.entries(formValidations)) {
      const form = this.invitationModalForms.get(formName);
      if (
        this.invitationForms.selectCustomerAndNdaForm &&
        this.invitationForms.uploadNdaDocumentForm
      ) {
        this.validateSelectCustomerAndNdaForm(
          this.invitationForms.selectCustomerAndNdaForm as FormGroup,
        );
        return;
      }
      if (form) {
        validationFn(form as FormGroup);
      }
    }
  }

  private validateSelectCustomerAndNdaForm(
    selectCustomerAndNdaForm: FormGroup,
  ) {
    const {
      ndaOption: ndaOptionControl,
      customer: customerControl,
      participationType: participationTypeControl,
      invitationFlowStrategy: invitationFlowStrategyControl,
    } = selectCustomerAndNdaForm.controls;
    const hasAccessToNda =
      this.currentCustomer.key ===
      this.invitationModalData.businessCase.leadCustomerKey;

    if (!hasAccessToNda) {
      ndaOptionControl.setValue('without');
    }

    if (!selectCustomerAndNdaForm.valid) {
      selectCustomerAndNdaForm.markAllAsTouched();
      Object.values(selectCustomerAndNdaForm.controls).forEach((control) =>
        control.updateValueAndValidity({ onlySelf: true }),
      );
      return;
    }

    if (customerControl.value.customerType !== CustomerType.BANK) {
      invitationFlowStrategyControl.clearValidators();
      invitationFlowStrategyControl.updateValueAndValidity();
    } else {
      invitationFlowStrategyControl.markAsDirty();
    }

    if (selectCustomerAndNdaForm.valid) {
      this.invitationModalData.applicationOrInvitationInfo = {
        ...customerControl.value,
        participationType: participationTypeControl.value,
        invitationType:
          invitationFlowStrategyControl?.value ??
          InvitationFlowStrategy.INVITE_TO_JOIN_STRATEGY,
      };
      if (
        this.invitationModalData.applicationOrInvitationInfo.customerStatus ===
        CustomerStatus.REGULAR
      ) {
        this.onSetCustomerDomain().subscribe();
      }
      this.nextStep();
    }
  }

  validateUploadNdaForm(isAddingUser = false) {
    const uploadNdaDocumentForm = this.invitationForms
      .uploadNdaDocumentForm as FormGroup;

    const {
      ndaDocument: ndaDocumentControl,
      signers: signersControl,
      userAdditionalFieldsForm,
    } = uploadNdaDocumentForm.controls;

    const {
      firstName: firstNameControl,
      lastName: lastNameControl,
      salutation: salutationControl,
      academicTitle: academicTitleControl,
      position: positionControl,
    } = (userAdditionalFieldsForm as FormGroup).controls;

    if (!this.showGuestFirstAndLastNameControls) {
      signersControl.setValidators([
        noAddedSignersFromBothOrganizationsValidator(
          this.ndaSignersInvitedUsers,
          this.ndaSignersInvitingUsers,
        ),
      ]);

      signersControl.markAsTouched();
      signersControl.updateValueAndValidity();
    }

    if (!ndaDocumentControl.value && !isAddingUser) {
      ndaDocumentControl.markAsTouched();
      this.controlAddValidator(ndaDocumentControl, Validators.required);
    }

    if (this.showGuestFirstAndLastNameControls) {
      this.controlAddValidator(firstNameControl, Validators.required);
      this.controlAddValidator(lastNameControl, Validators.required);
    }

    if (!userAdditionalFieldsForm.valid) {
      userAdditionalFieldsForm.markAllAsTouched();
    }

    if (
      uploadNdaDocumentForm.valid &&
      userAdditionalFieldsForm.valid &&
      signersControl.value &&
      this.showGuestFirstAndLastNameControls
    ) {
      const user = {
        firstName: firstNameControl.value,
        lastName: lastNameControl.value,
        username: signersControl.value,
        attributes: {
          salutation: salutationControl.value,
          academicTitle: academicTitleControl.value,
          position: positionControl.value,
        },
      };

      this.onAddSigner(user);

      signersControl.reset();
      userAdditionalFieldsForm.reset();
      this.controlRemoveValidator(firstNameControl, Validators.required);
      this.controlRemoveValidator(lastNameControl, Validators.required);
      this.showGuestFirstAndLastNameControls = false;
    }
  }

  public triggerNextStepAction() {
    if (this.currentInvitationStep === InvitationStep.PREVIEW) {
      this.inviteSelectedOrganization();
    } else {
      this.submitInvitationModalForms();
    }
  }

  private validateGuestUsersForm(guestUsersForm: FormGroup) {
    const { email: emailControl, userAdditionalFieldsForm } =
      guestUsersForm.controls;

    const {
      firstName: firstNameControl,
      lastName: lastNameControl,
      salutation: salutationControl,
      position: positionControl,
      academicTitle: academicTitleControl,
    } = (userAdditionalFieldsForm as FormGroup).controls;

    emailControl.setValue(emailControl.value?.trim());
    const hasNewAddedUsers = this.selectedUsers.filter(
      (user) => user.canBeRemoved,
    );

    if (!hasNewAddedUsers.length) {
      emailControl.setValidators([
        Validators.required,
        emailValidator(),
        alreadyAddedUserValidator(this.selectedUsers),
      ]);

      emailControl.markAsTouched();
      emailControl.updateValueAndValidity();
    }

    if (this.showGuestFirstAndLastNameControls) {
      this.controlAddValidator(firstNameControl, Validators.required);
      this.controlAddValidator(lastNameControl, Validators.required);
      userAdditionalFieldsForm.markAllAsTouched();
    }

    if (
      emailControl.value &&
      emailControl.valid &&
      !this.showGuestFirstAndLastNameControls
    ) {
      this.userManagementControllerService
        .getUserByUsername({
          username: emailControl.value,
          customerKey:
            this.invitationModalData.applicationOrInvitationInfo.customerKey,
        })
        .pipe(
          catchError(() => {
            this.showGuestFirstAndLastNameControls = true;
            return EMPTY;
          }),
        )
        .subscribe((user) => {
          firstNameControl.setValue(user.firstName);
          lastNameControl.setValue(user.lastName);
          salutationControl.setValue(user.attributes.salutation);
          positionControl.setValue(user.attributes.position);
          academicTitleControl.setValue(user.attributes.academicTitle);
          this.addGuestUser(guestUsersForm);
        });
    }

    if (guestUsersForm.valid && this.showGuestFirstAndLastNameControls) {
      this.addGuestUser(guestUsersForm);
    }
  }

  private addGuestUser(guestUsersForm: FormGroup) {
    const { email: emailControl, userAdditionalFieldsForm } =
      guestUsersForm.controls;

    const {
      firstName: firstNameControl,
      lastName: lastNameControl,
      salutation: salutationControl,
      position: positionControl,
      academicTitle: academicTitleControl,
    } = (userAdditionalFieldsForm as FormGroup).controls;

    this.selectedUsers = [
      ...this.selectedUsers,
      {
        firstName: firstNameControl.value,
        lastName: lastNameControl.value,
        username: emailControl.value,
        attributes: {
          salutation: salutationControl.value,
          academicTitle: academicTitleControl.value,
          position: positionControl.value,
        },
        canBeRemoved: true,
      },
    ];
    guestUsersForm.reset();
    userAdditionalFieldsForm.reset();
    guestUsersForm.updateValueAndValidity();
    this.controlRemoveValidator(emailControl, Validators.required);
    this.controlRemoveValidator(firstNameControl, Validators.required);
    this.controlRemoveValidator(lastNameControl, Validators.required);
    this.showGuestFirstAndLastNameControls = false;
  }

  private validateRegularUsersForm(regularUsersForm: AbstractControl) {
    const { email: emailControl } = (regularUsersForm as FormGroup).controls;

    if (!emailControl.valid) {
      return emailControl.updateValueAndValidity();
    }

    this.userManagementControllerService
      .getUserByUsername({
        customerKey:
          this.invitationModalData.applicationOrInvitationInfo.customerKey,
        username: emailControl.value + this.mostUsedEmailDomain,
      })
      .pipe(
        catchError(() => {
          emailControl.setErrors({ noFoundUser: true });
          emailControl.markAsTouched();
          return EMPTY;
        }),
        tap((res) => {
          emailControl.setValidators([
            alreadyAddedUserValidator(
              this.selectedUsers,
              emailControl.value + this.mostUsedEmailDomain,
            ),
          ]);
          emailControl.markAsTouched();
          if (regularUsersForm.valid) {
            this.selectedUsers = [
              ...this.selectedUsers,
              { ...res, canBeRemoved: true },
            ];
            regularUsersForm.reset();
          }
        }),
      )
      .subscribe();
  }

  public addGuestOrRegularUser() {
    if (this.invitationForms.guestUsersForm) {
      this.validateGuestUsersForm(
        this.invitationForms.guestUsersForm as FormGroup,
      );
    } else {
      this.validateRegularUsersForm(this.invitationForms.regularUsersForm);
    }
  }

  private setSelectedUsers() {
    return this.applicationInvitationsHelperService
      .onLoadSelectedUsers(this.invitationModalData.applicationOrInvitationInfo)
      .pipe(
        concatLatestFrom(() => [this.store.select(selectUsersById)]),
        tap(([users, unsortedUsersInCase]) => {
          if (this.invitationModalData.addUsersForAcceptedGuest) {
            const usersValues = Object.values(unsortedUsersInCase);

            this.selectedUsers = usersValues.filter(
              (user) =>
                user.customerKey ===
                this.invitationModalData.applicationOrInvitationInfo
                  .customerKey,
            );

            return;
          }

          this.selectedUsers = users;
        }),
        takeUntilDestroyed(this.destroyRef),
      );
  }

  public onAddSigner(signer: User) {
    if (
      !signer.id ||
      this.invitationModalData.applicationOrInvitationInfo.customerKey ===
        signer.customerKey
    ) {
      this.ndaSignersInvitedUsers = [
        ...this.ndaSignersInvitedUsers,
        {
          ...signer,
          canBeRemoved: true,
          customerKey:
            this.invitationModalData.applicationOrInvitationInfo.customerKey,
        },
      ];
    } else {
      this.ndaSignersInvitingUsers = [
        ...this.ndaSignersInvitingUsers,
        { ...signer, canBeRemoved: true },
      ];
    }
  }

  private controlAddValidator(
    control: AbstractControl,
    validator: ValidatorFn,
  ) {
    control.setValidators(validator);
    control.updateValueAndValidity();
  }

  private controlRemoveValidator(
    control: AbstractControl,
    validator: ValidatorFn,
  ) {
    control.removeValidators(validator);
    control.updateValueAndValidity();
  }

  public downloadNdaFile() {
    this.fileService.downloadFile(this.currentUploadedFile);
  }

  public onRemoveUser(userEmail: string) {
    this.selectedUsers = this.selectedUsers.filter(
      (u) => u.username !== userEmail,
    );
  }

  public onRemoveSigner(userEmail: string) {
    this.ndaSignersInvitedUsers = this.ndaSignersInvitedUsers.filter(
      (u) => u.username !== userEmail,
    );

    this.ndaSignersInvitingUsers = this.ndaSignersInvitingUsers.filter(
      (u) => u.username !== userEmail,
    );
  }

  private nextStep() {
    const stepHandlers = {
      [InvitationStep.SELECT_ORGANISATION]: () => {
        const { ndaOption: ndaOptionControl } = (
          this.invitationForms.selectCustomerAndNdaForm as FormGroup
        ).controls;
        this.ndaOption =
          ndaOptionControl.value === 'with'
            ? NdaOptionValue.WITH
            : NdaOptionValue.WITHOUT;

        this.currentInvitationStep = this.isNDAOptionSelected
          ? InvitationStep.ATTACH_NDA
          : InvitationStep.ADD_USERS;

        return this.invitationForms.selectCustomerAndNdaForm.valid;
      },

      [InvitationStep.ADD_USERS]: () => {
        const hasNewAddedUsers = this.selectedUsers.filter(
          (user) => user.canBeRemoved,
        );

        if (
          this.invitationModalData.applicationOrInvitationInfo
            .customerStatus === CustomerStatus.GUEST &&
          !hasNewAddedUsers.length
        ) {
          return this.invitationForms.guestUsersForm.valid;
        } else {
          this.showGuestFirstAndLastNameControls = false;
        }

        if (
          this.invitationModalData.isOpenViaKebabMenu &&
          this.invitationModalData.applicationOrInvitationInfo
            .customerStatus === CustomerStatus.REGULAR &&
          !hasNewAddedUsers.length
        ) {
          const { email: emailControl } = (
            this.invitationForms.regularUsersForm as FormGroup
          ).controls;
          emailControl.setValidators(Validators.required);
          emailControl.markAsTouched();
          emailControl.updateValueAndValidity();
          return this.invitationForms.regularUsersForm.valid;
        }

        this.currentInvitationStep = InvitationStep.PREVIEW;
        return true;
      },

      [InvitationStep.ATTACH_NDA]: () => {
        const { userAdditionalFieldsForm } = (
          this.invitationForms.uploadNdaDocumentForm as FormGroup
        ).controls;

        if (
          this.invitationForms.uploadNdaDocumentForm.valid &&
          userAdditionalFieldsForm.valid
        ) {
          this.currentInvitationStep = InvitationStep.PREVIEW;
        }
      },

      [InvitationStep.PREVIEW]: () => {
        return true;
      },
    };

    return stepHandlers[this.currentInvitationStep]
      ? stepHandlers[this.currentInvitationStep]()
      : false;
  }

  public goStepBack() {
    const isCurrentStepAddUsersOrAttachNDA =
      this.currentInvitationStep === InvitationStep.ADD_USERS ||
      this.currentInvitationStep === InvitationStep.ATTACH_NDA;

    if (this.currentInvitationStep === InvitationStep.SELECT_ORGANISATION) {
      return this.finModalRef.close();
    }

    if (
      isCurrentStepAddUsersOrAttachNDA &&
      this.invitationModalData.isOpenViaKebabMenu
    ) {
      return this.finModalRef.close();
    }

    const stepHandlers = {
      [InvitationStep.SELECT_ORGANISATION]: () => {
        this.currentInvitationStep = InvitationStep.SELECT_ORGANISATION;
        return;
      },
      [InvitationStep.ADD_USERS]: () => {
        this.currentInvitationStep = InvitationStep.SELECT_ORGANISATION;
        this.showGuestFirstAndLastNameControls = false;
      },
      [InvitationStep.ATTACH_NDA]: () => {
        this.currentInvitationStep = InvitationStep.SELECT_ORGANISATION;
      },
      [InvitationStep.PREVIEW]: () => {
        this.currentInvitationStep = this.isNDAOptionSelected
          ? InvitationStep.ATTACH_NDA
          : InvitationStep.ADD_USERS;
      },
    };

    stepHandlers[this.currentInvitationStep]();
  }

  private inviteSelectedOrganization() {
    this.store.dispatch(
      StateLibInvitationsPageActions.setCollaborationsInvitationsModalLoading({
        payload: true,
      }),
    );

    const newAddedUsers = this.selectedUsers.length
      ? this.selectedUsers.filter((u) => !!u.canBeRemoved)
      : [];

    const invitedUsers = newAddedUsers.length
      ? newAddedUsers.map((user) => {
          const { firstName, lastName, username, id, attributes } = user;

          if (
            this.invitationModalData.applicationOrInvitationInfo
              .customerStatus === CustomerStatus.REGULAR
          ) {
            return {
              firstName,
              lastName,
              email: username,
              userId: id,
            };
          }

          const { salutation, academicTitle, position } = attributes;
          return {
            firstName,
            lastName,
            email: username,
            attributes: {
              salutation,
              academicTitle,
              position,
            },
          };
        })
      : [];

    if (this.invitationModalData.addUsersForAcceptedGuest) {
      this.store.dispatch(
        StateLibInvitationsPageActions.inviteUsersToGuestCustomer({
          payload: {
            businessCaseId: this.invitationModalData.businessCase.id,
            customerKey:
              this.invitationModalData.applicationOrInvitationInfo.customerName,
            body: invitedUsers as UserData[],
          },
        }),
      );

      return;
    }

    if (
      this.invitationModalData.applicationOrInvitationInfo.invitationStatus ===
      InvitationStatus.PENDING
    ) {
      this.applicationInvitationsHelperService
        .addContactPersons(
          invitedUsers,
          this.invitationModalData.applicationOrInvitationInfo,
          this.invitationModalData.businessCase?.id,
        )
        .pipe(
          tap((res) => this.finModalRef.close({ ...res })),
          finalize(() =>
            this.store.dispatch(
              StateLibInvitationsPageActions.setCollaborationsInvitationsModalLoading(
                { payload: false },
              ),
            ),
          ),
        )
        .subscribe();
    } else {
      this.applicationInvitationsHelperService
        .sendInvitation(
          this.invitationModalData.applicationOrInvitationInfo,
          this.isNDAOptionSelected,
          this.currentUploadedFile,
          this.invitationModalData.businessCase,
          invitedUsers,
          this.ndaSignersInvitedUsers,
          this.ndaSignersInvitingUsers,
          this.currentCustomer.key,
        )
        .pipe(
          catchError((err) => {
            if (err.error.code === 'APP-1067') {
              this.finModalRef.close();
              this.finToastService.show(
                Toast.error(INVITED_USERS_WITHOUT_PERMISSIONS_ERROR),
              );
              return EMPTY;
            }
            this.finModalRef.close();
            this.finToastService.show(Toast.error());
            return EMPTY;
          }),
          tap((res) => {
            const result = [...res.failed, ...res.successful];

            if (result[0].failureReason) {
              this.finToastService.show(Toast.error());
              this.finModalRef.close();
            } else {
              this.finModalRef.close({
                ...result[0].invitation,
              });
            }
          }),
          finalize(() =>
            this.store.dispatch(
              StateLibInvitationsPageActions.setCollaborationsInvitationsModalLoading(
                { payload: false },
              ),
            ),
          ),
        )
        .subscribe();
    }
  }

  public onResetInvitationModalForms() {
    this.selectedUsers = [];
    this.ndaSignersInvitedUsers = [];
    this.ndaSignersInvitingUsers = [];
    this.invitationFlowStrategy =
      InvitationFlowStrategy.INVITE_TO_JOIN_STRATEGY;
    const {
      customer: customerControl,
      participantTypeControl: participationTypeControl,
      invitationFlowStrategy: invitationFlowStrategyControl,
    } = (this.invitationForms.selectCustomerAndNdaForm as FormGroup).controls;
    const customerValue = customerControl.value;
    const participationType = participationTypeControl?.value;
    const invitationFlow = invitationFlowStrategyControl.value;

    this.invitationModalForms.reset(EMPTY_OBJECT, {
      emitEvent: false,
    });

    customerControl.setValue(customerValue);
    participationTypeControl?.setValue(participationType);
    invitationFlowStrategyControl?.setValue(invitationFlow);

    this.invitationModalData.applicationOrInvitationInfo = customerValue;
  }

  public setSelectedCustomer(customer: ApplicationOrInvitationInfo) {
    this.invitationModalData.applicationOrInvitationInfo = customer;
  }

  public setSelectedInvitationType(
    invitationFlowStrategy: InvitationFlowStrategy,
  ) {
    this.invitationFlowStrategy = invitationFlowStrategy;
  }

  public addSelectedUserAsContactPerson(user: UserInfoExtended) {
    this.selectedUsers = [
      ...this.selectedUsers,
      { ...user, canBeRemoved: true },
    ];
  }

  //TODO: sounds like BL logic? UI can hande it for sure
  public getParticipantAvailableType(
    info: ApplicationOrInvitationInfo,
    isRealEstateFinStructureType: boolean,
  ) {
    if (!isRealEstateFinStructureType) {
      return [
        {
          value: ParticipationType.PARTICIPANT,
          label: PARTICIPATION_TYPE_LABEL.PARTICIPANT,
        },
      ];
    }

    switch (info?.customerType) {
      case CustomerType.BANK: {
        return [
          {
            value: ParticipationType.STRUCTURER,
            label: PARTICIPATION_TYPE_LABEL.STRUCTURER,
          },
          {
            value: ParticipationType.PARTICIPANT,
            label: PARTICIPATION_TYPE_LABEL.PARTICIPANT,
          },
        ];
      }
      case CustomerType.REAL_ESTATE: {
        return [
          {
            value: ParticipationType.PARTICIPANT,
            label: PARTICIPATION_TYPE_LABEL.PARTICIPANT,
          },
        ];
      }
      case CustomerType.CORPORATE: {
        return [
          {
            value: ParticipationType.PARTICIPANT,
            label: PARTICIPATION_TYPE_LABEL.PARTICIPANT,
          },
        ];
      }
      case CustomerType.FSP: {
        return [
          {
            value: ParticipationType.STRUCTURER,
            label: PARTICIPATION_TYPE_LABEL.STRUCTURER,
          },
          {
            value: ParticipationType.PARTICIPANT,
            label: PARTICIPATION_TYPE_LABEL.PARTICIPANT,
          },
        ];
      }
      default: {
        // BL need "default" type when case != real estate, lets help them
        return [
          {
            value: ParticipationType.PARTICIPANT,
            label: PARTICIPATION_TYPE_LABEL.PARTICIPANT,
          },
        ];
      }
    }
  }
}
