import {
  Component,
  DestroyRef,
  EventEmitter,
  inject,
  Inject,
  Input,
  LOCALE_ID,
  OnInit,
  Output,
} from '@angular/core';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { INDUSTRIES, SECTORS } from '@fincloud/core/company';
import { inputWhitespaceValidator } from '@fincloud/core/form';
import { AddressHelperService } from '@fincloud/core/services';
import { selectUserCustomerKey } from '@fincloud/state/user';
import {
  Address,
  Company,
  CompanyDto,
  Coordinates,
  IndustryControllerService,
  NorthDataAddress,
  NorthDataCompany,
  NorthDataCompanyInfo,
} from '@fincloud/swagger-generator/company';
import { Locale } from '@fincloud/types/enums';
import { NorthDataCompanyOption } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { isEmpty } from 'lodash-es';
import { Observable, of, Subject } from 'rxjs';
import {
  catchError,
  debounceTime,
  filter,
  map,
  startWith,
  switchMap,
  take,
  tap,
} from 'rxjs/operators';
import { CompanyForm } from '../../models/company-form';
import { IndustryInfoOption } from '../../models/industry-info-option';
import { LegalFormOption } from '../../models/legal-form-option';
import { LEGAL_FORMS } from '../../utils/company';

@Component({ template: '', selector: 'app-no-selector-3' })
export abstract class BaseCreateUpdateCompanyComponent implements OnInit {
  @Input()
  company: Company;

  @Output()
  canceled = new EventEmitter();

  @Output()
  saved = new EventEmitter();

  customerKey$ = this.store.select(selectUserCustomerKey);

  addressHelperService = inject(AddressHelperService);

  companyForm = this.fb.group({
    address: new FormControl(null, Validators.required),
    companyState: new FormControl(true, Validators.required),
    legalName: new FormControl('', Validators.required),
    legalForm: new FormControl(undefined, Validators.required),
    registerId: new FormControl('', Validators.required),
    registerCity: new FormControl('', Validators.required),
    registerUniqueKey: new FormControl(''),
    industryInfo: new FormControl(undefined, Validators.required),

    registeredOn: new FormControl(''),
    extractFrom: new FormControl(''),
    taxNumber: new FormControl('', inputWhitespaceValidator()),
    vatId: new FormControl('', inputWhitespaceValidator()),
    taxId: new FormControl(''),
    foundingDate: new FormControl(''),
  });

  companyOptions$: Observable<NorthDataCompanyOption[]>;
  legalFormOptions: LegalFormOption[];
  initialIndustryOptions$: Observable<IndustryInfoOption[]>;
  industryOptions$: Observable<IndustryInfoOption[]>;

  companyInput$ = new Subject<string>();
  legalFormInput$ = new Subject<string>();
  industryInput$ = new Subject<string>();
  isCustomOption: boolean;
  isFormSubmitted: boolean;
  isNotExistingOption = false;

  _allLegalNames: string[];

  protected constructor(
    public destroyRef: DestroyRef,
    public industryControllerService: IndustryControllerService,
    public fb: FormBuilder,
    public store: Store,
    @Inject(LOCALE_ID) public locale: Locale,
  ) {}

  get isEditMode() {
    return !!this.company?.id;
  }

  get formValues(): CompanyForm {
    return this.companyForm.getRawValue() as CompanyForm;
  }

  get companyStatus(): boolean {
    return this.company.companyState === 'ACTIVE';
  }

  static mapAddress(northDataAddress: NorthDataAddress): Address {
    return {
      city: northDataAddress.city,
      coordinates: {
        lat: +northDataAddress.lat,
        lng: +northDataAddress.lng,
      } as Coordinates,
      country: northDataAddress.country,
      postalCode: northDataAddress.postalCode,
      state: northDataAddress.state,
      street: northDataAddress.street,
    };
  }

  static mapToSelectOption(
    northDataCompany: NorthDataCompany,
  ): NorthDataCompanyOption {
    return {
      name: northDataCompany.company.name.name,
      value: northDataCompany,
    };
  }
  async ngOnInit() {
    this.setDefaultLegalNames();

    this.initialIndustryOptions$ = this.getIndustryOptions();

    if (this.isEditMode) {
      this.addInputValueToDropdownMetadataIfCustom();
      this.patchCompanyValues();
    }

    this.setCurrentLegalForms();

    this.companyOptions$ = this.companyInput$.pipe(
      takeUntilDestroyed(this.destroyRef),
      debounceTime(200),
      filter((searchTerm) => !!searchTerm),
      switchMap((searchTerm) => this.getCompanies(searchTerm)),
    );

    this.legalFormInput$
      .pipe(takeUntilDestroyed(this.destroyRef), debounceTime(200))
      .subscribe((searchTerm) => {
        if (!searchTerm) {
          return;
        }

        const forms = this.getLegalForms();
        this.isNotExistingOption = !forms.some(
          (lgo: { name: string; id: string }) =>
            lgo.name
              .toLocaleLowerCase()
              .includes(searchTerm?.toLocaleLowerCase()),
        );

        this.legalFormOptions = this.isNotExistingOption
          ? [
              {
                id: searchTerm,
                name: searchTerm,
              },
            ]
          : forms.filter((lgo: { name: string; id: string }) =>
              lgo.name
                .toLocaleLowerCase()
                .includes(searchTerm?.toLocaleLowerCase()),
            );

        this.legalFormOptions = this.isNotExistingOption
          ? [
              {
                id: searchTerm,
                name: searchTerm,
              },
            ]
          : forms.filter((lgo: { name: string; id: string }) =>
              lgo.name
                .toLocaleLowerCase()
                .includes(searchTerm.toLocaleLowerCase()),
            );
      });

    this.industryOptions$ = this.industryInput$.pipe(
      takeUntilDestroyed(this.destroyRef),
      debounceTime(200),
      startWith(''),
      switchMap((searchTerm) =>
        this.initialIndustryOptions$.pipe(
          take(1),
          map((options) => {
            const isNotExistingOption = options.every(
              (industry) =>
                !industry.sector
                  ?.toLocaleLowerCase()
                  ?.includes(searchTerm?.toLocaleLowerCase()),
            );
            this.isCustomOption = isNotExistingOption;
            return isNotExistingOption
              ? [
                  {
                    industry: '',
                    sector: searchTerm,
                  } as IndustryInfoOption,
                ]
              : options.filter((industry) =>
                  industry.sector
                    ?.toLocaleLowerCase()
                    ?.includes(searchTerm?.toLocaleLowerCase()),
                );
          }),
        ),
      ),
    );
  }

  checkLegalSelectCleared(option: string) {
    if (this._allLegalNames.indexOf(option) < 0) {
      this._allLegalNames.push(option);
    }

    this.setCurrentLegalForms();
  }

  checkIndustrySelectCleared(option: unknown) {
    if (isEmpty(option) || this.isCustomOption) {
      this.isCustomOption = false;

      this.industryInput$.next('');
    }
  }

  abstract fetchAllCompanies(
    uniqueKey: string,
  ): Observable<NorthDataCompanyInfo>;

  companySelectionChanged(companySelected: NorthDataCompany) {
    if (!companySelected) {
      return;
    }

    const companyNorth = companySelected.company;

    this.fetchAllCompanies(companyNorth.register.uniqueKey)
      .pipe(
        tap((companyInfo: NorthDataCompanyInfo) => {
          this.companyForm.patchValue({
            address: {
              ...BaseCreateUpdateCompanyComponent.mapAddress(
                companyInfo?.address,
              ),
              addressAsString: this.addressHelperService.asCompanyAddressString(
                companyInfo?.address,
              ),
            },
            companyState: companyNorth?.status?.toLowerCase() === 'active',
            legalName: this.stripCityName(companyNorth?.name?.name),
            legalForm: companyNorth?.name?.legalForm || null,
            registerId: companyNorth?.register?.id,
            registerCity: companyNorth?.register?.city,
            registerUniqueKey: companyNorth?.register?.uniqueKey,
            industryInfo: null,
            registeredOn: null,
            extractFrom: null,
            taxNumber: null,
            vatId: this.getDataFromExtras(companyInfo, 'vatId'),
            taxId: null,
            foundingDate: null,
          });
        }),
      )
      .subscribe();
  }

  stripCityName(companyName: string) {
    const lastCommaIndex = companyName?.lastIndexOf(',');
    if (lastCommaIndex > 0) {
      companyName = companyName.substring(0, lastCommaIndex);
    }

    return companyName;
  }

  abstract fetchCompanies(
    searchTerm: string,
    customerKey: string,
  ): Observable<NorthDataCompany[]>;

  getCompanies(searchTerm: string) {
    return this.customerKey$.pipe(
      switchMap((customerKey) => this.fetchCompanies(searchTerm, customerKey)),
      map((companies: NorthDataCompany[]) => {
        return companies
          .filter((c) => !isEmpty(c))
          .map((company: NorthDataCompany) =>
            BaseCreateUpdateCompanyComponent.mapToSelectOption(company),
          );
      }),
      catchError(() => of([])),
    );
  }

  onSave() {
    this.isFormSubmitted = true;
    this.companyForm.markAllAsTouched();
    this.setDefaultLegalNames();
    this.setCurrentLegalForms();
  }

  onCancel() {
    this.canceled.emit();
  }

  getIndustryOptions() {
    return this.industryControllerService.getAllIndustries().pipe(
      map((sectorsGroupedByIndustries) => {
        const industrySectors: IndustryInfoOption[] = [];
        for (const industry in sectorsGroupedByIndustries) {
          for (const sector of sectorsGroupedByIndustries[industry]) {
            const existingIndustry =
              this.locale === 'de'
                ? INDUSTRIES[sector]?.de
                : INDUSTRIES[sector]?.en;

            industrySectors.push({
              sector: existingIndustry ?? sector,
              industry: SECTORS[industry],
            });
          }
        }
        const customSector = !industrySectors
          .map((iSector) => iSector.sector)
          .includes(this.companyForm.controls?.industryInfo?.value?.sector)
          ? this.companyForm.controls?.industryInfo?.value?.sector
          : null;

        if (customSector) {
          const otherIndustry = industrySectors[0].industry;
          industrySectors.push({
            industry: otherIndustry,
            sector: customSector,
          });
        }

        return industrySectors;
      }),
    );
  }

  getLegalForms() {
    this.isNotExistingOption = false;

    return this._allLegalNames
      .filter(Boolean)
      .reduce((options: LegalFormOption[], legalForm: string) => {
        options.push({
          id: legalForm,
          name: legalForm,
        } as LegalFormOption);
        return options;
      }, []);
  }

  patchCompanyValues() {
    this.companyForm.patchValue({
      address: this.company?.address,
      companyState: this.companyStatus,
      legalName: this.company.companyInfo.legalName,
      legalForm: this.company.companyInfo.legalForm || null,
      registerId: this.company.companyInfo.register.id,
      registerCity: this.company.companyInfo.register.city,
      industryInfo: this.retrieveIndustryDetails(),
      registeredOn: this.company.companyInfo.registeredOn,
      extractFrom: this.company.companyInfo.extractFrom,
      taxNumber: this.company.companyInfo.taxNumber,
      vatId: this.company.companyInfo.vatId,
      taxId: this.company.companyInfo.taxId,
      foundingDate: this.company.companyInfo.foundingDate,
    });
  }

  setDefaultIndustries() {
    this.industryInput$.next('');
  }

  setDefaultLegalNames() {
    this._allLegalNames = [...LEGAL_FORMS];
  }

  setCurrentLegalForms() {
    this.legalFormOptions = this.getLegalForms();
  }

  getDtoFromFormValues(customerKey: string): CompanyDto {
    return {
      address: this.formValues.address,
      customerKey,
      companyState: this.formValues.companyState ? 'ACTIVE' : 'DELETED',
      companyInfo: {
        legalName: this.formValues.legalName,
        legalForm: this.formValues.legalForm || null,
        register: {
          id: this.formValues.registerId,
          city: this.formValues.registerCity,
          uniqueKey: this.formValues.registerUniqueKey,
        },
        industryInfo: this.formValues.industryInfo,
        foundingDate: this.formValues.foundingDate,
        extractFrom: this.formValues.extractFrom,
        taxId: this.formValues.taxId,
        vatId: this.formValues.vatId,
        taxNumber: this.formValues.taxNumber,
        registeredOn: this.formValues.registeredOn,
      },
    };
  }

  addInputValueToDropdownMetadataIfCustom() {
    const legalFormValue = this.company.companyInfo.legalForm;

    if (this._allLegalNames.indexOf(legalFormValue) < 0) {
      this._allLegalNames.push(legalFormValue);
    }
  }

  checkCompanyLegalFormExists(
    companyLegalForm: string,
  ): string | LegalFormOption[] | null {
    if (!LEGAL_FORMS.includes(companyLegalForm)) {
      return null;
    }

    return companyLegalForm;
  }

  retrieveIndustryDetails() {
    const matchedIndustry =
      Object.values(INDUSTRIES).find(
        (industry) =>
          industry.en === this.company.companyInfo.industryInfo.sector ||
          industry.de === this.company.companyInfo.industryInfo.sector,
      ) || null;

    if (matchedIndustry) {
      return {
        sector: this.locale === 'de' ? matchedIndustry.de : matchedIndustry.en,
        industry: this.company.companyInfo.industryInfo.industry,
      };
    }

    return this.company.companyInfo.industryInfo;
  }

  private getDataFromExtras(data: NorthDataCompanyInfo, key: string) {
    const extras = data?.extras as { items: { id: string; value: string }[] }[];

    if (!extras) {
      return null;
    }

    let output: string = null;

    extras.forEach((extra) => {
      const foundItem = extra.items?.find((item) => item.id === key);

      if (foundItem) {
        output = foundItem.value;
      }
    });

    return output;
  }
}
