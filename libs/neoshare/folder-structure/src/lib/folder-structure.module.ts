import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ElementCountComponent } from '@fincloud/components/element-count';
import { NsCorePipesModule } from '@fincloud/core/pipes';
import { NsTemplateFieldModule } from '@fincloud/neoshare/template-field';
import { FinBreadcrumbModule } from '@fincloud/ui/breadcrumb';
import { FinButtonActionComponent, FinButtonModule } from '@fincloud/ui/button';
import { FinDirectoryModule } from '@fincloud/ui/directory';
import { FinFieldMessageModule } from '@fincloud/ui/field-message';
import { FinHeaderAndFooterModule } from '@fincloud/ui/header-and-footer';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinInputModule } from '@fincloud/ui/input';
import { FinMenuItemModule } from '@fincloud/ui/menu-item';
import {
  FinModalCloseDirective,
  FinModalContentDirective,
  FinModalFooterDirective,
  FinModalHeaderDirective,
  FinModalModule,
} from '@fincloud/ui/modal';
import { FinScrollbarModule } from '@fincloud/ui/scrollbar';
import { FinTruncateTextDirective } from '@fincloud/ui/truncate-text';
import { FolderDeleteModalComponent } from './components';
import { FolderDetailsComponent } from './components/folder-details/folder-details.component';
import { ManageFolderModalComponent } from './components/manage-folder-modal/manage-folder-modal.component';
import { MoveFileModalComponent } from './components/move-file-modal/move-file-modal.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    FinModalHeaderDirective,
    FinModalContentDirective,
    FinModalFooterDirective,
    FinButtonActionComponent,
    FinModalCloseDirective,
    FinModalModule,
    FinButtonModule,
    FinIconModule,
    FinInputModule,
    FinFieldMessageModule,
    FinDirectoryModule,
    FinButtonModule,
    FinBreadcrumbModule,
    FinHeaderAndFooterModule,
    FinScrollbarModule,
    FinMenuItemModule,
    FinTruncateTextDirective,
    NsTemplateFieldModule,
    NsCorePipesModule,
    ElementCountComponent,
  ],
  declarations: [
    ManageFolderModalComponent,
    MoveFileModalComponent,
    FolderDetailsComponent,
    FolderDeleteModalComponent,
  ],
  exports: [
    ManageFolderModalComponent,
    MoveFileModalComponent,
    FolderDetailsComponent,
    FolderDeleteModalComponent,
  ],
})
export class NsFolderStructureModule {}
