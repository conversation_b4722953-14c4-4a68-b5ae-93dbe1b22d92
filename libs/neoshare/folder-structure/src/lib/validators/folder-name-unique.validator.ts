import { inject } from '@angular/core';
import {
  AbstractControl,
  AsyncValidatorFn,
  ValidationErrors,
} from '@angular/forms';
import { folderStructureFeature } from '@fincloud/state/folder-structure';
import { Store } from '@ngrx/store';
import { Observable, map, of, take } from 'rxjs';

export const folderNameUniqueValidator = (
  groupKey: string,
  folderId?: string,
): AsyncValidatorFn => {
  const store = inject(Store);

  return (control: AbstractControl): Observable<ValidationErrors | null> => {
    if (!control.value) {
      return of(null);
    }

    return store
      .select(folderStructureFeature.selectCurrentFolderPerGroup)
      .pipe(
        take(1),
        map(
          (currentFolderPerGroup) =>
            currentFolderPerGroup?.[groupKey]?.children
              ?.filter((subfolder) => subfolder.id !== folderId)
              ?.map((subfolder) => subfolder.name.toLocaleLowerCase()) || [],
        ),
        map((subfolderNames) => {
          if (subfolderNames.includes(control.value.toLocaleLowerCase())) {
            return { folderNameUnique: true };
          }
          return null;
        }),
      );
  };
};
