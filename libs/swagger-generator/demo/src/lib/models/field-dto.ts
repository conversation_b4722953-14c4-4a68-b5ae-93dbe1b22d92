/* tslint:disable */
/* eslint-disable */
import { FieldOwner } from '../models/field-owner';
export interface FieldDto {
  categoryId?: string;
  dependantFields?: Array<string>;
  description?: string;
  expression?: string;
  fieldMetaData?: {
};
  fieldOwner?: FieldOwner;
  fieldType?: 'SHORT_TEXT' | 'LONG_TEXT' | 'INTEGER' | 'DECIMAL' | 'MONETARY' | 'DATE' | 'DOCUMENT' | 'MONTHS' | 'PERCENT' | 'BOOLEAN' | 'LOCATION' | 'SELECT' | 'TABLE' | 'DATE_RANGE' | 'COMPOSITE' | 'MULTI_SELECT';
  isHidden?: boolean;
  isPromoted?: boolean;
  isPublic?: boolean;
  isRequired?: boolean;
  key?: string;
  label?: string;
  portalVisibility?: 'VISIBLE' | 'REQUESTED' | 'NOT_SET';
  priority?: number;
  value?: {
};
  visibilityExpression?: string;
}
