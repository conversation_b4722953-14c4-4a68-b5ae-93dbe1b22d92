const GERMAN_UMLAUTS = ['ä', 'ö', 'ü', 'ß'];
const GERMAN_UMLAUTS_EQUIVALENTS = ['ae', 'oe', 'ue', 'ss'];

export function normalizeUmlauts(text: string): string[] {
  let copyText = text;
  for (let i = 0; i < GERMAN_UMLAUTS.length; i++) {
    //Or we change to es2021 so replaceAll can be used
    while (copyText.includes(GERMAN_UMLAUTS[i])) {
      copyText = copyText.replace(
        GERMAN_UMLAUTS[i],
        GERMAN_UMLAUTS_EQUIVALENTS[i],
      );
    }
  }
  return text === copyText ? [text] : [text, copyText];
}
