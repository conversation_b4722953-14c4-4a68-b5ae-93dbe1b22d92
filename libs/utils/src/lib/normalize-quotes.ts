import { replacePairs } from './replace-pairs';

const CURLY_DOUBLE_QUOTES = ['“', '”'];
const GERMAN_DOUBLE_QUOTES = ['„', '“'];
const NORMAL_DOUBLE_QUOTES = ['"', '"'];

const CURLY_SINGLE_QUOTES = ['‘', '’'];
const GERMAN_SINGLE_QUOTES = ['‚', '‘'];
const NORMAL_SINGLE_QUOTES = ["'", "'"];

export function normalizeQuotes(searchTerm: string): string[] {
  const searchValuesSingleQuotes = [CURLY_SINGLE_QUOTES, GERMAN_SINGLE_QUOTES]
    .map((quotes) => replacePairs(searchTerm, quotes, NORMAL_SINGLE_QUOTES))
    .filter(Boolean);

  const searchValuesDoubleQuotes = [CURLY_DOUBLE_QUOTES, GERMAN_DOUBLE_QUOTES]
    .map((quotes) => replacePairs(searchTerm, quotes, NORMAL_DOUBLE_QUOTES))
    .filter(Boolean);

  return [...searchValuesSingleQuotes, ...searchValuesDoubleQuotes];
}
