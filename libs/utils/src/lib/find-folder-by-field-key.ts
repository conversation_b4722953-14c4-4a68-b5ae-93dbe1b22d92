import { Folder } from '@fincloud/swagger-generator/business-case-manager';
import { FinBreadcrumbItem } from '@fincloud/ui/breadcrumb';

/**
 * @description Recursively searches through a tree folder structure to find the folder
 * that contains a given field key and generates breadcrumbs to represent the navigation path.
 *
 * @param {Folder} folder - The tree folder structure to search the target folder in.
 * @param {string} fieldKey - The field key to search for.
 * @param {string[]} [path=[]] - Builds the breadcrumbs path used to navigate the folder structure.
 * @param {FinBreadcrumbItem[]} [breadcrumbs=[]] - Stores the breadcrumb trail up to the current folder along with additional breadcrumb data for each folder.
 * @returns {{ folder: Folder | undefined, breadcrumbs: FinBreadcrumbItem[] }} An object containing:
 *          - folder: the folder with the specified ID (if found), otherwise undefined.
 *          - breadcrumbs: an array of Breadcrumb objects representing the path to the located folder.
 **/
export const findFolderByFieldKey = (
  folder: Folder,
  fieldKey: string,
  path: string[] = [],
  breadcrumbs: FinBreadcrumbItem[] = [],
): { folder: Folder | undefined; breadcrumbs: FinBreadcrumbItem[] } => {
  // create a breadcrumb item for the current folder to be added in the breadcrumbs array
  const currentPath = path.join('.');
  breadcrumbs.push({
    label: folder.name,
    data: {
      folderId: folder.id,
      path: currentPath,
    },
  });

  if (folder.fields?.includes(fieldKey)) {
    // the folder that we're looking for is found
    return {
      folder,
      breadcrumbs,
    };
  } else if (folder.children?.length) {
    // iterate through the subfolders recursively and try to find the folder
    for (let i = 0; i < folder.children.length; i++) {
      const child = folder.children[i];
      const result = findFolderByFieldKey(
        child,
        fieldKey,
        path.concat(`children[${i}]`),
        breadcrumbs.slice(),
      );
      if (result.folder) {
        return result;
      }
    }
  }

  // cannot find the folder we're looking for
  return { folder: undefined, breadcrumbs: [] };
};
