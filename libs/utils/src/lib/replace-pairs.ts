export function replacePairs(
  term: string,
  toBeReplaced: string[],
  replaceWith: string[],
): string {
  if (!term.includes(toBeReplaced[0]) && !term.includes(toBeReplaced[1])) {
    return '';
  }

  for (let i = 0; i < toBeReplaced.length; i++) {
    // Need to escape brackets as they are taken as begining of group and etc.
    const matchingRegex = new RegExp('\\' + toBeReplaced[i], 'g');
    term = term.replace(matchingRegex, replaceWith[i]);
  }
  return term;
}
