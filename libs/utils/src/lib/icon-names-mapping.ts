/**
 * Mapping from friendly names used in the app to material-icon names
 */
export const ICON_NAMES_MAPPING = {
  'add-box': 'add_box',
  'arrow-circle-right': 'arrow_circle_right',
  'arrow-down': 'south',
  'calendar-today': 'calendar_today',
  'check-circle': 'check_circle',
  'downward-arrow': 'south',
  'drag-handle': 'drag_handle',
  'drag-indicator': 'drag_indicator',
  'edit-note': 'edit_note',
  'folder-open': 'folder_open',
  'hour-glass-empty': 'hourglass_empty',
  'integration-instructions': 'integration_instructions',
  'lock-open': 'lock_open',
  'material-symbols-outlined': 'material-symbols-outlined',
  'remove-circle': 'remove_circle_outline',
  'supervised-user-circle-outlined': 'supervised_user_circle_outlined',
  'supervised-user-circle': 'supervised_user_circle',
  'remove-participant': 'person_remove',
  'right-arrow': 'east',
  'three-dots-vert': 'more_vert',
  'title-plus': 'title',
  'transfer-leader': 'connect_without_contact',
  'upward-arrow': 'north',
  'user-management': 'manage_accounts',
  'view-headline': 'view_headline',
  'view-week': 'view_week',
  'watch-later': 'watch_later',
  'zoom-in': 'zoom_in',
  'zoom-out': 'zoom_out',
  laptopIcon: '💻',
  pcIcon: '🖥️',
  shield: 'shield',
  accountCircle: 'account_circle',
  account_balance: 'account_balance',
  account_box: 'account_box',
  add: 'add',
  add_participant: 'person_add',
  align_horizontal_left: 'align_horizontal_left',
  align_horizontal_right: 'align_horizontal_right',
  align_vertical_bottom: 'align_vertical_bottom',
  align_vertical_top: 'align_vertical_top',
  vertical_align_top: 'vertical_align_top',
  vertical_align_bottom: 'vertical_align_bottom',
  vertical_align_center: 'vertical_align_center',
  apartment: 'apartment',
  apps: 'apps',
  archive: 'archive',
  arrow_circle_right: 'arrow_circle_right',
  arrow_down: 'arrow_drop_down',
  arrow_forward: 'arrow_forward_ios ',
  arrow_right: 'arrow_right',
  article: 'article',
  assignment: 'assignment',
  assignment_add: 'assignment_add ',
  autorenew: 'autorenew',
  balance: 'account_balance',
  business: 'business',
  business_center: 'business_center',
  cake: 'cake',
  calendar: 'date_range',
  cancel: 'cancel',
  chat_bubble: 'chat_bubble_outline',
  chat_bubble_fill: 'chat_bubble',
  chat_bubble_inline: 'chat_bubble',
  chat_unread: 'mark_chat_unread',
  check: 'check',
  chevron_left: 'chevron_left',
  circle: 'circle',
  clear: 'clear',
  clip: 'attach_file',
  close: 'close',
  code: 'code',
  collapse: 'expand_less',
  contacts: 'contacts',
  content_copy: 'content_copy',
  copy: 'copy',
  compare_arrows: 'compare_arrows',
  delete: 'delete',
  dialpad: 'dialpad',
  svgAccessibility: 'svgAccessibility',
  do_not_disturb_on: 'do_not_disturb_on',
  document: 'description',
  domain_add: 'domain_add',
  done: 'done',
  download: 'file_download',
  download_circle: 'download_for_offline',
  draft: 'draft',
  edit: 'edit',
  editOff: 'edit_off',
  edit_document: 'edit_document',
  euro: 'euro',
  expand: 'expand_more',
  expand_less: 'expand_less',
  expand_more: 'expand_more',
  feedback: 'feedback',
  file_upload: 'file_upload',
  file_present: 'file_present',
  filter_list: 'filter_list',
  filter_alt: 'filter_alt',
  file_open: 'file_open',
  folder: 'folder',
  forum: 'forum',
  fullscreen: 'fullscreen',
  fullscreen_exit: 'fullscreen_exit',
  open_in_full: 'open_in_full',
  open_in_new: 'open_in_new',
  close_fullscreen: 'close_fullscreen',
  gold: 'gold',
  graph: 'equalizer',
  svgDeclineChanges: 'svgDeclineChanges',
  grid: 'grid_view',
  group: 'group',
  help: 'help_outline',
  history: 'history',
  home: 'home',
  hourglass: 'hourglass_bottom',
  image: 'image',
  inbox: 'inbox',
  info: 'info',
  insert_drive_file: 'insert_drive_file',
  left: 'arrow_back_ios_new',
  libraryAdd: 'library_add',
  libraryAdded: 'library_add_check',
  link: 'link',
  list: 'list',
  location: 'location_on',
  logout: 'logout',
  mail: 'mail',
  mark_chat_read: 'mark_chat_read',
  mark_email_read: ' mark_email_read',
  market: 'storefront',
  menu: 'menu',
  merge: 'merge',
  mobile_phone: 'phone_android',
  money: 'attach_money',
  my_location: 'my_location',
  notifications_off: 'notifications_off',
  numbers: 'numbers',
  subject: 'subject',
  pdf: 'picture_as_pdf',
  pen_underscore: 'border_color',
  people: 'people',
  percent: 'percent',
  person: 'person_outline',
  phone: 'local_phone',
  pinch: 'pinch',
  place: 'place',
  print: 'print',
  receipt: 'receipt',
  refresh: 'refresh',
  remove: 'remove',
  remove_user: 'remove_circle_outline',
  replay: 'replay',
  report: 'report',
  calculate: 'calculate',
  restart: 'restart_alt',
  right: 'chevron_right',
  rule: 'rule',
  save: 'save',
  schedule: 'schedule',
  search: 'search',
  send: 'send',
  sentiment_dissatisfied: 'sentiment_dissatisfied',
  sentiment_neutral: 'sentiment_neutral',
  sentiment_satisfied: 'sentiment_satisfied',
  server: 'dns',
  settings: 'settings',
  shortcut: 'shortcut',
  show_chart: 'show_chart',
  sync: 'sync',
  cut: 'cut',
  format_color_text: 'format_color_text',
  format_color_fill: 'format_color_fill',
  format_underlined: 'format_underlined',
  format_bold: 'format_bold',
  format_italic: 'format_italic',
  format_align_center: 'format_align_center',
  format_align_left: 'format_align_left',
  format_align_right: 'format_align_right',
  format_align_justify: 'format_align_justify',
  ios_share: 'ios_share',
  functions: 'functions',
  event_list: 'event_list',
  wrap_text: 'wrap_text',
  window: 'window',
  svgEmptyUsersState: '',
  svgAccept: '',
  svgAcceptCheckbox: '',
  svgAddMeToCase: '',
  svgAddMeToCaseDuplicate: '',
  svgApartment: '',
  svgApartmentCrossed: '',
  svgApartmentPopulated: '',
  svgApartmentPopulatedLarge: '',
  svgApartmentRequested: '',
  svgArchivedChatAccess: '',
  svgArchivedChatNonAccess: '',
  svgAssignmentAdd: '',
  svgBusinessCase: '',
  svgBusinessCase2: '',
  svgBusinessCaseInverse: '',
  svgCancelledInvitation: '',
  svgUnblockCollaboration: '',
  svgBlockCollaboration: '',
  svgChatBubble: '',
  svgChatTick: '',
  svgChatTickX: '',
  svgChatExisting: '',
  svgCustomerMasterData: '',
  svgCaseReassignmentIcon: '',
  svgDataRoomGroupAccess: '',
  svgDiscardFieldRequestAction: '',
  svgDissatisfied: '',
  svgDisabledVisible: '',
  svgDracoonAppsCommingSoon: '',
  svgDracoonLogo: '',
  svgDracoonLogoIcon: '',
  svgDracoonInformationIcon: '',
  svgDirectShare: '',
  svgEditDocument: '',
  svgEmptyGroup: '',
  svgCopyGroups: '',
  svgGenerateCoreBankingToken: '',
  svgEuro: '',
  svgFilter: '',
  svgImageDocument: '',
  svgGermanFlag: '',
  svgIntegrateDracoon: '',
  svgIntegrateDracoonLoading: '',
  svgCaseTransferWarningIcon: '',
  svgIntegrateDracoonWarning: '',
  svgLeaderCustomerAvatar: '',
  svgMiniChart: '',
  svgAllowDataExport: 'svgAllowDataExport',
  svgManyCustomers: '',
  svgMultipleCustomerInvitationsAvatar: '',
  svgNeoLogo: '',
  svgNeoshareLogo: '',
  svgNeoshareLogoV: '',
  svgPoweredByNeoshareLogo: '',
  svgNextfolderInformationIcon: '',
  svgRequirementCheckboxDone: '',
  svgRemoveDeviceIcon: '',
  svgNextfolderIcon: '',
  svgNextfolderSuccessIcon: '',
  svgNextfolderLogoIcon: '',
  svgIntegrateNextfolderWarning: '',
  svgNeutral: '',
  svgParticipatingCustomerAvatar: '',
  svgParticipantFieldNotRequested: '',
  svgParticipantFieldPopulated: '',
  svgParticipantFieldRequested: '',
  svgRestrictedCaseAccess: '',
  svgDuplicateCase: '',
  svgParticipantRolesChange: '',
  svgPasswordReset: '',
  svgPortalGroupActionsInfo: '',
  svgPositive: '',
  svgRemoveFinBlock: '',
  svgRemoveFolder: '',
  svgEmptyFolder: '',
  svgPriorityHigh: '',
  svgPending: '',
  svgReRequestFieldAction: '',
  svgRequestedTransferLeadership: '',
  svgReject: '',
  svgRejectedIcon: '',
  svgRejectedIconError: '',
  svgEditIcon: '',
  svgSuccessIcon: '',
  svgSignLaterIcon: '',
  svgSessionTimeoutIcon: '',
  svgRemoveContact: '',
  svgRadioButtonEmpty: '',
  svgRadioButtonSelected: '',
  svgRemoveParticipant: '',
  svgDeleteFile: '',
  svgRequestFieldAction: '',
  svgShieldLock: '',
  svgSearchOff: '',
  svgSentFail: '',
  svgErrorNotFound: '',
  svgSetGroupPublic: '',
  svgSetGroupPrivate: '',
  svgSetGroupInviteeApplicant: '',
  svgSetGroupParticipant: '',
  svgSendAuthCode: '',
  svgSentSuccess: '',
  svgSingleCustomerInvitationAvatar: '',
  svgSidebar: '',
  svgTable: '',
  svgLight: '',
  svgTransferLeadership: '',
  svgUkFlag: '',
  svgRemoveExplicitCadrShare: '',
  svgUndo: '',
  svgRedo: '',
  svgUserDelete: '',
  svgUserDeleteAction: '',
  svgUpdateInProgress: '',
  svgUpdateStateIcon: '',
  svgVisibilityPortal: '',
  svgVisibilityPortalOff: '',
  svgViaCaseShare: '',
  svgEditSquare: '',
  svgAnonymousUser: '',
  svgUserMissing: '',
  svgEvaluateIcon: '',
  svgCorporateCancelGroup: '',
  svgCorporateAcceptGroup: '',
  svgActivityLog: '',
  svgCorporateGroup: '',
  svgHint: '',
  svgNeoGptIconComponent: '',
  svgSettingsApplications: '',
  svgWithdrawParticipationIcons: '',
  svgUploadProfilePhoto: '',
  svgProfileIcon: '',
  svgLogoutIcon: '',
  svgPersonDataLogoutIcon: '',
  svgNeutralFace: '',
  svgPositiveFace: '',
  svgNegativeFace: '',
  svgDeleteV2: '',
  svgBell: '',
  svgEventList: '',
  svgImageUpload: '',
  svgDeleteProfilePhotoIcon: '',
  svgCoreBankingLogo: '',
  svgProfilePictureHoverIcon: '',
  svgAdjustProfilePictureIcon: '',
  svgSettingsAlert: 'ui-svg-settings-alert-icon',
  svgAddFiles: '',
  svgDevices: '',
  svgLock: '',
  svgLoginUnauthorizeDevices: '',
  svgMissingLogo: '',
  svgInHomeMode: '',
  svgHomeWork: '',
  svgFlower: '',
  svgScreenshotRegion: '',
  svgGenericErrorExclamation: '',
  svgUpwardTrend: '',
  svgCloseCaseComposite: '',
  svgReactivateCaseComposite: '',
  svgEnableKpiComposite: '',
  svgDisableKpiComposite: '',
  svgHighWarningIconComponent: '',
  svgAccountManagementEmptyState: '',
  svgFilterList: '',
  extension: 'extension',
  tag: 'tag',
  task: 'task',
  template: 'copy_all',
  text_fields: 'text_fields',
  three_dots_horizontal: 'more_horiz',
  thumb_up: 'thumb_up',
  thumb_down: 'thumb_down',
  timer: 'timer',
  title: 'title',
  unfold_less: 'unfold_less',
  unfold_more: 'unfold_more',
  update: 'update',
  upload: 'upload',
  verified: 'verified',
  visibility: 'visibility',
  visibility_off: 'visibility_off',
  warning: 'warning_amber',
  website: 'language',
  work_outline: 'work_outline',
  share: 'share',
  done_all: 'done_all',
  group_add: 'group_add',
  pending_actions: 'pending_actions',
  forward_to_inbox: 'forward_to_inbox',
  list_alt: 'list_alt',
  dashboard: 'dashboard',
  svgFilterV2: '',
  svgNeogptLogo: '',
  svgChevronRight: 'svgChevronRight',
  svgNotificationSystemChatIcon: '',
  svgNotificationSystemEmptyState: '',
  svgAddField: '',
  svgSuccessCheck: '',
  svgAddFiles2: '',
  svgNotificationModalImg: '',
  svgAvatarNoImage: '',
  svgBackgroundForStepperModal: '',
  svgChatBigBubble: '',
  svgDashboardEmpty: '',
  svgDeleteInput: '',
  svgNeoshareForegroundDefaultDe: '',
  svgNeoshareForegroundDefaultEn: '',
  svgNeoshareLogo2: '',
  svgNeoGptFirstStepImg: '',
  svgNeoGptThirdStepImg: '',
  svgNeoGptSecondImg: '',
  svgTipsDragAndDropFinanceCase: '',
  svgTipsDragAndDropMail: '',
  svgTipsSelectAndAddToGroups: '',
  svgClusterDemo: '',
  svgNoDemosIcon: '',
  svgViewIcon: '',
  svgResetIcon: '',
  svgCloneIcon: '',
  svgDeleteIcon: '',
  svgCancelProjectCreation: '',
  svgNeogptChatAvatar: '',
  svgShowChart: '',
  svgAIDocument: '',
  svgReturn: '',
  svgDelete: '',
  svgDownload: '',
  svgDownloadV2: '',
  gpp_maybe: 'gpp_maybe',
  svgBubbleRounded: '',
  svgRemoveSnapshot: '',
  svgManageSnapshotDeployment: '',
  spinner: '',
  svgZipIcon: '',
  svgPlaceholderIcon: '',
  svgPresentationIcon: '',
  svgAudioIcon: '',
  svgDocumentIcon: '',
  svgExcelIcon: '',
  svgVideoIcon: '',
  svgImageIcon: '',
  svgEmailIcon: '',
  svgConfigIcon: '',
  svgDiagramIcon: '',
  svgDefaultDocumentIcon: '',
};
