import { cloneDeep, has, mergeWith } from 'lodash-es';

export class ObjectHelper {
  public static isObject(item: unknown) {
    return (
      item && typeof item === 'object' && !Array.isArray(item) && item !== null
    );
  }

  /**
   * Merges all source objects into the target one
   * Will skip source properties wuth NULL/UNDEFINED values
   * @param {Partial<T>} target
   * @param {...Partial<T>[]} sources
   * @return {*}  {T}
   */
  public static mergeDeep<T>(target: Partial<T>, ...sources: Partial<T>[]): T {
    mergeWith(target, ...sources, (a: Partial<T>, b: Partial<T>) => {
      if (Array.isArray(a) && Array.isArray(b)) {
        return [...a, ...b];
      }
      return b === null ? a : undefined;
    });
    return target as T;
  }

  public static cloneDeep<T>(object: T): T {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-call
    const cloned = cloneDeep(object);

    return cloned;
  }

  public static isNumeric(strValue: string) {
    return (
      !isNaN(strValue as unknown as number) &&
      !isNaN(parseFloat(<string>strValue))
    );
  }

  public static parseString(strValue: string) {
    const isNumeric = (str: unknown) =>
      !isNaN(<number>str) && // use type coercion to parse the _entirety_ of the string (`parseFloat` alone does not do this)...
      !isNaN(parseFloat(<string>str)); // ...and ensure strings of whitespace fail

    if (strValue === 'true' || strValue === 'false') {
      return strValue === 'true';
    } else if (isNumeric(strValue)) {
      return parseFloat(strValue);
    }

    return strValue;
  }

  public static objectContainsObject(
    object: Record<string, unknown>,
    subObject: Record<string, unknown>,
  ) {
    const objProps = Object.getOwnPropertyNames(object);
    const subProps = Object.getOwnPropertyNames(subObject);

    if (subProps.length > objProps.length) {
      return false;
    }

    for (const subProp of subProps) {
      if (!has(object, subProp)) {
        return false;
      }

      if (object[subProp] !== subObject[subProp]) {
        return false;
      }
    }

    return true;
  }

  public static compareObjects(
    objA: { [x: string]: unknown; hasOwnProperty: (arg: string) => unknown },
    objB: { [x: string]: unknown },
  ) {
    let equal = true;
    Object.keys(objB).forEach((key) => {
      // eslint-disable-next-line no-prototype-builtins
      if (!objA.hasOwnProperty(key) || objA[key] !== objB[key]) {
        equal = false;
        return;
      }
    });
    return equal;
  }

  public static when(condition: boolean) {
    if (condition) {
      return {
        add(overrides: Record<string, unknown>) {
          return {
            to(target: Record<string, unknown>) {
              return ObjectHelper.mergeDeep(target, overrides);
            },
          };
        },
      };
    }
    return {
      add(_overrides: Record<string, unknown>) {
        return {
          to(target: Record<string, unknown>) {
            return target;
          },
        };
      },
    };
  }
}
