import { DocumentMimeType } from '@fincloud/types/enums';

export const MIME_TYPE_ICON_PATHS: Record<DocumentMimeType, string> = {
  // Document
  [DocumentMimeType.CSV]: 'assets/svg/svgDocumentIcon.svg',
  [DocumentMimeType.PDF]: 'assets/svg/svgDocumentIcon.svg',
  [DocumentMimeType.PDF_UTF8]: 'assets/svg/svgDocumentIcon.svg',
  [DocumentMimeType.DOC]: 'assets/svg/svgDocumentIcon.svg',
  [DocumentMimeType.DOCX]: 'assets/svg/svgDocumentIcon.svg',
  [DocumentMimeType.TXT]: 'assets/svg/svgDocumentIcon.svg',
  [DocumentMimeType.RTF]: 'assets/svg/svgDocumentIcon.svg',
  [DocumentMimeType.ODT]: 'assets/svg/svgDocumentIcon.svg',

  // Spreadsheet
  [DocumentMimeType.XLS]: 'assets/svg/svgExcelIcon.svg',
  [DocumentMimeType.XLSX]: 'assets/svg/svgExcelIcon.svg',
  [DocumentMimeType.XLSX_UTF8]: 'assets/svg/svgExcelIcon.svg',
  [DocumentMimeType.ODS]: 'assets/svg/svgExcelIcon.svg',

  // Presentation
  [DocumentMimeType.PPT]: 'assets/svg/svgPresentationIcon.svg',
  [DocumentMimeType.PPTX]: 'assets/svg/svgPresentationIcon.svg',
  [DocumentMimeType.ODP]: 'assets/svg/svgPresentationIcon.svg',

  // Archive
  [DocumentMimeType.ZIP]: 'assets/svg/svgZipIcon.svg',
  [DocumentMimeType.ZIP_ALT_1]: 'assets/svg/svgZipIcon.svg',
  [DocumentMimeType.ZIP_ALT_2]: 'assets/svg/svgZipIcon.svg',
  [DocumentMimeType.SEVEN_ZIP]: 'assets/svg/svgZipIcon.svg',
  [DocumentMimeType.ARC]: 'assets/svg/svgZipIcon.svg',
  [DocumentMimeType.GZ]: 'assets/svg/svgZipIcon.svg',
  [DocumentMimeType.RAR]: 'assets/svg/svgZipIcon.svg',
  [DocumentMimeType.TAR]: 'assets/svg/svgZipIcon.svg',

  // Audio
  [DocumentMimeType.AAC]: 'assets/svg/svgAudioIcon.svg',
  [DocumentMimeType.MP3]: 'assets/svg/svgAudioIcon.svg',
  [DocumentMimeType.WAV]: 'assets/svg/svgAudioIcon.svg',
  [DocumentMimeType.WEBM]: 'assets/svg/svgAudioIcon.svg',

  // Video
  [DocumentMimeType.MP4]: 'assets/svg/svgVideoIcon.svg',
  [DocumentMimeType.MOV]: 'assets/svg/svgVideoIcon.svg',
  [DocumentMimeType.AVI]: 'assets/svg/svgVideoIcon.svg',
  [DocumentMimeType.MPEG]: 'assets/svg/svgVideoIcon.svg',

  // Image
  [DocumentMimeType.JPG]: 'assets/svg/svgImageIcon.svg',
  [DocumentMimeType.PNG]: 'assets/svg/svgImageIcon.svg',
  [DocumentMimeType.SVG]: 'assets/svg/svgImageIcon.svg',
  [DocumentMimeType.BMP]: 'assets/svg/svgImageIcon.svg',
  [DocumentMimeType.GIF]: 'assets/svg/svgImageIcon.svg',
  [DocumentMimeType.TIFF]: 'assets/svg/svgImageIcon.svg',
  [DocumentMimeType.WEBP]: 'assets/svg/svgImageIcon.svg',

  // Email
  [DocumentMimeType.MSG]: 'assets/svg/svgEmailIcon.svg',
  [DocumentMimeType.EML]: 'assets/svg/svgEmailIcon.svg',

  // Config
  [DocumentMimeType.JSON]: 'assets/svg/svgConfigIcon.svg',
  [DocumentMimeType.XML]: 'assets/svg/svgConfigIcon.svg',
  [DocumentMimeType.YAML]: 'assets/svg/svgConfigIcon.svg',

  // Diagram
  [DocumentMimeType.DWG]: 'assets/svg/svgDiagramIcon.svg',
  [DocumentMimeType.VSD]: 'assets/svg/svgDiagramIcon.svg',
};
