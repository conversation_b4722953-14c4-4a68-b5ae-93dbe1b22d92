import {
  Directive,
  ElementRef,
  Input,
  OnChanges,
  OnDestroy,
} from '@angular/core';

@Directive({
  selector: '[appFocus]',
  exportAs: 'appFocus',
})
export class FocusDirective implements OnChanges, OnDestroy {
  @Input()
  appFocus: { value: boolean };

  private timeout: NodeJS.Timeout;

  constructor(private hostElement: ElementRef<HTMLElement>) {}

  ngOnChanges() {
    this.checkIfTimeoutShouldBeCleared();
    if (this.appFocus?.value) {
      this.timeout = setTimeout(
        (() => {
          this.hostElement.nativeElement.focus();
        }).bind(this),
        400,
      );
    }
  }

  ngOnDestroy() {
    this.checkIfTimeoutShouldBeCleared();
  }

  private checkIfTimeoutShouldBeCleared() {
    if (this.timeout) {
      clearTimeout(this.timeout);
    }
  }
}
