import {
  AfterViewInit,
  DestroyRef,
  Directive,
  ElementRef,
  Input,
  Renderer2,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { isEqual } from 'lodash-es';
import { distinctUntilChanged } from 'rxjs';
import { PositioningInfo } from '../models/positioning-info';
import { ElementPositioningService } from '../services/element-positioning.service';

/**
 * This directive represents the TARGET in a synchronisation mechanism between html elements
 * It applies a set of properties directly on the element when notified
 *
 * @example
 * <div appSyncPositionSource="some-key">
 * ....
 * // will get certain html/css properties from the synced element above
 * <div appSyncPositionTarget="some-key">
 *
 */

@Directive({
  selector: '[appSyncPositionTarget]',
  exportAs: 'appSyncPositionTarget',
})
export class SyncPositionTargetDirective implements AfterViewInit {
  @Input()
  appSyncPositionTarget: string;

  @Input()
  appSyncTargetProperties: (keyof PositioningInfo)[] = ['width', 'leftOffset'];

  positioningInfo: PositioningInfo;
  propertyValueAccessors = {
    width: () => ['width', `${this.positioningInfo.width}px`],
    leftOffset: () => ['left', `${this.positioningInfo.leftOffset}px`],
    height: () => ['height', `${this.positioningInfo.height}px`],
    left: () => ['left', `${this.positioningInfo.left}px`],
    right: () => [
      'left',
      `${
        this.positioningInfo.left +
        this.positioningInfo.width -
        this.el.nativeElement.offsetWidth
      }px`,
    ],
  };
  constructor(
    private destroyRef: DestroyRef,
    private el: ElementRef<HTMLElement>,
    private renderer: Renderer2,
    private elementPositioningService: ElementPositioningService,
  ) {}

  ngAfterViewInit(): void {
    this.elementPositioningService
      .getPositionChangesByKey(this.appSyncPositionTarget)
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        distinctUntilChanged((prev, curr) => isEqual(prev, curr)),
      )
      .subscribe((positioningInfo) => {
        // don't ask - waiting for redesign and removing document inbox :)
        if (this.appSyncPositionTarget === 'data-room') {
          positioningInfo.leftOffset += 26;
        }
        this.positioningInfo = positioningInfo;
        this.applyProperties();
        this.forceReflow();
      });
  }

  // Safari specific fix
  private forceReflow() {
    this.renderer.setStyle(this.el.nativeElement, 'display', 'none');
    this.el.nativeElement.offsetHeight; // no need to store this anywhere, the reference is enough
    this.renderer.setStyle(this.el.nativeElement, 'display', '');
  }

  private getPropertyValue(property: keyof PositioningInfo) {
    if (!(property in this.propertyValueAccessors)) {
      throw new Error(
        `Property ${property} is not supported by ${SyncPositionTargetDirective.name}`,
      );
    }
    return this.propertyValueAccessors[property]();
  }

  private applyProperties() {
    for (const property of this.appSyncTargetProperties) {
      this.renderer.setStyle(
        this.el.nativeElement,
        ...(this.getPropertyValue(property) as [string, string]),
      );
    }
  }
}
