import {
  AfterViewInit,
  Directive,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
} from '@angular/core';
import { WindowRef } from '@fincloud/core/services';
import {
  cancelAnimationFrameUnpatched,
  requestAnimationFrameUnpatched,
} from '@fincloud/core/utils';
import { Subject, debounceTime, fromEvent, merge, takeUntil } from 'rxjs';
import { PositioningInfo } from '../models/positioning-info';
import { ElementPositioningService } from '../services/element-positioning.service';
import { LayoutCommunicationService } from '../services/layout-communication.service';

/**
 * This directive represents the SOURCE in a synchronisation mechanism between html elements
 * It provides real-time info on it's position and size
 *
 * @example
 * <div appSyncPositionSource="some-key">
 * ....
 * // will get certain html/css properties from the synced element above
 * <div appSyncPositionTarget="some-key">
 *
 */
@Directive({
  selector: '[appSyncPositionSource]',
  exportAs: 'appSyncPositionSource',
})
export class SyncPositionSourceDirective implements AfterViewInit, OnDestroy {
  @Input()
  appSyncPositionSource: string;

  @Input()
  appSyncSourceProperties: (keyof PositioningInfo)[] = ['width', 'leftOffset'];

  @Input()
  appSyncSourceMinThreshold: number = Number.NEGATIVE_INFINITY;

  @Input()
  appSyncSourceMaxThreshold: number = Number.POSITIVE_INFINITY;

  @Input()
  appSyncSourceWidthCorrection = 0;

  @Output()
  positionUpdated = new EventEmitter<PositioningInfo>();

  resizeObserver: ResizeObserver;
  private destroy$$ = new Subject<void>();
  private requestAnimationFrameId: number;

  propertyValueAccessors = {
    width: (element: ElementRef<HTMLElement>) =>
      element.nativeElement.offsetWidth,
    height: (element: ElementRef<HTMLElement>) =>
      element.nativeElement.offsetHeight,
    leftOffset: (element: ElementRef<HTMLElement>) =>
      element.nativeElement.offsetLeft,
    left: (element: ElementRef<HTMLElement>) =>
      element.nativeElement.getBoundingClientRect().left,
    right: (element: ElementRef<HTMLElement>) =>
      element.nativeElement.getBoundingClientRect().right,
  };

  constructor(
    private el: ElementRef<HTMLElement>,
    private elementPositioningService: ElementPositioningService,
    private layoutCommunicationService: LayoutCommunicationService,
    private windowRef: WindowRef,
  ) {}

  ngAfterViewInit(): void {
    this.resizeObserver = new ResizeObserver((entries) => {
      // We wrap it in requestAnimationFrame to avoid this error - ResizeObserver loop limit exceeded
      if (this.requestAnimationFrameId) {
        cancelAnimationFrameUnpatched(this.requestAnimationFrameId);
        this.requestAnimationFrameId = 0;
      }
      this.requestAnimationFrameId = requestAnimationFrameUnpatched(() => {
        if (!Array.isArray(entries) || !entries.length) {
          return;
        }

        entries.forEach(() => {
          this.notifyForResize();
        });
      });
    });

    this.resizeObserver.observe(this.el.nativeElement);

    merge(
      this.layoutCommunicationService.layoutChanged$,
      fromEvent(this.windowRef.nativeWindow, 'resize'),
    )
      .pipe(debounceTime(300), takeUntil(this.destroy$$))
      .subscribe(() => {
        this.notifyForResize();
      });
  }

  private notifyForResize() {
    if (this.shouldNotify()) {
      const positionInfo = this.getPositionInfo();
      this.applyCorrections(positionInfo);

      this.elementPositioningService.emitPositioningChanges(
        this.appSyncPositionSource,
        positionInfo,
      );
      this.positionUpdated.emit(positionInfo);
    }
  }

  private applyCorrections(positionInfo: PositioningInfo) {
    if (this.appSyncSourceWidthCorrection !== 0) {
      positionInfo.width += this.appSyncSourceWidthCorrection;
      positionInfo.leftOffset += (-1 * this.appSyncSourceWidthCorrection) / 2;
    }
  }

  private shouldNotify() {
    const { innerWidth: width } = this.windowRef.nativeWindow;

    return (
      width >= this.appSyncSourceMinThreshold &&
      width <= this.appSyncSourceMaxThreshold
    );
  }

  private getPositionInfo(): PositioningInfo {
    return this.appSyncSourceProperties.reduce((p, c) => {
      p = { ...p, [c]: this.getPropertyValue(c) };
      return p;
    }, {});
  }

  private getPropertyValue(property: keyof PositioningInfo) {
    if (!(property in this.propertyValueAccessors)) {
      throw new Error(
        `Property ${property} is not supported by ${SyncPositionSourceDirective.name}`,
      );
    }
    return this.propertyValueAccessors[property](this.el);
  }

  ngOnDestroy(): void {
    this.resizeObserver.disconnect();
    this.destroy$$.next();
    this.destroy$$.complete();
  }
}
