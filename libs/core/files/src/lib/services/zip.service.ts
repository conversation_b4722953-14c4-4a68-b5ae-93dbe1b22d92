import { Injectable } from '@angular/core';
import { StrictHttpResponse } from '@fincloud/swagger-generator/handelsregister';
import { fs, ZipDirectoryEntry } from '@zip.js/zip.js';
import { groupBy, keyBy, last, partition } from 'lodash-es';
import { defer, from, mergeMap } from 'rxjs';
import { SucceededDocumentDownload } from '../models/succeeded-document-download';
import { FileService } from './file.service';

@Injectable({
  providedIn: 'root',
})
export class ZipService {
  constructor(private fileService: FileService) {}

  async zipFiles(
    documentDownloads: SucceededDocumentDownload[],
    zipName: string,
  ) {
    const zipFs = new fs.FS();
    const root = zipFs.root.addDirectory(zipName);

    const [standAloneDocuments, referencedDocuments] = partition(
      documentDownloads,
      (d) => !d.referenced,
    );

    const groups = groupBy(standAloneDocuments, (d) =>
      d.document.levels.join(),
    );
    const referencesDocsById = keyBy(referencedDocuments, (d) => d.document.id);
    const foldersByName: Map<string, ZipDirectoryEntry> = new Map();

    Object.values(groups).forEach((docDownloads) => {
      const docFolderNameCounts: Map<string, number> = new Map();

      for (const d of docDownloads) {
        d.document.levels.reduce((parent, level) => {
          return this.createDirectory(level, parent, foldersByName);
        }, root);

        if (d.document.references?.length > 0) {
          // Document with referenced documents should be isolated in a separate folder
          const fileName = this.getFileName(d.response, docFolderNameCounts);
          const folder =
            foldersByName.get(this.getValidPath(last(d.document.levels))) ||
            root;

          const docWithRefsFolder = folder.addDirectory(fileName);
          docWithRefsFolder.addBlob(fileName, d.response.body);

          d.document.references.forEach((r) => {
            const referenceName = this.getFileName(
              referencesDocsById[r.id]?.response,
              docFolderNameCounts,
            );
            docWithRefsFolder.addBlob(
              referenceName,
              referencesDocsById[r.id]?.response.body,
            );
          });
        } else {
          // Document with no referenced documents should appear on it's own
          const fileName = this.getFileName(d.response, docFolderNameCounts);
          const folder =
            foldersByName.get(this.getValidPath(last(d.document.levels))) ||
            root;
          folder.addBlob(fileName, d.response.body);
        }
      }
    });

    const zipTaskResults = await root.exportBlob();

    const blobURL = this.fileService.getObjectUrl(
      zipTaskResults,
      'application/zip',
    );

    this.fileService.downloadUrl(blobURL, zipName);
  }

  /**
   * Get filename from response with optional suffix if such already exists in the give folder
   */
  getFileName(
    documentResponse: StrictHttpResponse<Blob>,
    fileNameCounts?: Map<string, number>,
  ) {
    const header = documentResponse.headers.get('Content-Disposition');
    const fileName = this.getValidPath(
      this.fileService.getCompanyDocumentFileName(header),
    );

    let suffix = '';
    if (fileNameCounts) {
      if (fileNameCounts.has(fileName)) {
        fileNameCounts.set(fileName, fileNameCounts.get(fileName) + 1);
      } else {
        fileNameCounts.set(fileName, 1);
      }

      suffix =
        fileNameCounts.get(fileName) === 1
          ? ''
          : '_' + fileNameCounts.get(fileName).toString();
    }

    return fileName + suffix;
  }

  zipFileSystemEntry(entry: FileSystemEntry) {
    const zipFs = new fs.FS();
    return defer(() =>
      from(zipFs.root.addFileSystemEntry(entry)).pipe(
        mergeMap(() => zipFs.root.exportBlob()),
      ),
    );
  }

  /**
   * Create directory if not already there
   */
  private createDirectory(
    level: string,
    parent: ZipDirectoryEntry,
    foldersByName: Map<string, ZipDirectoryEntry>,
  ) {
    const folderName = this.getValidPath(level);

    if (foldersByName.get(folderName)) {
      return foldersByName.get(folderName);
    }

    const folder = parent.addDirectory(folderName);
    foldersByName.set(folderName, folder);
    return folder;
  }

  /**
   * Remove characters that are invalid for a file/folder path
   */
  private getValidPath(fileName: string) {
    const illegalForFilePaths = /[\\/?<>:*|"]/g;
    fileName = fileName.replace(illegalForFilePaths, '');
    return fileName;
  }
}
