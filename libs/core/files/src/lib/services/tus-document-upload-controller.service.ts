import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { TusDocumentUploadConfiguration } from '@fincloud/core/config';
import { BaseService } from '@fincloud/swagger-generator/application';

@Injectable({
  providedIn: 'root',
})
export class TusDocumentUploadController extends BaseService {
  constructor(config: TusDocumentUploadConfiguration, http: HttpClient) {
    super(config, http);
  }

  getUploadBusinessCaseDocumentPath(businessCaseId: string) {
    return `${this.rootUrl}/business-case/${businessCaseId}`;
  }

  getUploadChatDocumentPath(customerKey: string) {
    return `${this.rootUrl}/document-floating-content/${customerKey}`;
  }

  getUploadCompanyDocumentPath(companyId: string) {
    return `${this.rootUrl}/company/${companyId}`;
  }

  getUploadOrganizationLogoPath(customerKey: string) {
    return `${this.rootUrl}/organization-logo/${customerKey}`;
  }

  getUploadContractPath(customerKey: string) {
    return `${this.rootUrl}/contract/${customerKey}`;
  }

  getUploadDocumentContentPath(documentId: string) {
    return `${this.rootUrl}/document-contenent/${documentId}`;
  }

  getUploadDocumentFloatingContentPath(customerKey: string) {
    return `${this.rootUrl}/document-floating-contenent/${customerKey}`;
  }

  getUploadEmailAttachmentDocumentPath(businessCaseId: string) {
    return `${this.rootUrl}/email-attachment/${businessCaseId}`;
  }

  getUploadInboxDocumentPath(businessCaseId: string) {
    return `${this.rootUrl}/document-inbox/${businessCaseId}`;
  }

  getUploadInboxZipDocumentPath(businessCaseId: string) {
    return `${this.rootUrl}/document-inbox-zip/${businessCaseId}`;
  }

  getUploadProfilePicturePath(userId: string) {
    return `${this.rootUrl}/profile-picture/${userId}`;
  }
}
