import { Injectable } from '@angular/core';
import { TokenManagementService } from '@fincloud/core/auth';
import { SocketService, SocketType } from '@fincloud/core/socket';
import { AccessTokenStatus, UploadStatus } from '@fincloud/types/enums';
import { UploadResponseNotification } from '@fincloud/types/models';
import { Observable, filter, tap } from 'rxjs';
import { DetailedError, HttpRequest, Upload } from 'tus-js-client';

import {
  clearIntervalUnpatched,
  setIntervalUnpatched,
} from '@fincloud/core/utils';
import { UPLOAD_DESTINATION } from '@fincloud/utils';
import { UploadConfig } from '../models/upload-file-config';

@Injectable({
  providedIn: 'root',
})
export class FileManagerService {
  private RETRY_DELAYS = [0, 3000, 6000, 12000, 24000];

  constructor(
    private tokenManagementService: TokenManagementService,
    private socketService: SocketService,
  ) {}

  upload(config: UploadConfig): Observable<UploadResponseNotification> {
    return this.startUpload(config);
  }

  private startUpload(config: UploadConfig) {
    this.createUpload(config).start();
    return this.handleSocketCommunication(config.uploadId);
  }

  private createUpload({
    file,
    endpoint,
    businessCaseId,
    typeOfTusUpload,
    customerKey,
    uploadId,
    docType,
  }: UploadConfig) {
    return new Upload(file, {
      retryDelays: this.RETRY_DELAYS,
      endpoint,
      chunkSize: 20_000_000, // this is 20 mb,
      removeFingerprintOnSuccess: true,
      parallelUploads: this.calculateParallelUploadsConnections(file?.size),
      metadata: {
        filename: (file as File)?.name || '',
        filetype: file.type,
        businessCaseId: businessCaseId || '',
        customerKey: customerKey || '',
        typeOfTusUpload,
        docType: docType || '',
        /* We will filter the websocket upload events based on this property and the event type */
        uploadId,
      },
      /* We should always attach the latest token in case the initial Token expires */
      onBeforeRequest: (request: HttpRequest) => {
        return new Promise((resolve) => {
          const token = this.tokenManagementService.getToken(customerKey);
          if (token.status === AccessTokenStatus.VALID) {
            const accessToken = `Bearer ${
              token?.tokenRaw?.accessToken as string
            }`;

            request.setHeader('Authorization', accessToken);
            resolve();
          } else {
            const tokenCheckInterval = setIntervalUnpatched(() => {
              const token = this.tokenManagementService.getToken(customerKey);

              if (token.status === AccessTokenStatus.VALID) {
                clearIntervalUnpatched(tokenCheckInterval);
                const accessToken = `Bearer ${
                  token?.tokenRaw?.accessToken as string
                }`;

                request.setHeader('Authorization', accessToken);
                resolve();
              }
            }, 100);
          }
        });
      },
      onShouldRetry(error) {
        const detailedError = error as DetailedError;
        return (
          detailedError?.originalResponse.getStatus() !== 400 &&
          detailedError?.originalResponse.getStatus() !== 500
        );
      },
    });
  }

  private handleSocketCommunication(currentUploadId: string) {
    return this.socketService
      .getMessagesByDestination$<UploadResponseNotification>(
        UPLOAD_DESTINATION,
        SocketType.PLATFORM_NOTIFICATION,
      )
      .pipe(
        filter(({ uploadId }) => uploadId === currentUploadId),
        tap(({ uploadStatus, errorReason }) => {
          if (uploadStatus === UploadStatus.FAILED) {
            throw new Error(errorReason || 'Upload failed');
          }
        }),
        filter(({ uploadStatus }) => uploadStatus === UploadStatus.COMPLETED),
      );
  }

  private calculateParallelUploadsConnections(fileSize: number): number {
    const maxConnections = 5;
    const minPerConnections = 60000000; // every connection should upload at least 60mb
    let connections = Math.ceil(fileSize / minPerConnections) || 2;

    if (connections < 2) {
      // The BE does not support 1 connection
      connections = 2;
    }

    return connections > maxConnections ? maxConnections : connections;
  }
}
