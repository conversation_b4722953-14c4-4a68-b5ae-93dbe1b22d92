import { TusUploadType } from '@fincloud/types/enums';

export interface UploadConfig {
  file: File | Blob;
  uploadId: string;
  typeOfTusUpload: TusUploadType;
  businessCaseId?: string;
  endpoint: string;
  customerKey?: string;
  docType?:
    | 'DATA_ROOM'
    | 'CHAT'
    | 'COMPANY'
    | 'CONTRACT'
    | 'FLOATING'
    | 'DOCUMENT_CONTENT'
    | 'FLOATING_CONTENT'
    | 'EMAIL_ATTACHMENT'
    | 'INBOX'
    | 'INBOX_ZIP'
    | 'ORGANIZATION_LOGO'
    | 'BUSINESS_CASE'
    | 'PROFILE_PICTURE';

  //TODO change docType DATA_ROOM and CHAT
}
