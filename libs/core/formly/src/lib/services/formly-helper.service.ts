import { Injectable } from '@angular/core';
import { FieldDtoWithIndex, FieldType } from '@fincloud/core/business-case';
import { Template } from '@fincloud/swagger-generator/business-case-manager';
import { FacilityFieldViewModel } from '@fincloud/types/models';
import { FormlyFieldConfig } from '@ngx-formly/core';
import { FieldTypeEnum } from '../enums/field-type-enum';
import { TemplateSelectOption } from '../models/template-select-option';

@Injectable({
  providedIn: 'root',
})
export class FormlyHelperService {
  convertTemplateToFormlyStructure(
    template: Template,
    fieldOptions: unknown,
    excludeFieldKeys: string[],
  ): FormlyFieldConfig[] {
    return this.getFormlyFieldsConfig(
      template.fields
        .filter((f) => f.isRequired)
        .filter((f) => !excludeFieldKeys.includes(f.key)),
      fieldOptions,
    );
  }

  convertFacilitiesToFormlyStructure(
    facilities: FacilityFieldViewModel[],
  ): FormlyFieldConfig[] {
    return [
      {
        fieldGroup: facilities.map((f) =>
          this.convertSingleFacilityFieldToFormlyFieldConfig(f),
        ),
        fieldGroupClassName: 'field-group',
      },
    ];
  }

  getFormlyFieldsConfig(fields: FieldDtoWithIndex[], fieldOptions: unknown) {
    return [
      {
        fieldGroup: fields?.map((f) =>
          this.convertSingleFieldToFormlyFieldConfig(f, fieldOptions, fields),
        ),
        fieldGroupClassName: 'field-group',
      },
    ];
  }

  private convertSingleFacilityFieldToFormlyFieldConfig(
    field: FacilityFieldViewModel,
  ): FormlyFieldConfig {
    const type = field.fieldType;

    const formField: FormlyFieldConfig = {
      key: field.id,
      type: this.convertTemplateTypeToFormlyType(type as FieldType),
      templateOptions: {
        label: field.label,
        required: false,
        attributes: {
          valueFormatting: type.toLocaleLowerCase(),
        },
        fieldOptions: {},
        fieldDto: field,
      },
      wrappers: ['basic'],
      className: 'field',
    };

    if (formField.type === 'select') {
      formField.templateOptions.options = (field.fieldMetaData as []).map(
        (data) => {
          return {
            name: data,
            value: data,
          };
        },
      );
    }

    return formField;
  }

  private setDropdownPositionToTop(
    // The main idea behind this function is to set the dropdown position to 'top', when there is not enough space to place it at the bottom
    fields: FieldDtoWithIndex[],
    currentField: FieldDtoWithIndex,
    formField: FormlyFieldConfig,
  ): void {
    fields.forEach((field, i: number) => {
      if (field === currentField && i + 2 >= fields.length) {
        formField.templateOptions.fieldOptions.dropdownPosition = 'top';
      }
    });
  }

  private convertSingleFieldToFormlyFieldConfig(
    field: FieldDtoWithIndex,
    fieldOptions: unknown,
    fields: FieldDtoWithIndex[],
    keyFn = (f: FieldDtoWithIndex) => f.key,
  ): FormlyFieldConfig {
    const type = field.fieldType;

    const formField: FormlyFieldConfig = {
      key: keyFn(field),
      type: this.convertTemplateTypeToFormlyType(type as FieldType),
      templateOptions: {
        label: field.isRequired ? field.label + '*' : field.label,
        required: field.isRequired,
        attributes: {
          valueFormatting: type.toLocaleLowerCase(),
        },
        fieldOptions,
        fieldDto: field,
      },
      wrappers: ['basic'],
      className: 'field',
    };

    if (formField.type === 'select') {
      this.setDropdownPositionToTop(fields, field, formField);
      formField.templateOptions.options = (field.fieldMetaData as []).map(
        (data) => {
          return {
            name: data,
            value: data,
          };
        },
      );
    }

    if (formField.type === 'composite') {
      formField.templateOptions.options = (field.fieldMetaData as []).map(
        (data) => {
          return {
            label: data,
            value: data,
          };
        },
      );
    }

    return formField;
  }

  mapToSelectOption(template: Template): TemplateSelectOption {
    return {
      name: template.name,
      template: template,
      type: template.financingStructureType,
    };
  }

  private convertTemplateTypeToFormlyType(templateType: FieldType): string {
    switch (templateType) {
      case FieldTypeEnum.SHORT_TEXT:
        return 'input';
      case FieldTypeEnum.LONG_TEXT:
        return 'textarea';
      case FieldTypeEnum.INTEGER:
      case FieldTypeEnum.PERCENT:
      case FieldTypeEnum.DECIMAL:
      case FieldTypeEnum.MONTHS:
      case FieldTypeEnum.MONETARY:
        return 'number';
      case FieldTypeEnum.DATE:
        return 'date';
      case FieldTypeEnum.DOCUMENT:
        return 'file-upload';
      case FieldTypeEnum.SELECT:
        return 'select';
      case FieldTypeEnum.BOOLEAN:
        return 'boolean';
      case FieldTypeEnum.LOCATION:
        return 'location';
      case FieldTypeEnum.COMPOSITE:
        return 'composite';
      default:
        return '';
    }
  }
}
