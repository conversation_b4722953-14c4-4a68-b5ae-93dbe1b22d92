import {
  DestroyRef,
  Directive,
  ElementRef,
  Inject,
  Input,
  OnDestroy,
  OnInit,
  Renderer2,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NgbTooltip } from '@ng-bootstrap/ng-bootstrap';
import { TOOLTIP_CLOSE_DELAY } from '../utils/tooltip-close-delay';
import { TooltipContainerDirective } from './tooltip-container.directive';

/**
 * This directive will open a tooltip on hover and also provide a context object to the tooltip template
 * Therefore it's not useful if the tooltip uses primitive string value instead of a template
 * It has to be applied on an element which also has NgbTooltip directive applied
 *
 * @example
 * <div [ngbTooltip]="edgeTooltip"
 appTooltipTrigger
 [context]="link.data.tooltipData">
 * ...
 */

@Directive({
  selector: '[appTooltipTrigger]',
  exportAs: 'appTooltipTrigger',
})
export class TooltipTriggerDirective implements OnInit, OnD<PERSON>roy {
  @Input()
  context: unknown;

  @Input()
  tooltipEnabled = true;

  @Input()
  trigger: 'hover' | 'click' = 'hover';

  private listeners: (() => void)[] = [];
  private isOpen = false;

  constructor(
    private destroyRef: DestroyRef,
    private renderer2: Renderer2,
    private hostElement: ElementRef<HTMLElement>,
    private ngbtooltip: NgbTooltip,
    @Inject(TooltipContainerDirective)
    private container: TooltipContainerDirective,
  ) {}

  ngOnInit() {
    if (this.trigger === 'hover') {
      this.listeners.push(
        this.renderer2.listen(
          this.hostElement.nativeElement,
          'mouseenter',
          () => {
            if (!this.tooltipEnabled) {
              return;
            }
            if (!this.isOpen) {
              this.isOpen = true;
              this.openTooltip();
            }
          },
        ),
      );
      this.listeners.push(
        this.renderer2.listen(
          this.hostElement.nativeElement,
          'mouseleave',
          () => {
            if (!this.tooltipEnabled) {
              return;
            }
            if (this.isOpen) {
              // without delay the UX is worse since the tooltip becomes too sensitive to mouse movements and flickers
              setTimeout(() => {
                this.isOpen = false;
                this.ngbtooltip.close();
              }, TOOLTIP_CLOSE_DELAY);
            }
          },
        ),
      );
    } else {
      this.listeners.push(
        this.renderer2.listen(this.hostElement.nativeElement, 'click', () => {
          if (!this.tooltipEnabled) {
            return;
          }
          if (!this.isOpen) {
            this.isOpen = true;
            this.openTooltip();
          }
        }),
      );

      this.ngbtooltip.hidden
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe({
          next: () => {
            this.isOpen = false;
          },
        });
    }
  }

  ngOnDestroy() {
    this.listeners.forEach((listener) => listener());
  }

  get tooltipWindow() {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-explicit-any
    return (this.ngbtooltip as any)._windowRef.location.nativeElement;
  }

  get containerForTooltip() {
    return this.container.hostElement.nativeElement;
  }

  private openTooltip() {
    this.ngbtooltip.open({ $implicit: this.context });
    // move element under specified container until support for container property other than 'body' comes along
    // this helps with tooltip auto-orientation and fullscreen mode stacking context
    this.containerForTooltip.appendChild(this.tooltipWindow);
  }
}
