import { Directive, ElementRef } from '@angular/core';

/**
 * Marks an element as container for tooltip
 *
 * @example
 * <div appTooltipContainer>
 ...
 <div appTooltipTrigger [ngbTooltip]="edgeTooltip">
 */

@Directive({
  selector: '[appTooltipContainer]',
  exportAs: 'appTooltipContainer',
})
export class TooltipContainerDirective {
  constructor(public hostElement: ElementRef<HTMLElement>) {}
}
