import { retryBackoff } from 'backoff-rxjs';

/**
 * Retry source observable on error.
 * Intervals get increasingly longer until maxRetries is reached, when error is finally emitted downstream
 * Last attempt will be made at: Math.pow(2, iteration) * initialInterval
 * Last attempt for 5 retries with 1 sec initial interval => 32 sec
 * Last attempt for 3 retries with 1 sec initial interval => 8 sec
 *
 * */
export const retryCall = (options?: {
  initialInterval?: number;
  maxRetries?: number;
  shouldRetry?: (error: Error) => boolean;
}) => {
  const mergedOptions = {
    maxRetries: 3,
    initialInterval: 1000,
    shouldRetry: (_error: Error) => {
      return true;
    },
    ...options,
  };
  return retryBackoff({
    initialInterval: mergedOptions.initialInterval,
    maxRetries: mergedOptions.maxRetries,
    resetOnSuccess: true,
    shouldRetry: mergedOptions.shouldRetry,
  });
};
