import { <PERSON>pe, PipeTransform, SecurityContext } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';

@Pipe({
  name: 'boldSubstring',
})
export class BoldSubstringPipe implements PipeTransform {
  constructor(private sanitizer: DomSanitizer) {}

  transform(value: string, substring: string) {
    return this.sanitize(this.replace(value, substring));
  }

  replace(str: string, substring: string) {
    // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
    return str
      ? str.replace(
          new RegExp(`(${substring})`, 'gi'),
          '<b><b><b>$1</b></b></b>',
        )
      : '';
  }

  sanitize(str: string) {
    return this.sanitizer.sanitize(SecurityContext.HTML, str);
  }
}
