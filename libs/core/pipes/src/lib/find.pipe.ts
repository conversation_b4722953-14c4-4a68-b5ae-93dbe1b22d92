import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'find',
})
export class FindPipe<T> implements PipeTransform {
  transform(
    value: string,
    collection: (Record<string, T> | string)[],
    key?: keyof Record<string, T>,
  ) {
    return collection.find((entity) =>
      key && typeof entity === 'object'
        ? entity[key] === value
        : entity === value,
    );
  }
}
