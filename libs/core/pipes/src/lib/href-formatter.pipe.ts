import { Pipe, PipeTransform } from '@angular/core';

/**
 * @description Formats a given website address so that it could be correctly opened when passed as a href attribute to an anchor tag.
 * By default, anchor tags cannot open links without protocol attached to them (e.g. "example.com") so it needs to be converted to "//example.com" so
 * that the browser knows how to open it correctly, instead of trying to attach it to the current url.
 *
 * @see https://developer.mozilla.org/en-US/docs/Web/HTML/Element/a
 *
 * @param url The url that needs to be formatted.
 *
 * @returns The formatted url. If it's passed with a protocol, it would remain unchanged. If it's passed without a protocol, it would be prefixed with "//".
 * */
@Pipe({ name: 'href' })
export class HrefFormatterPipe implements PipeTransform {
  transform(url: string): string {
    return url && !url.startsWith('http') ? `//${url}` : url;
  }
}
