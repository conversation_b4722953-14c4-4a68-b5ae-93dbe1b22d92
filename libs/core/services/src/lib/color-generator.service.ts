/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/restrict-plus-operands */
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ColorGeneratorService {
  private static hexToRgb(hex: string): { r: number; b: number; g: number } {
    const shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
    hex = hex.replace(shorthandRegex, (m, r, g, b) => {
      return r + r + g + g + b + b;
    });

    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : null;
  }

  private static rgbToHex(r: number, g: number, b: number): string {
    return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
  }

  private static ensureHexColorHighContrast(hex: string): string {
    const rgb = ColorGeneratorService.hexToRgb(hex);
    const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
    hex = hex.replace(/^#/, '');
    return brightness < 120
      ? `#${hex}`
      : this.changeHexLightness(hex, -Math.round(brightness / 2));
  }

  private static changeHexLightness(color: any, amount: number): string {
    color = color.replace(/^#/, '');
    if (color.length === 3) {
      color = color[0] + color[0] + color[1] + color[1] + color[2] + color[2];
    }

    let [r, g, b] = color.match(/.{2}/g);
    [r, g, b] = [
      parseInt(r, 16) + amount,
      parseInt(g, 16) + amount,
      parseInt(b, 16) + amount,
    ];

    r = Math.max(Math.min(255, +r), 0).toString(16);
    g = Math.max(Math.min(255, +g), 0).toString(16);
    b = Math.max(Math.min(255, +b), 0).toString(16);

    const rr = (r.length < 2 ? '0' : '') + r;
    const gg = (g.length < 2 ? '0' : '') + g;
    const bb = (b.length < 2 ? '0' : '') + b;

    return `#${rr}${gg}${bb}`;
  }

  public generateHexColor(object: unknown, onlyHighContrast = true): string {
    const SEED = 16777215;
    const FACTOR = 69979693;

    const text = String(JSON.stringify(object));
    let b = 1;
    let d = 0;
    let f = 1;
    for (const char of text) {
      const charCode = char.charCodeAt(0);
      if (charCode > d) {
        d = char.charCodeAt(0);
      }
      f = parseInt(SEED / d + '', 10);
      b = (b + charCode * f * FACTOR) % SEED;
    }
    let hex = ((b * text.length) % SEED).toString(16);
    hex = hex.padEnd(6, hex);
    return onlyHighContrast
      ? ColorGeneratorService.ensureHexColorHighContrast(hex)
      : `#${hex}`;
  }

  public generateHslColor(
    object: unknown,
    saturationPercent = 100,
    lightnessPercent = 35,
  ): string {
    let hue = 0;
    const text = String(JSON.stringify(object));
    for (const char of text) {
      hue += char.charCodeAt(0);
    }
    const hueMod360 = hue % 360;

    return `hsl(${hueMod360}, ${saturationPercent}%, ${lightnessPercent}%)`;
  }
}
