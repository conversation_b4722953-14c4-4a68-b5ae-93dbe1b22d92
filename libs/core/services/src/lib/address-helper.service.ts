import { Injectable } from '@angular/core';
import { BusinessCaseLocation } from '@fincloud/core/formly';
import { Address as CustomerAddress } from '@fincloud/swagger-generator/authorization-server';
import { Address as CompanyAddress } from '@fincloud/swagger-generator/company';

@Injectable({
  providedIn: 'root',
})
export class AddressHelperService {
  asCustomerAddressString(address: CustomerAddress): string {
    if (!address) {
      return '';
    }
    return [
      address.streetNameAndNumber,
      [address.postalCode, address.cityName].filter(Boolean).join(' '),
    ]
      .filter(Boolean)
      .join(', ');
  }

  asCompanyAddressString(
    address: CompanyAddress,
    options = { includeCountry: true },
  ): string {
    if (!address) {
      return '';
    }
    const streetAddress = address.street.trim() ? `${address.street},` : '';
    const postalCode = address.postalCode ? `${address.postalCode}` : '';
    const city = address.city ? `${address.city}` : '';

    return `${streetAddress} ${postalCode} ${city} ${
      options.includeCountry ? address.country : ''
    }`;
  }

  isAddress(
    address: CustomerAddress | CompanyAddress | unknown,
  ): address is CustomerAddress {
    return (<CustomerAddress>address)?.postalCode !== undefined;
  }

  asFormlyLocationString(location: BusinessCaseLocation) {
    if (!location || !location.address) {
      return '';
    }
    return this.formatAddress(location);
  }

  formatAddress(location: BusinessCaseLocation) {
    const addressInfo = [
      location.address.street,
      location.address.city,
      location.address.zip,
      location.address.state,
      location.address.country,
    ];

    return addressInfo
      .map((address) => (address ? address.trim() : ''))
      .filter(Boolean)
      .join(', ');
  }
}
