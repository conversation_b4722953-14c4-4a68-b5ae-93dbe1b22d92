import { DOCUMENT } from '@angular/common';
import { Inject, Injectable, Renderer2, RendererFactory2 } from '@angular/core';

/**
 * Css helper class.
 * @class
 */
@Injectable({
  providedIn: 'root',
})
export class CssService {
  private renderer: Renderer2;
  constructor(
    @Inject(DOCUMENT) private document: Document,
    private rendererFactory: RendererFactory2,
  ) {
    this.renderer = rendererFactory.createRenderer(null, null);
  }

  /**
   * Add css class to dom element.
   * @param {string} elementId - id of the dom element.
   * @param {string} className - name of the class to be added to the dom element.
   * @param {HTMLElement} element - reference to the dom element. OPTIONAL
   */
  addCssClass(
    elementId: string,
    className: string,
    element?: HTMLElement,
  ): void {
    if (element) {
      return this.renderer.addClass(element, className);
    }
    const domElement = this.document.getElementById(elementId);
    if (domElement) {
      this.renderer.addClass(domElement, className);
    }
  }

  /**
   * Add css class to dom element.
   * @param {string} elementId - id of the dom element.
   * @param {string} className - name of the class to which will be removed from the dom element.
   * @param {HTMLElement} element - reference to the dom element. OPTIONAL
   */
  removeCssClass(
    elementId: string,
    className: string,
    element?: HTMLElement,
  ): void {
    if (element) {
      return this.renderer.removeClass(element, className);
    }
    const domElement = elementId
      ? this.document.getElementById(elementId)
      : this.document.querySelector('.' + className);

    if (domElement) {
      this.renderer.removeClass(domElement, className);
    }
  }
}
