export class CollectionUtils {
  /**
   * Inserts provided element inbetween each existing array members
   * @param array
   * @param elementToInsert
   */
  public static insertBetweenEachElement<T>(
    array: T[],
    elementToInsert: T,
  ): T[] {
    if (!Array.isArray(array) || array.length === 0) {
      return [];
    }

    const [firstElement, ...rest] = array;

    return rest.reduce(
      (acc: T[], curr: T): T[] => acc.concat(elementToInsert, curr),
      [firstElement],
    );
  }
}
