import { ConnectedPosition, Overlay } from '@angular/cdk/overlay';
import {
  Directive,
  ElementRef,
  Input,
  OnDestroy,
  ViewContainerRef,
} from '@angular/core';
import { OverlayDirective } from './overlay.directive';

@Directive({
  selector: '[appMenuTrigger]',
})
export class MenuTriggerDirective
  extends OverlayDirective
  implements OnDestroy
{
  @Input()
  appMenuOffset = 20;

  @Input()
  isInverseArrow = false;

  @Input()
  inverseArrowOffset: number;

  @Input()
  showMenuBottom: boolean;

  @Input()
  showMenuBottomAndCenter: boolean;

  @Input()
  showMenuBottomAndLeft: boolean;

  constructor(
    overlay: Overlay,
    elementRef: ElementRef<HTMLElement>,
    viewContainerRef: ViewContainerRef,
  ) {
    super(overlay, elementRef, viewContainerRef);
  }

  getPositions(): ConnectedPosition[] {
    if (this.showMenuBottom) {
      return [
        {
          originX: 'start',
          originY: 'top',
          overlayX: 'start',
          overlayY: 'top',
          offsetX: 0,
          offsetY: 30,
        },
      ];
    }

    if (this.isInverseArrow) {
      return [
        {
          originX: 'center',
          originY: 'top',
          overlayX: 'end',
          overlayY: 'top',
          offsetX: this.inverseArrowOffset,
        },
      ];
    }

    if (this.showMenuBottomAndCenter) {
      return [
        {
          originX: 'center',
          originY: 'top',
          overlayX: 'end',
          overlayY: 'top',
          offsetX: 20,
          offsetY: this.appMenuOffset,
        },
      ];
    }

    if (this.showMenuBottomAndLeft) {
      return [
        {
          originX: 'center',
          originY: 'top',
          overlayX: 'end',
          overlayY: 'top',
          offsetX: 20,
          offsetY: 30,
        },
      ];
    }

    return [
      {
        originX: 'end',
        originY: 'center',
        overlayX: 'start',
        overlayY: 'center',
        offsetX: this.appMenuOffset,
      },
    ];
  }
}
