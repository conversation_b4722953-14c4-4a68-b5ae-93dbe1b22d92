import {
  ConnectedPosition,
  Overlay,
  OverlayConfig,
} from '@angular/cdk/overlay';
import {
  Directive,
  ElementRef,
  HostListener,
  Input,
  OnDestroy,
  ViewContainerRef,
} from '@angular/core';
import { Observable, fromEvent, merge } from 'rxjs';
import { OverlayDirective } from './overlay.directive';

@Directive({
  selector: '[appHoverOverlayTrigger]',
})
export class HoverOverlayTriggerDirective
  extends OverlayDirective
  implements OnD<PERSON>roy
{
  @Input({ required: true })
  appOverlayPositions: ConnectedPosition[];

  constructor(
    overlay: Overlay,
    elementRef: ElementRef<HTMLElement>,
    viewContainerRef: ViewContainerRef,
  ) {
    super(overlay, elementRef, viewContainerRef);
  }

  @HostListener('mouseenter', ['$event'])
  onHover(event: Event) {
    event.stopPropagation();
    this.openOverlay();
  }

  override openOverlay(): void {
    if (this.isOverlayOpen) {
      return;
    }
    super.openOverlay();
  }

  protected override dropdownClosingActions(): Observable<MouseEvent | void> {
    const mouseLeave$ = fromEvent<MouseEvent>(
      this.elementRef.nativeElement,
      'mouseleave',
    );
    const detachment$ = this.overlayRef.detachments();

    return merge(mouseLeave$, detachment$);
  }

  protected override getConfigOverrides(): Partial<OverlayConfig> {
    return {
      hasBackdrop: false,
      backdropClass: undefined,
    };
  }

  protected override getPositions(): ConnectedPosition[] {
    return this.appOverlayPositions;
  }

  protected override onHostClicked() {
    // Intentionally empty. Trigger is hover instead of click.
  }
}
