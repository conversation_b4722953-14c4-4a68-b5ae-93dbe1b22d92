import {
  ConnectedPosition,
  Overlay,
  OverlayConfig,
  OverlayRef,
} from '@angular/cdk/overlay';
import { TemplatePortal } from '@angular/cdk/portal';
import {
  Directive,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  OnDestroy,
  Output,
  TemplateRef,
  ViewContainerRef,
} from '@angular/core';
import { Observable, Subscription, merge } from 'rxjs';

@Directive({
  selector: '[appOverlayTrigger]',
})
export class OverlayDirective implements OnDestroy {
  @Input()
  appOverlayContent: TemplateRef<unknown>;

  @Output()
  closed = new EventEmitter();

  @Output()
  opened = new EventEmitter();

  protected isOverlayOpen = false;
  protected overlayRef: OverlayRef;
  protected overlayClosingActionsSub = Subscription.EMPTY;
  protected config: OverlayConfig;

  @HostListener('click', ['$event'])
  onClick(event: Event) {
    this.onHostClicked(event);
  }

  protected onHostClicked(event: Event) {
    event.stopPropagation();
    this.openOverlay();
  }

  get isOpen() {
    return this.isOverlayOpen;
  }

  constructor(
    protected overlay: Overlay,
    protected elementRef: ElementRef<HTMLElement>,
    protected viewContainerRef: ViewContainerRef,
  ) {}

  protected getPositions(): ConnectedPosition[] {
    throw new Error(
      'getPositions() should be implemented when extending OverlayDirective',
    );
  }

  protected getConfig() {
    const baseConfig = {
      hasBackdrop: true,
      backdropClass: 'cdk-overlay-transparent-backdrop',
      scrollStrategy: this.overlay.scrollStrategies.reposition(),
      positionStrategy: this.overlay
        .position()
        .flexibleConnectedTo(this.elementRef)
        .withPositions(this.getPositions()),
    };

    return { ...baseConfig, ...this.getConfigOverrides() };
  }

  protected getConfigOverrides(): Partial<OverlayConfig> {
    return {};
  }

  toggleOverlay(): void {
    this.isOverlayOpen ? this.destroyOverlay() : this.openOverlay();
  }

  openOverlay(): void {
    this.opened.emit();

    const config = this.getConfig();
    this.isOverlayOpen = true;
    this.overlayRef = this.overlay.create(config);

    const templatePortal = new TemplatePortal(
      this.appOverlayContent,
      this.viewContainerRef,
    );
    this.overlayRef.attach(templatePortal);

    this.overlayClosingActionsSub = this.dropdownClosingActions().subscribe(
      () => this.destroyOverlay(),
    );
  }

  protected dropdownClosingActions(): Observable<MouseEvent | void> {
    const backdropClick$ = this.overlayRef.backdropClick();
    const detachment$ = this.overlayRef.detachments();

    return merge(backdropClick$, detachment$);
  }

  protected destroyOverlay(): void {
    if (!this.overlayRef || !this.isOverlayOpen) {
      return;
    }

    this.overlayClosingActionsSub.unsubscribe();
    this.isOverlayOpen = false;
    this.overlayRef.detach();
    this.closed.emit();
  }

  ngOnDestroy(): void {
    if (this.overlayRef) {
      this.overlayRef.dispose();
    }
  }
}
