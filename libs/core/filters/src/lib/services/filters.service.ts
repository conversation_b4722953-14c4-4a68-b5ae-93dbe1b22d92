/* eslint-disable @typescript-eslint/restrict-template-expressions */
import { LabelType, Options } from '@angular-slider/ngx-slider';
import { formatCurrency, formatPercent } from '@angular/common';
import { Inject, Injectable, LOCALE_ID } from '@angular/core';
import {
  CustomerFilter,
  FilterList,
  MinMaxFilter,
} from '@fincloud/swagger-generator/watchlist';
import { Locale } from '@fincloud/types/enums';
import { Subject } from 'rxjs';
import { SliderFilterOptionsSettingsConfig } from '../models/slider-filter-options-settings-config';
import { VR_RATING_SCALA } from '../utils/vr-rating-scala';

@Injectable({
  providedIn: 'root',
})
export class FiltersService {
  constructor(@Inject(LOCALE_ID) private locale: Locale) {}
  private sliderFilterOptionsSettingsConfig: SliderFilterOptionsSettingsConfig =
    {
      volume: {
        step: 1000,
        translateExpanded: (value: number) =>
          `${formatCurrency(
            Number(value),
            this.locale,
            '€',
            'symbol',
            '1.2-2',
          )}`,
        translateCollapsed: (floor: number, ceil: number) =>
          `${formatCurrency(
            Number(floor),
            this.locale,
            '€',
            'symbol',
            '1.2-2',
          )} - ${formatCurrency(
            Number(ceil),
            this.locale,
            '€',
            'symbol',
            '1.2-2',
          )}`,
      },
      financingTerm: {
        step: 1,
        translateExpanded: (value: number) => `${value} mon.`,
        translateCollapsed: (floor: number, ceil: number) =>
          `${floor} - ${ceil} Monate`,
      },
      financingInterest: {
        step: 0.01,
        translateExpanded: (value: number) =>
          `${formatPercent(
            parseFloat(String(value) || '0') / 100,
            this.locale,
            '1.2-2',
          )}`,
        translateCollapsed: (floor: number, ceil: number) =>
          `${formatPercent(
            parseFloat(String(floor) || '0') / 100,
            this.locale,
            '1.2-2',
          )} -${formatPercent(
            parseFloat(String(ceil) || '0') / 100,
            this.locale,
            '1.2-2',
          )}`,
      },
      distanceCustomerToCustomer: {
        step: 10,
        translateExpanded: (value: number) => `${value} km`,
        translateCollapsed: () => '',
      },
      distanceCustomerToInvestmentLocation: {
        step: 10,
        translateExpanded: (value: number) => `${value} km`,
        translateCollapsed: () => '',
      },
      vrRating: {
        step: 1,
        translateExpanded: (value: number) => `${VR_RATING_SCALA[value].label}`,
        translateCollapsed: () => '',
      },
    };

  private filtersChanged$$ = new Subject<MinMaxFilter | CustomerFilter>();
  filtersChanged$ = this.filtersChanged$$.asObservable();

  filtersChange(filter: MinMaxFilter | CustomerFilter) {
    this.filtersChanged$$.next(filter);
  }

  getFilterSettingsOptions(
    key: string,
    filtersRange: FilterList,
    isExpanded: boolean,
    filterList?: FilterList,
  ): Options {
    const currentFilterValues =
      filterList && (filterList[key as keyof FilterList] as MinMaxFilter);
    const range = filtersRange[key as keyof FilterList] as MinMaxFilter;
    return {
      floor: range.min,
      ceil: range.max,
      step: this.sliderFilterOptionsSettingsConfig[key].step,
      animate: false,
      translate: (_: unknown, label: LabelType): string => {
        switch (label) {
          case LabelType.Floor:
            if (key === 'volume') {
              return this.sliderFilterOptionsSettingsConfig[
                key
              ].translateCollapsed(
                currentFilterValues?.min ?? range.min,
                currentFilterValues?.max ?? range.max,
              );
            }
            return isExpanded
              ? this.sliderFilterOptionsSettingsConfig[key].translateExpanded(
                  currentFilterValues?.min ?? range.min,
                )
              : this.sliderFilterOptionsSettingsConfig[key].translateCollapsed(
                  currentFilterValues?.min ?? range.min,
                  currentFilterValues?.max ?? range.max,
                );
          case LabelType.Ceil:
            if (key === 'volume') {
              return '';
            }
            return isExpanded
              ? this.sliderFilterOptionsSettingsConfig[key].translateExpanded(
                  currentFilterValues?.max ?? range.max,
                )
              : '';
          default:
            return '';
        }
      },
    };
  }
}
