import {
  Directive,
  EventEmitter,
  HostListener,
  Input,
  Output,
} from '@angular/core';
import { ZipService } from '@fincloud/core/files';
import { Toast } from '@fincloud/core/toast';
import { FinToastService } from '@fincloud/ui/toast';
import { Observable, defer, forkJoin, from, take, tap } from 'rxjs';

@Directive({
  selector: '[appDragDrop]',
  exportAs: 'appDragDrop',
})
export class DragDropDirective {
  @Input()
  appDragDropEnabled = true;

  @Input() allowedMimeType: string[];

  @Input()
  foldersDragDropEnabled = false;

  @Output() singleFileDropped = new EventEmitter<File>();
  @Output() multipleFilesDropped = new EventEmitter<File[]>();

  dragOver: boolean;
  toastErrorUnsupportedFileTypePrefix = $localize`:@@fileUploader.toast.error.unsupportedType.prefix:Nicht unterstützter Dateityp. Unterstützte Dateitypen sind:`;

  constructor(
    private zipService: ZipService,
    private finToastService: FinToastService,
  ) {}

  @HostListener('body:dragover', ['$event'])
  dragover(event: DragEvent) {
    if (!this.appDragDropEnabled) {
      return;
    }
    this.startDragOver(event);
  }

  @HostListener('body:drop', ['$event'])
  bodyDrop(event: DragEvent) {
    if (!this.appDragDropEnabled) {
      return;
    }
    this.stopDragOver(event);
  }

  @HostListener('window:mouseout')
  mouseleave() {
    if (!this.appDragDropEnabled) {
      return;
    }
    this.dragOver = false;
  }

  @HostListener('drop', ['$event'])
  drop(event: DragEvent) {
    if (!this.appDragDropEnabled) {
      return;
    }
    this.stopDragOver(event);

    const { dataTransfer } = event;
    if (dataTransfer.items?.length && dataTransfer.items[0].kind === 'file') {
      if (this.foldersDragDropEnabled) {
        const fileSystemEntries = Array.from(dataTransfer.items).map((item) =>
          item.webkitGetAsEntry(),
        );
        const files = fileSystemEntries.filter((entry) => entry.isFile);
        const folders = fileSystemEntries.filter((entry) => entry.isDirectory);
        if (files.length) {
          const files$ = files.map((file) =>
            this.getFileFromFileSystemEntry(file as FileSystemFileEntry),
          );

          forkJoin(files$)
            .pipe(
              take(1),
              tap((files) => {
                files.length > 1
                  ? this.multipleFilesDropped.emit(files)
                  : this.singleFileDropped.emit(files[0]);
              }),
            )
            .subscribe();
        }

        if (folders.length) {
          for (const folder of folders) {
            this.zipService
              .zipFileSystemEntry(folder)
              .pipe(
                take(1),
                tap((zippedFolderBlob) => {
                  const zippedFolderFile = new File(
                    [zippedFolderBlob],
                    `${folder.name}.zip`,
                    {
                      type: 'application/zip',
                    },
                  );
                  this.singleFileDropped.emit(zippedFolderFile);
                }),
              )
              .subscribe();
          }
        }
      } else {
        const droppedFiles = Array.from(dataTransfer.files);
        const isFileTypeNotAllowed =
          this.allowedMimeType?.length &&
          droppedFiles.find(
            (file) => !this.allowedMimeType?.includes(file.type),
          );
        if (isFileTypeNotAllowed) {
          this.finToastService.show(
            Toast.error(
              `${
                this.toastErrorUnsupportedFileTypePrefix
              } ${this.allowedMimeType.join(', ')}.`,
            ),
          );
          return;
        }

        dataTransfer.files.length > 1
          ? this.multipleFilesDropped.emit(Array.from(dataTransfer.files))
          : this.singleFileDropped.emit(dataTransfer.files[0]);
      }
    }
  }

  private startDragOver(event: Event) {
    event.preventDefault();
    event.stopPropagation();
    if (!this.dragOver) {
      this.dragOver = true;
    }
  }

  private stopDragOver(event: Event) {
    event.preventDefault();
    this.dragOver = false;
  }

  private getFileFromFileSystemEntry(
    entry: FileSystemFileEntry,
  ): Observable<File> {
    return defer(() => {
      return from(
        new Promise((resolve, _reject) => {
          entry?.file((file: File) => resolve(file));
        }) as Promise<File>,
      );
    });
  }
}
