import { APP_BASE_HREF } from '@angular/common';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Inject, Injectable, LOCALE_ID } from '@angular/core';
import { ENVIRONMENT_TOKEN } from '@fincloud/core/config';
import { PublicCustomerResponse } from '@fincloud/swagger-generator/authorization-server';
import { AuthErrorCode, Locale, LoginErrorState } from '@fincloud/types/enums';
import { Environment, LocalEnvironment } from '@fincloud/types/models';
import { Observable } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';
import { TokenManagementService } from './token-management.service';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  constructor(
    protected http: HttpClient,
    @Inject(APP_BASE_HREF) private baseHref: string,
    @Inject(ENVIRONMENT_TOKEN)
    private environment: Environment | LocalEnvironment,
    @Inject(LOCALE_ID) public locale: Locale,
    private tokenManagementService: TokenManagementService,
  ) {}

  private AUTHORIZE_URL = '/api/authorization-server/oauth2/token';

  tokenObtain(
    code: string,
  ): Observable<{ access_token: string; refresh_token: string }> {
    const origin = window.location.origin;
    let baseHrefWithoutLocale = this.baseHref;
    // Oauth flow can work with only one redirect url, so we are sending the user to user-validation page with de as default locale and the guard on this page will fix the right locale
    if (this.locale !== Locale.DE) {
      baseHrefWithoutLocale = this.baseHref.replace(`${this.locale}/`, '');
    }

    const formData = new FormData();
    formData.append('code', code);
    formData.append('grant_type', 'authorization_code');
    formData.append(
      'redirect_uri',
      `${origin}${baseHrefWithoutLocale}login/user-validation`,
    );

    const queryOptions = {
      headers: new HttpHeaders().set(
        'Authorization',
        `Basic ${this.environment.oauthBasicAuthorization}`,
      ),
      withCredentials: true,
    };

    return this.http.post<{ access_token: string; refresh_token: string }>(
      this.AUTHORIZE_URL,
      formData,
      queryOptions,
    );
  }

  // TODO: Test this out!
  tokenRefresh(
    customerKey: string,
    refreshToken: string,
  ): Observable<{ access_token: string; refresh_token: string }> {
    const refreshRequestId = uuidv4();
    const formData = new FormData();
    formData.append('refresh_token', refreshToken);
    formData.append('grant_type', 'refresh_token');
    formData.append('id', refreshRequestId);

    this.tokenManagementService.updateRefreshRequestId(
      customerKey,
      refreshRequestId,
    );

    const queryOptions = {
      headers: new HttpHeaders().set(
        'Authorization',
        `Basic ${this.environment.oauthBasicAuthorization}`,
      ),
      withCredentials: true,
    };

    return this.http.post<{ access_token: string; refresh_token: string }>(
      this.AUTHORIZE_URL,
      formData,
      queryOptions,
    );
  }

  createCustomersMap(customers: PublicCustomerResponse[]) {
    return customers.reduce(
      (acc, customer) => {
        acc[customer.key] = customer;
        return acc;
      },
      {} as { [key: string]: PublicCustomerResponse },
    );
  }

  getForgotPasswordCodeErrorState(errorCode: string): LoginErrorState {
    let errorState = LoginErrorState.UNKNOWN;

    if (errorCode === AuthErrorCode.PASSWORD_RESET_CODE_EXPIRED) {
      errorState = LoginErrorState.RESET_PASSWORD_LINK_EXPIRED;
    }
    if (errorCode === AuthErrorCode.NO_SUCH_VALIDATION_CODE) {
      errorState = LoginErrorState.NO_SUCH_VALIDATION_PASSWORD_CODE;
    }

    return errorState;
  }
}
