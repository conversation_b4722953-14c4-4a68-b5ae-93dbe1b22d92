import { Injectable } from '@angular/core';
import { AuthenticationService } from '@fincloud/core/auth';
import {
  ContractDto,
  SigningProcess,
  VendorSigner,
} from '@fincloud/swagger-generator/contract-management';

@Injectable({
  providedIn: 'root',
})
export class ContractHelperService {
  constructor(private authenticationService: AuthenticationService) {}

  isLatestDocumentAvailable(contract: ContractDto) {
    if (
      contract.users.some((user) => user.state === 'SUCCESSFULLY_SIGNED') &&
      contract.signingProcess.webhookFeedbacks?.some(
        (feedback) => !feedback.receivedDocumentWebhook,
      )
    ) {
      const userIdsWaitingForWebhook = contract.users
        .filter((user) => user.state === 'SUCCESSFULLY_SIGNED')
        .map((user) => user.id);

      return userIdsWaitingForWebhook?.some(
        (id) =>
          contract.signingProcess.webhookFeedbacks.find(
            (feedback) => feedback.userId === id,
          ).receivedDocumentWebhook === true,
      );
    }
    return true;
  }

  getContractStatus(contract: ContractDto, userId: string) {
    if (
      contract.users.find((user) => user.id === userId)?.state ===
        'SUCCESSFULLY_SIGNED' &&
      !this.isLatestDocumentAvailable(contract)
    ) {
      return 'SUCCESSFULLY_SIGNED';
    }
    return contract.status;
  }

  getVoidableStatus(contract: ContractDto) {
    return (
      contract.status !== 'VOIDED' &&
      contract.status !== 'COMPLETED' &&
      !!contract.signingProcess
    );
  }

  isUserVoidedContract(user: VendorSigner, contract: SigningProcess) {
    if (contract.voidedReason?.includes('Voided by Signer')) {
      const userId = contract.voidedReason.split('=')[1]?.trim();
      return user.clientUserId === userId;
    }
    return false;
  }

  getIconStatus(signer: VendorSigner, contract: SigningProcess): string {
    if (signer.status === 'SIGNED' || signer.status === 'COMPLETED') {
      return $localize`:@@contractManagement.contract.showUserWithCustomerInModal.label.dateSigned:Unterzeichnet am`;
    } else if (
      (signer.status === 'SENT' || signer.status === 'CREATED') &&
      !this.isUserVoidedContract(signer, contract)
    ) {
      return $localize`:@@contractManagement.contract.showUserWithCustomerInModal.label.pending:Anstehend`;
    } else {
      return $localize`:@@contractManagement.contract.showUserWithCustomerInModal.label.voided:Annulliert`;
    }
  }
}
