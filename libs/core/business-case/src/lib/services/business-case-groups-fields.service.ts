import { Injectable } from '@angular/core';
import {
  FieldDto,
  Group,
  GroupDto,
  Information,
} from '@fincloud/swagger-generator/business-case-manager';
import {
  CadrGroup,
  FieldDto as CompanyFieldDto,
  Information as CompanyInformation,
} from '@fincloud/swagger-generator/company';
import { InformationRecord } from '@fincloud/swagger-generator/exchange';
import { CaseFieldAccess } from '@fincloud/swagger-generator/portal';
import { GroupTemplateFields } from '../models/group-template-fields';
import { TemplateFieldViewModel } from '../models/template-field-view-model';

@Injectable({
  providedIn: 'root',
})
export class DataRoomGroupsFieldsService {
  private readonly fieldsNotAllowedToBeShown = ['companyId'];

  getGroupsTemplateFields(
    fields: FieldDto[] | CompanyFieldDto[],
    groupsOrderd: GroupDto[] | CadrGroup[],
    information: Record<string, Information | CompanyInformation>,
    caseFieldsAccess?: CaseFieldAccess[],
  ): GroupTemplateFields[] {
    if (caseFieldsAccess && !caseFieldsAccess?.length) {
      return [];
    }

    const businessCaseFields = fields;
    const notHiddenFields = businessCaseFields
      .filter(
        (f) => !f.isHidden && !this.fieldsNotAllowedToBeShown.includes(f.key),
      )
      .map((f) => f.key);
    const hiddenFields = businessCaseFields
      .filter((f) => f.isHidden)
      .map((f) => f.key);

    let groups = groupsOrderd?.map((group: Group, groupIndex: number) => {
      let nonDocumentFieldIndex = 0;
      const allGroupFields = group.fields
        .filter((f) => notHiddenFields.includes(f))
        .map((fieldKey) => {
          const field = businessCaseFields.find((f) => f.key === fieldKey);
          const fieldInformation =
            information[fieldKey as string | keyof InformationRecord];

          nonDocumentFieldIndex += field.fieldType !== 'DOCUMENT' ? 1 : 0;

          return {
            field: {
              ...field,
              index: `${groupIndex + 1}.${nonDocumentFieldIndex}`,
            },
            information: fieldInformation,
          } as TemplateFieldViewModel;
        });

      return {
        ...(group || []),
        groupIndex: groupIndex + 1,
        templateFields: allGroupFields.filter(
          (f) => f.field.fieldType !== 'DOCUMENT',
        ),
        documents: allGroupFields.filter(
          (f) => f.field.fieldType === 'DOCUMENT',
        ),
        hasHiddenConditionalFields: group.fields.some((f) =>
          hiddenFields.includes(f),
        ),
      } as GroupTemplateFields;
    });

    if (caseFieldsAccess) {
      groups = this.getAccessableFieldsAndGroups(groups, caseFieldsAccess);
    }

    return groups;
  }

  private getAccessableFieldsAndGroups(
    groups: GroupTemplateFields[],
    caseFieldsAccess: CaseFieldAccess[],
  ) {
    return groups
      .map((g) => {
        g.templateFields = g.templateFields.filter((f) =>
          caseFieldsAccess.some(
            (cfa) => cfa.informationId === f.information?.id,
          ),
        );
        g.documents = g.documents.filter((f) =>
          caseFieldsAccess.some(
            (cfa) => cfa.informationId === f.information?.id,
          ),
        );
        return g;
      })
      .filter((g) => g.templateFields.length || g.documents.length);
  }
}
