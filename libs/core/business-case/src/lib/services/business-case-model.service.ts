import { Inject, Injectable, LOCALE_ID } from '@angular/core';

import { MathUtils } from '@fincloud/core/math';
import { PartialBy } from '@fincloud/core/types';
import { Customer } from '@fincloud/swagger-generator/authorization-server';
import { ParticipantCasePermissionSetEntity } from '@fincloud/swagger-generator/business-case-manager';
import {
  BusinessCaseResultDto,
  ExchangeBusinessCase,
  InformationRecord,
  StructuredFinancingConfiguration,
} from '@fincloud/swagger-generator/exchange';
import {
  BusinessCasePermission,
  CustomerType,
  Locale,
} from '@fincloud/types/enums';
import {
  BusinessCase,
  BusinessCaseBreakdown,
  DropdownInputItem,
  ListViewBusinessCaseBreakdown,
  ListViewPartnerInfo,
  PartnerInfo,
  UserToken,
} from '@fincloud/types/models';
import { first, isNil } from 'lodash-es';
import { FinancedCase } from '../models/financed-case';
import { BusinessCaseFacilityHelperService } from './business-case-facility-helper.service';

@Injectable({
  providedIn: 'root',
})
export class BusinessCaseModelService {
  constructor(
    private businessCaseFacilityHelperService: BusinessCaseFacilityHelperService,
    @Inject(LOCALE_ID) private locale: Locale,
  ) {}

  toBusinessCaseViewModel(
    businessCase: Pick<
      BusinessCaseResultDto,
      | 'information'
      | 'company'
      | 'creationDate'
      | 'autoGeneratedBusinessCaseName'
      | 'leadCustomerKey'
      | 'participants'
      | 'businessCaseType'
      | 'id'
      | 'allParticipantsVisibilityOn'
    > & {
      financingVolumeValue?: number;
      structuredFinancingConfiguration?: StructuredFinancingConfiguration;
      masterBusinessCaseId?: string;
      participantVisibilityEnabledConfig?: boolean;
    },
    participantsPermissions: Record<string, ParticipantCasePermissionSetEntity>,
    customerKey?: string,
  ): BusinessCase {
    return {
      financingPurpose: businessCase.information?.financingPurpose?.value,
      financingVolume: this.getFinancingVolume(businessCase),
      company: businessCase.company,
      date: businessCase.creationDate,
      id: businessCase.id,
      autoGeneratedBusinessCaseName: businessCase.autoGeneratedBusinessCaseName,
      isCustomerLeadPartner: businessCase.leadCustomerKey === customerKey,
      isCustomerParticipant: !!businessCase.participants.find(
        (p) => p.customerKey === customerKey,
      ),
      financedPercent: businessCase.information
        ? this.getFinancedPercent(businessCase, participantsPermissions)
        : null,
      investedAmount: this.getTotalFinancedSum(
        businessCase,
        participantsPermissions,
      ),
      businessCaseType: businessCase.businessCaseType ?? null,
      allParticipantsVisibilityOn: businessCase.allParticipantsVisibilityOn,
      participantVisibilityEnabledConfig:
        businessCase.participantVisibilityEnabledConfig,
    } as BusinessCase;
  }

  toBusinessCaseBreakdownViewModel(
    businessCase: ExchangeBusinessCase,
    customerKeyNames: Customer[],
    participantsPermissions: Record<string, ParticipantCasePermissionSetEntity>,
    userToken?: UserToken,
    currentCustomer?: Customer,
  ): BusinessCaseBreakdown {
    const investedAmount = this.getTotalFinancedSum(
      businessCase,
      participantsPermissions,
    );
    const financingVolume = this.getFinancingVolume(businessCase);

    const allPartners = this.getPartnersData(
      businessCase,
      customerKeyNames,
      userToken,
      currentCustomer,
    );

    return {
      isRealEstateFinStructureCase:
        businessCase.structuredFinancingConfiguration
          ?.financingStructureType === 'REAL_ESTATE',
      investedAmount,
      outstandingAmount: financingVolume - investedAmount,
      financingVolume,
      leadPartner: allPartners.leadPartner,
      partners: allPartners.participants,
      id: businessCase.id,
      minParticipationAmount: businessCase.minParticipationAmount,
      maxParticipationAmount: businessCase.maxParticipationAmount,
      businessCaseType: businessCase.businessCaseType,
    };
  }

  toBusinessCaseBreakdownListViewModel(
    businessCase: PartialBy<
      BusinessCaseResultDto,
      'structuredFinancingConfiguration'
    >,
    customerKeyNames: Customer[],
    participantsPermissions: Record<string, ParticipantCasePermissionSetEntity>,
    userToken?: UserToken,
  ): ListViewBusinessCaseBreakdown {
    const investedAmount = this.getTotalFinancedSum(
      businessCase,
      participantsPermissions,
    );
    const financingVolume = this.getFinancingVolume(businessCase);

    return {
      investedAmount: investedAmount,
      outstandingAmount: financingVolume - investedAmount,
      financingVolume: financingVolume,
      leadPartner: first(
        this.getListViewPartnersData(
          businessCase,
          customerKeyNames,
          userToken,
          true,
        ),
      ),
      partners: this.getListViewPartnersData(
        businessCase,
        customerKeyNames,
        userToken,
      ),
      id: businessCase.id,
      businessCaseType: businessCase.businessCaseType,
    };
  }

  private getPartnersData(
    businessCase: ExchangeBusinessCase,
    customerKeyNames: Customer[],
    userToken?: UserToken,
    currentCustomer?: Customer,
  ): { leadPartner: PartnerInfo; participants: PartnerInfo[] } {
    const financingVolume = this.getFinancingVolume(businessCase);
    const result = {
      leadPartner: null,
      participants: [],
    } as { leadPartner: PartnerInfo; participants: PartnerInfo[] };

    const isFinStructureCase =
      businessCase.structuredFinancingConfiguration?.financingStructureType ===
      'REAL_ESTATE';
    const isCustomerRealEstate =
      currentCustomer?.customerType === CustomerType.REAL_ESTATE;

    businessCase.participants.forEach((participant) => {
      const preparedParticipant = {
        ...participant,
        name: customerKeyNames.find(
          (keyName: Customer) => keyName.key === participant.customerKey,
        )?.name,
        totalParticipationAmount: this.getTotalParticipationAmount(participant),
        totalParticipationRatio: MathUtils.getSafeValue(
          this.getTotalParticipationAmount(participant) / financingVolume,
          4,
        ),
        isOwnCustomer: participant.customerKey === userToken?.customer_key,
      };

      if (isCustomerRealEstate) {
        preparedParticipant.participationAmountToggle = !!isFinStructureCase;
      }

      if (preparedParticipant.lead) {
        result.leadPartner = preparedParticipant;
      } else {
        result.participants.push(preparedParticipant);
      }
    });

    return result;
  }

  private getListViewPartnersData(
    businessCase: FinancedCase & Pick<BusinessCaseResultDto, 'participants'>,
    customerKeyNames: Customer[],
    userToken?: UserToken,
    isLeadPartner = false,
  ): ListViewPartnerInfo[] {
    const participants = businessCase.participants.map((participant) => {
      const financingVolume = this.getFinancingVolume(businessCase);

      return {
        ...participant,
        name: customerKeyNames.find(
          (keyName: Customer) => keyName.key === participant.customerKey,
        )?.name,
        totalParticipationAmount: this.getTotalParticipationAmount(participant),
        totalParticipationRatio: MathUtils.roundToThreeDecimals(
          this.getTotalParticipationAmount(participant) / financingVolume,
        ),
        isOwnCustomer: participant.customerKey === userToken?.customer_key,
      };
    });

    return isLeadPartner
      ? participants?.filter((p) => p.lead)
      : participants.filter((p) => !p.lead);
  }

  getTotalParticipationAmount(participant: {
    totalParticipationAmount: number;
  }): number {
    return participant.totalParticipationAmount;
  }

  private getInformationValueByKey(
    businessCase: Pick<BusinessCaseResultDto, 'information'>,
    key: string,
  ): string | number | DropdownInputItem {
    return (
      businessCase.information[key as keyof InformationRecord]?.value ?? ''
    );
  }

  private getFinancedPercent(
    businessCase: FinancedCase & Pick<BusinessCaseResultDto, 'participants'>,
    participantsPermissions: Record<string, ParticipantCasePermissionSetEntity>,
  ) {
    const totalFinancedSum: number = this.getTotalFinancedSum(
      businessCase,
      participantsPermissions,
    );
    const financingVolume: number = this.getFinancingVolume(businessCase);
    return totalFinancedSum / financingVolume;
  }

  getTotalFinancedSum(
    businessCase: Pick<BusinessCaseResultDto, 'participants'> | undefined,
    participantsPermissions: Record<string, ParticipantCasePermissionSetEntity>,
  ) {
    if (isNil(businessCase)) {
      return 0;
    }
    return businessCase.participants
      .map(({ customerKey, totalParticipationAmount }) => {
        const permissions = participantsPermissions
          ? participantsPermissions[customerKey]?.permissions
          : null;

        if (
          permissions?.includes(BusinessCasePermission.BCP_00063) ||
          permissions?.includes(BusinessCasePermission.BCP_00140)
        ) {
          return Number(totalParticipationAmount);
        }
        return 0;
      })
      .reduce((a, b) => a + b, 0);
  }

  getFinancingVolume(businessCase: FinancedCase) {
    const value = businessCase?.financingVolumeValue;
    if (value) {
      return MathUtils.getSafeValue(value);
    }

    return +this.businessCaseFacilityHelperService.getFinancingVolume(
      businessCase,
    );
  }

  formatMonths(value: number | string): string {
    return `${value}` + $localize`:@@months: Monate`;
  }

  formatCommissionFee(value: number | string, unit: string): string {
    return `${value}  |  ${unit}`;
  }

  formatDecimal(value: number | string): string {
    return value.toString().replace(/\./, ',');
  }
}
