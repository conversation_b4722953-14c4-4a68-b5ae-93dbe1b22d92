import { neitherNullNorUndefined } from '@fincloud/core/utils';
import { isEmpty, isNumber, isObject, isString, toNumber } from 'lodash-es';
import { FieldInformationValue } from './field-information-value';

export class InformationUtils {
  // Information values are typed as strings but can take many forms
  // Examples for values representing no useful value are: '', {}, []
  public static isEmptyValue(informationValue: FieldInformationValue) {
    const isNumeric =
      isNumber(toNumber(informationValue)) &&
      neitherNullNorUndefined(informationValue);

    if (!informationValue && !isNumeric) {
      return true;
    }

    if (isEmpty(informationValue) && !isNumeric) {
      return true;
    }

    if (
      (isObject(informationValue) || Array.isArray(informationValue)) &&
      isEmpty(informationValue)
    ) {
      return true;
    }

    return false;
  }

  public static isStringValue(
    informationValue: FieldInformationValue,
  ): informationValue is string {
    return isString(informationValue);
  }
}
