import { ValidatorFn } from '@angular/forms';
import { IconName } from '@fincloud/types/models';

export class FieldBase<T> {
  value: T | undefined;
  key: string;
  placeholder: string;
  controlType: string;
  type: string;
  options: { label: string; value: unknown }[];
  validators: ValidatorFn[];
  icon: IconName;
  label: string;

  constructor(
    options: {
      value?: T;
      key?: string;
      placeholder?: string;
      controlType?: string;
      type?: string;
      options?: { label: string; value: unknown }[];
      validators?: ValidatorFn[];
      icon?: IconName;
      label?: string;
    } = {},
  ) {
    this.value = options.value;
    this.key = options.key || '';
    this.placeholder = options.placeholder || '';
    this.controlType = options.controlType || '';
    this.validators = options.validators || [];
    this.type = options.type || '';
    this.options = options.options || [];
    this.icon = options.icon || 'folder';
    this.label = options.label || '';
  }
}
