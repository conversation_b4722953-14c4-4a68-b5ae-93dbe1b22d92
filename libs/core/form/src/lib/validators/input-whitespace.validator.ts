import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function inputWhitespaceValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;

    if (!value) {
      return null;
    }
    const isWhitespace = value.trim().length === 0 && value.length > 0;
    return isWhitespace ? { whitespace: true } : null;
  };
}
