import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { EMAIL_VALIDATOR_REGEX } from '../utils/email-validator-regex';

export function emailValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = (control.value as string)?.trim();
    if (!value) {
      return null;
    }

    const valid = EMAIL_VALIDATOR_REGEX.test(value);
    return !valid ? { email: true } : null;
  };
}
