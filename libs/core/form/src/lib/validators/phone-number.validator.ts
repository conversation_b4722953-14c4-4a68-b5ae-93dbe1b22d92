import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { COUNTRY_PHONE_CODE_VALIDATOR_REGEX } from '../utils/country-phone-code-validator-regex';

export function phoneNumberInputValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value as string;

    if (!value) {
      return null;
    }

    const valid = COUNTRY_PHONE_CODE_VALIDATOR_REGEX.test(value);
    return !valid ? { phoneNumber: true } : null;
  };
}
