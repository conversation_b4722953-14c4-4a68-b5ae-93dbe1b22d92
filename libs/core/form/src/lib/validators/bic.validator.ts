import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { BIC_DISALLOWED_CHARS_REGEX } from '../utils/bic-disallowed-chars-regex';
import { BIC_REGEX } from '../utils/bic-regex';

/**
 * Before refactoring keep in mind the intended behaviour:
 * A# -> invalid symbol
 * AAA$ -> invalid symbol
 * ABCDEFG* -> invalid symbol
 * 33 -> invalid bic (due to pattern)
 * AA -> invalid length
 * ABCDEF12 -> valid
 * ABCDEF12A -> invalid bic (due to pattern requesting 3 finishing chars for branch id)
 * ABCDEF12AB3 -> valid
 *
 */
export function bicValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value as string;

    if (!value) {
      return null;
    }

    if (BIC_DISALLOWED_CHARS_REGEX.test(value)) {
      return { bicAllowedChars: true };
    }

    if (value.length <= 6) {
      if (!/^[A-Z]+$/.test(value)) {
        return { bic: true };
      }
      return { bicLength: true };
    } else if (value.length < 8) {
      if (!/^[A-Z]{6}[A-Z0-9]{1}$/.test(value)) {
        return { bic: true };
      }
      return { bicLength: true };
    } else if (value.length === 8) {
      if (!/^[A-Z]{6}[A-Z0-9]{2}$/.test(value)) {
        return { bic: true };
      }
    } else if (!BIC_REGEX.test(value)) {
      return { bic: true };
    }

    return null;
  };
}
