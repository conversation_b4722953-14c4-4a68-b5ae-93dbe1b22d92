import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { NAME_VALIDATOR_REGEX } from '../utils/name-validator-regex';

export function nameValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value as string;

    if (!value) {
      return null;
    }

    const valid = NAME_VALIDATOR_REGEX.test(value);
    return !valid ? { name: true } : null;
  };
}
