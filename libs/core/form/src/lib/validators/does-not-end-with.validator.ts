import { AbstractControl, ValidatorFn } from '@angular/forms';

/**
 * Returns a validator function that checks if the control's value does not end with the specified character.
 */
export function doesNotEndWith(forbiddenEnding: string): ValidatorFn {
  return (
    control: AbstractControl,
  ): { [key: string]: { value: string } } | null => {
    const value = control.value;

    if (value && value.endsWith(forbiddenEnding)) {
      return { doesNotEndWith: { value: forbiddenEnding } };
    }

    return null;
  };
}
