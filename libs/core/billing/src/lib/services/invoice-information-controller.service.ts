/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  HttpClient,
  HttpHeaders,
  HttpRequest,
  HttpResponse,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  InputStreamResource,
  InvoiceInformationControllerService,
  StreamingResponseBody,
  ApiConfiguration as __Configuration,
} from '@fincloud/swagger-generator/billing';
import { StrictHttpResponse as __StrictHttpResponse } from '@fincloud/swagger-generator/document';
import { Observable } from 'rxjs';
import { filter as __filter, map as __map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class InvoiceInformationControllerCoreService extends InvoiceInformationControllerService {
  constructor(config: __Configuration, http: HttpClient) {
    super(config, http);
  }

  /**
   * getInvoiceAsPdf
   * @param invoiceId invoiceId
   * @return OK
   */
  getInvoiceAsPdfUsingGETResponse(
    invoiceId: string,
  ): Observable<__StrictHttpResponse<InputStreamResource>> {
    const __params = this.newParams();
    const __headers = new HttpHeaders();
    const __body: any = null;

    const req = new HttpRequest<any>(
      'GET',
      this.rootUrl +
        `/invoice-info/pdf/${encodeURIComponent(String(invoiceId))}`,
      __body,
      {
        headers: __headers,
        params: __params,
        responseType: 'blob',
      },
    );

    return this.http.request<any>(req).pipe(
      __filter((_r) => _r instanceof HttpResponse),
      __map((_r) => {
        return _r as __StrictHttpResponse<InputStreamResource>;
      }),
    );
  }

  /**
   * getFilteredInvoicesAsZip
   * @param invoiceIds invoiceIds
   * @return OK
   */
  getFilteredInvoicesAsZipUsingPOSTResponse(
    invoiceIds: Array<string>,
  ): Observable<__StrictHttpResponse<StreamingResponseBody>> {
    const __params = this.newParams();
    const __headers = new HttpHeaders();
    let __body: any = null;
    __body = invoiceIds;
    const req = new HttpRequest<any>(
      'POST',
      this.rootUrl + '/invoice-info/filtered-invoices',
      __body,
      {
        headers: __headers,
        params: __params,
        responseType: 'blob',
      },
    );

    return this.http.request<any>(req).pipe(
      __filter((_r) => _r instanceof HttpResponse),
      __map((_r) => {
        return _r as __StrictHttpResponse<StreamingResponseBody>;
      }),
    );
  }
}
