/* eslint-disable @typescript-eslint/no-explicit-any */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  DocumentBusinessCaseControllerService as DocumentBusinessCaseControllerServiceSwagger,
  RequestBuilder,
  StreamingResponseBody,
  StrictHttpResponse,
  ApiConfiguration as __Configuration,
} from '@fincloud/swagger-generator/document';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class DocumentBusinessCaseControllerService extends DocumentBusinessCaseControllerServiceSwagger {
  constructor(config: __Configuration, http: HttpClient) {
    super(config, http);
  }

  /**
   * getAllBusinessCaseDocuments.
   *
   *
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllBusinessCaseDocumentsUsingGet()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllBusinessCaseDocuments$Response(
    params: {
      /**
       * businessCaseId
       */
      businessCaseId: string;
    },
    context?: HttpContext,
  ): Observable<StrictHttpResponse<StreamingResponseBody>> {
    const rb = new RequestBuilder(
      this.rootUrl,
      DocumentBusinessCaseControllerService.GetAllBusinessCaseDocumentsPath,
      'get',
    );
    if (params) {
      rb.path('businessCaseId', params.businessCaseId, { style: 'simple' });
    }

    return this.http
      .request(
        rb.build({
          responseType: 'blob',
          accept: 'application/json',
          context: context,
        }),
      )
      .pipe(
        filter((r: any) => r instanceof HttpResponse),
        map((r: HttpResponse<any>) => {
          return r as StrictHttpResponse<StreamingResponseBody>;
        }),
      );
  }
}
