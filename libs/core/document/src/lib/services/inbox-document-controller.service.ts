/* eslint-disable @typescript-eslint/no-explicit-any */
import { HttpClient, HttpContext, HttpEvent } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RequestBuilder } from '@fincloud/swagger-generator/authorization-server';
import {
  DocumentEntity,
  InboxDocumentControllerService as InboxDocumentControllerSwaggerService,
  StrictHttpResponse,
  ApiConfiguration as __Configuration,
  StrictHttpResponse as __StrictHttpResponse,
} from '@fincloud/swagger-generator/document';
import { Observable, Observable as __Observable } from 'rxjs';
import { map } from 'rxjs/operators';

/**
 * Inbox Document Controller
 */
@Injectable({
  providedIn: 'root',
})
export class InboxDocumentService extends InboxDocumentControllerSwaggerService {
  constructor(config: __Configuration, http: HttpClient) {
    super(config, http);
  }

  /**
   * uploadInboxDocument
   *
   *
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `uploadInboxDocumentUsingPost()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  uploadInboxDocumentUsingPost$Response(
    params: {
      /**
       * businessCaseId
       */
      businessCaseId: string;
      userId: string;
      body?: {
        /**
         * file
         */
        file: Blob;
      };
    },
    context?: HttpContext,
  ): __Observable<__StrictHttpResponse<any>> {
    const rb = new RequestBuilder(
      this.rootUrl,
      'InboxDocumentControllerSwaggerService.UploadInboxDocumentPath', // TODO: DELETE
      'post',
    );
    if (params) {
      rb.path('businessCaseId', params.businessCaseId, { style: 'simple' });
      rb.query('userId', params.userId, {});
      rb.body(params.body, 'multipart/form-data');
    }

    return this.http
      .request(
        rb.build({
          responseType: 'json',
          accept: '*/*',
          context: context,
          reportProgress: true,
        }),
      )
      .pipe(
        map((r) => {
          return r as StrictHttpResponse<HttpEvent<any>>;
        }),
      );
  }

  /**
   * uploadInboxDocument
   *
   *
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `uploadInboxDocumentUsingPost$Response()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  uploadInboxDocumentUsingPost(
    params: {
      /**
       * businessCaseId
       */
      businessCaseId: string;
      userId: string;
      body?: {
        /**
         * file
         */
        file: Blob;
      };
    },
    context?: HttpContext,
  ): Observable<any> {
    return this.uploadInboxDocumentUsingPost$Response(params, context).pipe(
      map((r) => {
        return r as StrictHttpResponse<HttpEvent<DocumentEntity>>;
      }),
    );
  }

  /**
   * uploadInboxZip.
   *
   *
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `uploadInboxZipUsingPost()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  uploadInboxZipUsingPost$Response(
    params: {
      /**
       * businessCaseId
       */
      businessCaseId: string;
      body?: {
        /**
         * zipPackage
         */
        zipPackage: Blob;
      };
    },
    context?: HttpContext,
  ): Observable<StrictHttpResponse<any>> {
    const rb = new RequestBuilder(
      this.rootUrl,
      'InboxDocumentControllerSwaggerService.UploadInboxZip1Path', // TODO: DELETE
      'post',
    );
    if (params) {
      rb.path('businessCaseId', params.businessCaseId, { style: 'simple' });
      rb.body(params.body, 'multipart/form-data');
    }

    return this.http
      .request(
        rb.build({
          responseType: 'json',
          accept: 'application/json',
          context: context,
          reportProgress: true,
        }),
      )
      .pipe(
        map((r) => {
          return r as StrictHttpResponse<HttpEvent<any>>;
        }),
      );
  }

  /**
   * uploadInboxZip.
   *
   *
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `uploadInboxZipUsingPost$Response()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  uploadInboxZipUsingPost(
    params: {
      /**
       * businessCaseId
       */
      businessCaseId: string;
      body?: {
        /**
         * zipPackage
         */
        zipPackage: Blob;
      };
    },
    context?: HttpContext,
  ): Observable<any> {
    return this.uploadInboxZipUsingPost$Response(params, context).pipe(
      map((r) => {
        return r as StrictHttpResponse<HttpEvent<any>>;
      }),
    );
  }
}
