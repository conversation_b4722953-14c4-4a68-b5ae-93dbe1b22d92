import { DOCUMENT } from '@angular/common';
import { HttpBackend, HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Environment, LocalEnvironment } from '@fincloud/types/models';
import { ENVIRONMENT_TOKEN } from '../utils/environment-token';

@Injectable({
  providedIn: 'root',
})
export class ConfigurationService {
  private http: HttpClient;

  // Extract customer key and initial path if it exists
  // E.g. host/<customer_key>/<specific page and/or query params>
  // private urlPattern =
  //   /(?<locale>\/en)?\/(?<customerKey>[^/?]+)\/?(?<initialPath>[^#]*)/;

  // private _customerKey: string;
  // get customerKey(): string {
  //   if (!this._customerKey) {
  //     this._customerKey = this.getCustomerKey();
  //   }
  //   return this._customerKey;
  // }

  // private _environmentKey: EnvironmentKey;
  // get environmentKey(): EnvironmentKey {
  //   if (!this._environmentKey) {
  //     this._environmentKey = this.extractEnvironmentKey();
  //   }
  //   return this._environmentKey;
  // }

  // private _urlLocale: 'de' | 'en';
  // get urlLocale(): 'de' | 'en' {
  //   return this._urlLocale;
  // }

  // get urlLocaleBaseHrefSegment(): 'en' | '' {
  //   return this._urlLocale === 'de' ? '' : 'en';
  // }

  // Store initial path which otherwise will be lost after APP_BASE_HREF token is provided
  // Which will result in losing context on page refresh by redirecting to home page
  public initialNavigationPath: string[] = [];

  constructor(
    httpBackend: HttpBackend,
    @Inject(ENVIRONMENT_TOKEN)
    private environment: Environment | LocalEnvironment,
    @Inject(DOCUMENT) private document: Document,
  ) {
    this.http = new HttpClient(httpBackend);
    // this.setInitialNavigationPath();
    // this.setUrlLocale();
  }

  // public getConfigObservable(): Observable<Configuration> {
  //   let { origin } = this.getUrlParts();

  //   if (isDevMode()) {
  //     origin = this.getCustomerKey();
  //   }

  //   return this.buildConfiguration(origin);
  // }

  // private getUrlParts() {
  //   return new URL(this.document.URL);
  // }

  // private getCustomerKey() {
  //   if (isDevMode()) {
  //     return this.getBuiltInEnvironmentKey();
  //   }
  //   return this.getHostBasedEnvironmentKey();
  // }

  // private getHostBasedEnvironmentKey() {
  //   const { customerKey } = this.getPathParts();
  //   return customerKey;
  // }

  // private setInitialNavigationPath() {
  //   const { initialPath } = this.getPathParts();
  //   this.initialNavigationPath = initialPath ? [initialPath] : [];
  // }

  // private setUrlLocale() {
  //   const { locale } = this.getPathParts();
  //   this._urlLocale = (locale?.split('/')[1] as 'en') ?? 'de';
  // }

  // getPathParts() {
  //   const pathname = this.getWindowPath();
  //   const matches = this.urlPattern.exec(pathname);
  //   const { locale, customerKey, initialPath } = matches?.groups ?? {};

  //   return { locale, customerKey, initialPath };
  // }

  // private getWindowPath() {
  //   return `${this.windowRef.nativeWindow.location.pathname}${
  //     this.windowRef.nativeWindow.location.search ?? ''
  //   }`;
  // }

  // private getBuiltInEnvironmentKey() {
  //   if ('customerKey' in this.environment) {
  //     return (this.environment as { customerKey: string }).customerKey;
  //   } else {
  //     throw new Error(
  //       'Customer key should be present in environment.ts for local development'
  //     );
  //   }
  // }

  // private buildConfiguration(origin: string): Observable<Configuration> {
  //   return of({
  //     environmentKey: this.environmentKey,
  //     apiServer: {
  //       apiUrl: `${origin}/api/`,
  //     },
  //     // customer: {
  //     //   key: this.customerKey,
  //     //   name: this.customerKey,
  //     // },
  //   });
  // }
}
