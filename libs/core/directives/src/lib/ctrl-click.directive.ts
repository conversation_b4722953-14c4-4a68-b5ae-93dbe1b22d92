import {
  Directive,
  EventEmitter,
  HostListener,
  Input,
  Output,
} from '@angular/core';
import { selectUserCustomerKey } from '@fincloud/core-state/user';
import { WindowRef } from '@fincloud/core/services';
import { AppState } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { take } from 'rxjs';

@Directive({
  selector: '[appMetaKeyOrCtrlKey]',
  exportAs: 'appMetaKeyOrCtrlKey',
})
export class CtrlClickDirective {
  @Input() path: string;
  @Input() id: string;
  @Input() tab: string;
  @Input() subTab: string;

  @Input() blockExecuteOnIdMissing: boolean;

  @Output() executeOnClickChange = new EventEmitter<boolean>();

  constructor(
    private store: Store<AppState>,
    private windowRef: WindowRef,
  ) {}

  @HostListener('click', ['$event'])
  onClick(event: MouseEvent) {
    this.checkMetaKeyOrCtrlKey(event);
  }

  checkMetaKeyOrCtrlKey(event: MouseEvent) {
    if (this.blockExecuteOnIdMissing && !this.id) {
      return;
    }

    let shouldExecuteOnClick = true;
    if (event.metaKey || event.ctrlKey) {
      this.store
        .select(selectUserCustomerKey)
        .pipe(take(1))
        .subscribe((userCustomerKey) => {
          const url = `/${userCustomerKey}${this.path ? `/${this.path}` : ''}${
            this.id ? `/${this.id}` : ''
          }${this.tab ? `/${this.tab}` : ''}${
            this.subTab ? `/${this.subTab}` : ''
          }`;
          this.windowRef.openInNewTab(url);
        });
      shouldExecuteOnClick = false;
    }
    this.executeOnClickChange.emit(shouldExecuteOnClick);
  }
}
