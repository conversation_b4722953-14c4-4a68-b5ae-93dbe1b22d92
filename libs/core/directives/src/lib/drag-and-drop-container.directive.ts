import {
  DestroyRef,
  Directive,
  ElementRef,
  EventEmitter,
  HostBinding,
  HostListener,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DocumentInteractionsService } from '@fincloud/core/services';
import { Toast } from '@fincloud/core/toast';
import { FinToastService } from '@fincloud/ui/toast';
import { BehaviorSubject, debounceTime, distinctUntilChanged } from 'rxjs';

/**
 * Directive that turns an element into a drag and drop container for files.
 * Emits events when files are dropped and when the drag state changes.
 *
 * @example
 * <div appDragAndDropContainer
 *      (filesDropped)="onFilesDropped($event)">
 *   Drop files here
 * </div>
 */

// Currently this directive is coupled with the functionality for folder structure
// In the end it should provide drag and drop events which implementor should handle
@Directive({
  selector: '[appDragAndDropContainer]',
  exportAs: 'appDragAndDropContainer',
})
export class DragAndDropContainerDirective implements OnInit {
  @Input() set isActive(value: boolean) {
    this.isEnabled$.next(value);
  }
  @Input() hoverDelay = 0;

  @Output() filesDropped = new EventEmitter<FileList>();

  dragzoneActive$ = new BehaviorSubject(false);
  isEnabled$ = new BehaviorSubject(true);
  isOnDragOver: boolean;

  private readonly toastErrorMaxFileCountReachedPrefix = $localize`:@@fileUploader.toast.error.maxFileCount.prefix:Maximale Dateianzahl erreicht. Es können maximal`;
  private readonly toastErrorMaxFileCountReachedSuffix = $localize`:@@fileUploader.toast.error.maxFileCount.suffix:gleichzeitig hochgeladen werden.`;

  private dragzoneActive = false;
  private isEnabled = true;

  constructor(
    private destroRef: DestroyRef,
    private documentInteractionsService: DocumentInteractionsService,
    private finToastService: FinToastService,
    private elementRef: ElementRef,
  ) {}

  ngOnInit() {
    this.dragzoneActive$
      .pipe(
        distinctUntilChanged(),
        debounceTime(this.hoverDelay),
        takeUntilDestroyed(this.destroRef),
      )
      .subscribe((isActive) => {
        this.dragzoneActive = isActive;
      });

    this.isEnabled$
      .pipe(
        distinctUntilChanged(),
        debounceTime(this.hoverDelay),
        takeUntilDestroyed(this.destroRef),
      )
      .subscribe((isActive) => {
        this.isEnabled = isActive;
      });

    this.documentInteractionsService.isHoveringDocumentField$
      .pipe(distinctUntilChanged(), takeUntilDestroyed(this.destroRef))
      .subscribe((isHoveringDocumentField) => {
        this.isOnDragOver = !isHoveringDocumentField;
        if (!this.isOnDragOver) {
          this.documentInteractionsService.sectionExit();
        }
      });
  }

  @HostBinding('class')
  get dragZoneClasses(): string | null {
    return this.dragzoneActive && this.isEnabled
      ? '!tw-bg-color-transparency-secondary-minimal outline-2-interactive'
      : '';
  }

  private handleEvent(ev: Event, isActive: boolean): void {
    ev.preventDefault();
    ev.stopPropagation();
    this.dragzoneActive$.next(isActive);
  }

  @HostListener('dragover', ['$event'])
  onDragOver(ev: DragEvent): void {
    this.isOnDragOver = true;
    this.handleEvent(ev, true);
  }

  @HostListener('dragenter', ['$event'])
  onDragEnter() {
    this.documentInteractionsService.sectionEnter();
  }

  @HostListener('dragleave', ['$event'])
  onDragLeave(ev: DragEvent): void {
    // the only way to track actual leave on safari
    if (this.checkIsEventOutsideContainer(this.elementRef, ev)) {
      this.isOnDragOver = false;
    }

    if (this.isOnDragOver) {
      return;
    }

    this.documentInteractionsService.sectionExit();
    this.handleEvent(ev, false);
  }

  @HostListener('drop', ['$event'])
  onDrop(ev: DragEvent): void {
    this.handleEvent(ev, false);
    this.documentInteractionsService.stopAllHoverEffects();

    if (!this.isEnabled) {
      return;
    }

    if (ev.dataTransfer.files?.length > 1) {
      this.finToastService.show(
        Toast.error(
          `${this.toastErrorMaxFileCountReachedPrefix} 1 ${this.toastErrorMaxFileCountReachedSuffix}`,
        ),
      );

      return;
    }

    const { dataTransfer } = ev;

    if (dataTransfer?.items?.length && dataTransfer.items[0].kind === 'file') {
      this.filesDropped.emit(dataTransfer.files);
    }

    dataTransfer?.clearData();
  }

  private checkIsEventOutsideContainer(
    elementRef: ElementRef,
    event: DragEvent,
  ) {
    // Check if the coordinates are outside the rectangle
    const rect = elementRef.nativeElement.getBoundingClientRect();
    const x = event.clientX;
    const y = event.clientY;

    return x < rect.left || x > rect.right || y < rect.top || y > rect.bottom;
  }
}
