{"name": "@fincloud/core", "peerDependencies": {"@ag-grid-community/core": "30.1.0", "@angular-slider/ngx-slider": "17.0.2", "@angular/common": "17.3.11", "@angular/core": "17.3.11", "@angular/forms": "17.3.11", "@angular/platform-browser": "17.3.11", "@angular/router": "17.3.11", "@fincloud/core-state": "0.0.1", "@fincloud/swagger-generator": "0.0.1", "@fincloud/types": "0.0.1", "@ng-bootstrap/ng-bootstrap": "16.0.0", "@ngrx/store": "17.2.0", "@ngx-formly/core": "6.3.3", "@siemens/ngx-datatable": "22.4.1", "@zip.js/zip.js": "2.7.45", "backoff-rxjs": "7.0.0", "dayjs": "1.11.11", "jwt-decode": "4.0.0", "lodash-es": "4.17.21", "ngx-currency": "17.0.0", "ngx-device-detector": "7.0.0", "ngx-webstorage": "13.0.1", "rxjs": "7.8.1", "uuid": "9.0.1", "posthog-js": "1.150.1", "tus-js-client": "4.2.3", "screenfull": "6.0.2", "ngx-ui-tour-ngx-bootstrap": "12.1.0", "ngx-scrollbar": "16.1.0", "@fincloud/ui": "~0.0.601", "@angular/cdk": "17.3.10", "azure-maps-control": "3.3.0", "socket.io-client": "4.8.1", "@fincloud/utils": "0.0.1"}, "sideEffects": false, "version": "0.0.1"}