import { Injectable } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { FieldBase } from '@fincloud/core/dynamic-form-fields';

@Injectable({
  providedIn: 'root',
})
export class FormFieldService {
  toFormGroup(formFields: FieldBase<unknown>[]): UntypedFormGroup {
    const group: { [key: string]: UntypedFormControl } = {};

    formFields?.forEach(
      (field) =>
        (group[field.key] = new UntypedFormControl(
          field.value,
          field.validators,
        )),
    );

    return new UntypedFormGroup(group);
  }
}
