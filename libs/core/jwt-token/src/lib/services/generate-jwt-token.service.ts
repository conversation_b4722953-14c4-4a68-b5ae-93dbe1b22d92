import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class GenerateJwtTokenService {
  generateJwtToken(customerKey: string, userId: string) {
    const header = {
      alg: 'HS256',
      typ: 'JWT',
    };

    const payload = {
      customer_key: customerKey,
      sub: userId,
    };

    const encodedHeader = this.base64UrlEncode(JSON.stringify(header));
    const encodedPayload = this.base64UrlEncode(JSON.stringify(payload));

    const signature = this.base64UrlEncode(
      btoa(encodedHeader + '.' + encodedPayload),
    );

    return `Bearer ${encodedHeader}.${encodedPayload}.${signature}`;
  }

  base64UrlEncode(input: string): string {
    return btoa(input).replace('+', '-').replace('/', '_').replace(/=+$/, '');
  }
}
