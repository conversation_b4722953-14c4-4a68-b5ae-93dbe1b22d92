{"extends": ["../../.eslintrc.json"], "ignorePatterns": ["!**/*"], "overrides": [{"files": ["*.ts"], "extends": ["plugin:@nx/angular", "plugin:@angular-eslint/template/process-inline-templates", "plugin:prettier/recommended"], "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "app", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "app", "style": "kebab-case"}], "check-file/folder-match-with-fex": ["error", {"**/*.component.{ts,html,scss}": "**/components/**/"}], "prettier/prettier": ["error", {"endOfLine": "auto"}]}}, {"files": ["**/models/*.ts", "**/enums/*.ts", "**/utils/*.ts", "**/utils/**/*.ts"], "extends": ["plugin:@nx/typescript"], "rules": {"@fincloud/ns/no-multiple-exports": "error"}}, {"files": ["**/models/*.ts", "**/enums/*.ts", "**/utils/*.ts", "**/utils/**/*.ts"], "excludedFiles": ["*.module.ts"], "extends": ["plugin:@nx/typescript"], "rules": {"check-file/filename-naming-convention": ["error", {"**/*.ts": "KEBAB_CASE"}]}}, {"files": ["*.html"], "extends": ["plugin:@nx/angular-template", "plugin:prettier/recommended"], "rules": {"@angular-eslint/template/prefer-control-flow": "error", "prettier/prettier": ["error", {"endOfLine": "auto"}]}}, {"files": ["*.json"], "parser": "jsonc-eslint-parser", "rules": {"@nx/dependency-checks": "error"}}]}