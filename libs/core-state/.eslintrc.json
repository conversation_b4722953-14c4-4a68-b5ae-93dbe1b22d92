{"extends": ["../../.eslintrc.json"], "ignorePatterns": ["!**/*"], "overrides": [{"files": ["*.ts"], "extends": ["plugin:@nx/angular", "plugin:@angular-eslint/template/process-inline-templates", "plugin:prettier/recommended"], "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "lib", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "lib", "style": "kebab-case"}], "prettier/prettier": ["error", {"endOfLine": "auto"}]}}, {"files": ["*.ts"], "excludedFiles": ["**/index.ts", "**/*.module.ts", "test-setup.ts"], "extends": ["plugin:@nx/typescript", "plugin:@ngrx/recommended"], "rules": {"@ngrx/prefer-effect-callback-in-block-statement": "off", "@ngrx/prefix-selectors-with-select": "error", "@ngrx/on-function-explicit-return-type": "error", "check-file/filename-naming-convention": ["error", {"**/!(utils|enums|models)/*.ts": "**/[a-z0-9-]*.@(actions|reducer|metareducer|effects|selectors|service)"}], "check-file/folder-naming-convention": ["error", {"lib/**/": "@(actions|reducers|metareducers|effects|selectors|utils|enums|models|services)"}]}}, {"files": ["**/actions/index.ts"], "extends": ["plugin:@nx/typescript"], "rules": {"@fincloud/ns/no-ngrx-incorrect-actions-barrel-exports": "error", "@fincloud/ns/no-ngrx-actions-exports-mismatch": "error"}}, {"files": ["**/actions/*.actions.ts"], "rules": {"@fincloud/ns/no-ngrx-actions-missing-format": "error", "@typescript-eslint/naming-convention": ["error", {"selector": "variable", "modifiers": ["const", "exported"], "types": ["function"], "format": ["camelCase"]}]}}, {"files": ["**/models/*.ts", "**/enums/*.ts", "**/utils/*.ts", "**/utils/**/*.ts"], "extends": ["plugin:@nx/typescript"], "rules": {"@fincloud/ns/no-multiple-exports": "error"}}, {"files": ["**/models/*.ts", "**/enums/*.ts", "**/utils/*.ts", "**/utils/**/*.ts"], "excludedFiles": ["*.module.ts"], "extends": ["plugin:@nx/typescript"], "rules": {"check-file/filename-naming-convention": ["error", {"**/*.ts": "KEBAB_CASE"}]}}, {"files": ["*.html"], "extends": ["plugin:@nx/angular-template", "plugin:prettier/recommended"], "rules": {"prettier/prettier": ["error", {"endOfLine": "auto"}]}}, {"files": ["*.json"], "parser": "jsonc-eslint-parser", "rules": {"@nx/dependency-checks": "error"}}]}