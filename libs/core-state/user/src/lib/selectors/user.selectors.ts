import { UserState } from '@fincloud/types/models';
import { createFeatureSelector, createSelector } from '@ngrx/store';

export const featureKey = 'user';

export const selectUserState = createFeatureSelector<UserState>(featureKey);

export const selectUserCustomerKey = createSelector(
  selectUserState,
  (state: UserState) => state?.userToken?.customer_key,
);

export const selectUserId = createSelector(
  selectUserState,
  (state: UserState) => state?.userToken?.sub,
);

export const selectUser = createSelector(
  selectUserState,
  (state: UserState) => state?.user,
);
