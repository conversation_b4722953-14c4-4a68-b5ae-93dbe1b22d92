<div class="field-wrapper">
  @if (field.label) {
    <div class="label">
      {{ field.label }}
      {{ required ? '*' : '' }}
    </div>
  }

  <div class="inline">
    <ui-icon
      class="inline__icon"
      [name]="icon"
      color="subtle"
      size="large"
      [ngClass]="{ inline__icon__focused: focused }"
    ></ui-icon>

    @if (!editable) {
      @switch (field.type) {
        @case ('currency') {
          <span class="inline__element">
            <ui-monetary-expression
              [value]="+field.value"
            ></ui-monetary-expression>
          </span>
        }
        @case ('address') {
          <span class="inline__element">{{ formatAddress(field.value) }}</span>
        }
        @default {
          <span class="inline__element">{{ field.value }}</span>
        }
      }
    }

    @if (editable) {
      <div [formGroup]="form">
        <div>
          @switch (field.controlType) {
            @case ('textbox') {
              <input
                class="inline__edit-element"
                (focus)="focused = true"
                (blur)="focused = false"
                [formControlName]="field.key"
                [id]="field.key"
                [placeholder]="field.placeholder"
              />
            }
            @case ('number') {
              <input
                class="inline__edit-element"
                (focus)="focused = true"
                (blur)="focused = false"
                [formControlName]="field.key"
                [id]="field.key"
                [placeholder]="field.placeholder"
                currencyMask
                [options]="modeOptions"
              />
            }
            @case ('selectAddress') {
              <ui-select-address
                (addressFocus)="focused = true"
                (addressBlur)="focused = false"
                [formControlName]="field.key"
                [address]="field.value"
                [streetSuggestionsOnly]="true"
              >
              </ui-select-address>
            }
            @case ('radio') {
              <ui-radio-select
                [formControlName]="field.key"
                [options]="field.options"
              ></ui-radio-select>
            }
          }
        </div>
      </div>
    }
  </div>
</div>
