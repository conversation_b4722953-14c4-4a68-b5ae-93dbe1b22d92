import { FieldBase, NumberField } from '@fincloud/core/dynamic-form-fields';
import { AddressHelperService } from '@fincloud/core/services';
import {
  CURRENCY_MASK_CONFIG,
  MASK_CONFIG_BASE,
  PERCENTAGE_MASK_CONFIG,
} from '@fincloud/core/utils';

import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { Address } from '@fincloud/swagger-generator/authorization-server';
import { IconName } from '@fincloud/types/models';
import { NgxCurrencyConfig } from 'ngx-currency';

@Component({
  selector: 'ui-dynamic-editable-field',
  templateUrl: './dynamic-editable-field.component.html',
  styleUrls: ['./dynamic-editable-field.component.scss'],
})
export class DynamicEditableFieldComponent implements OnInit {
  @Input() editable: boolean;
  @Input() field: FieldBase<unknown>;
  @Input() form: UntypedFormGroup;
  @Input() icon: IconName;

  modeOptions: NgxCurrencyConfig;

  focused = false;
  required = false;

  constructor(
    private addressHelper: AddressHelperService,
    private regionalSettings: RegionalSettingsService,
  ) {}

  ngOnInit(): void {
    if (this.field instanceof NumberField) {
      if (this.field.type === 'integer') {
        this.modeOptions = MASK_CONFIG_BASE[this.regionalSettings.locale];
      }
      if (this.field.type === 'percent') {
        this.modeOptions = PERCENTAGE_MASK_CONFIG[this.regionalSettings.locale];
      }
      if (this.field.type === 'currency') {
        this.modeOptions = CURRENCY_MASK_CONFIG[this.regionalSettings.locale];
      }
    }

    if (this.field.validators.some((v) => v.name === 'required')) {
      this.required = true;
    }
  }

  formatAddress(address: unknown) {
    return this.addressHelper.asCustomerAddressString(address as Address);
  }
}
