import { FieldBase } from '@fincloud/core/dynamic-form-fields';
import { FormFieldService } from '@fincloud/core/form-field';
import { AddressHelperService } from '@fincloud/core/services';

import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
} from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { isEqual } from 'lodash-es';

@Component({
  selector: 'ui-dynamic-editable-form',
  templateUrl: './dynamic-editable-form.component.html',
  styleUrls: ['./dynamic-editable-form.component.scss'],
})
export class DynamicEditableFormComponent implements OnChanges {
  @Input() fields: FieldBase<unknown>[];
  @Input() updateDate: Date;

  @Output() formSubmitted = new EventEmitter();

  form: UntypedFormGroup;
  editable = false;

  constructor(
    private formFieldService: FormFieldService,
    private addressHelper: AddressHelperService,
  ) {}

  ngOnChanges() {
    if (this.fields) {
      this.form = this.formFieldService.toFormGroup(this.fields);
    }
  }

  switchEditState() {
    this.editable = !this.editable;

    if (this.editable) {
      this.patchFormInitialValues();
    }
  }

  onSubmit() {
    if (!this.form.valid) {
      return;
    }

    this.editable = false;

    const newFormValues = this.getFormNewValues();
    const fieldsObj = this.getFieldsAsObject();

    if (isEqual(fieldsObj, newFormValues)) {
      return;
    }

    this.formSubmitted.next(this.form.value);
  }

  private getFormNewValues() {
    return Object.keys(this.form.controls).reduce(
      (data: { [key: string]: string }, controlKey: string) => {
        data[controlKey] = this.getValueAsString(
          this.form.controls[controlKey].value,
        );
        return data;
      },
      {},
    );
  }

  private getFieldsAsObject() {
    return this.fields.reduce(
      (data: { [key: string]: unknown }, field: FieldBase<unknown>) => {
        data[field.key.toString()] = field.value;
        return data;
      },
      {},
    );
  }

  private getValueAsString(value: unknown): string {
    if (this.addressHelper.isAddress(value)) {
      return this.addressHelper.asCustomerAddressString(value);
    }
    return value as string;
  }

  private patchFormInitialValues() {
    this.fields.forEach((field: FieldBase<unknown>) =>
      this.form.patchValue({ [field.key]: field.value }),
    );
  }
}
