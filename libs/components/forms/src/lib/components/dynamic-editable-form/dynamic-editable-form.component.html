@if (form) {
  <form [formGroup]="form">
    <div class="tabs-level">
      <span>
        <span i18n="@@customerMasterData.tab.customerDetails.lastUpdated">
          Letzte Änderung am</span
        >
        {{ updateDate | date }}
      </span>
    </div>
    @for (field of fields; track field) {
      <div class="form-row">
        <ui-dynamic-editable-field
          [editable]="editable"
          [field]="field"
          [form]="form"
          [icon]="field.icon"
        ></ui-dynamic-editable-field>
      </div>
    }
  </form>
  <div class="buttons-wrapper">
    @if (!editable) {
      <ui-button
        (clicked)="switchEditState()"
        label="Daten bearbeiten"
        i18n-label="
          @@customerMasterData.tab.customerDetails.label.button.rework"
        type="outline"
        size="small"
      ></ui-button>
    }
    @if (editable) {
      <ui-button
        (clicked)="switchEditState()"
        label="Abbrechen"
        i18n-label="@@button.label.cancel"
        type="stealth"
        size="small"
      ></ui-button>
      <ui-button
        [disabled]="form.invalid"
        (clicked)="onSubmit()"
        label="Daten speichern"
        i18n-label="@@customerMasterData.tab.customerDetails.label.button.save"
        type="fill"
        size="small"
      ></ui-button>
    }
  </div>
}
