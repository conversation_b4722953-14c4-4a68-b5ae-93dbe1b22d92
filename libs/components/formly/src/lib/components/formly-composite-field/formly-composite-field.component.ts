import {
  CURRENCY_MASK_CONFIG,
  MASK_CONFIG_BASE,
  PERCENTAGE_MASK_CONFIG,
} from '@fincloud/core/utils';

import { Component, DestroyRef, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { UntypedFormControl, Validators } from '@angular/forms';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { DropdownInputItem } from '@fincloud/types/models';
import { FieldType } from '@ngx-formly/core';
import { NgxCurrencyConfig } from 'ngx-currency';

@Component({
  selector: 'ui-formly-composite-field',
  templateUrl: './formly-composite-field.component.html',
  styleUrls: ['./formly-composite-field.component.scss'],
})
export class FormlyCompositeFieldComponent extends FieldType implements OnInit {
  get formControl(): UntypedFormControl {
    return super.formControl as UntypedFormControl;
  }
  dropdownOptions: Record<string, unknown>[];
  inputMask: NgxCurrencyConfig;

  ngOnInit(): void {
    const initialValue: DropdownInputItem = this.formControl
      .value as DropdownInputItem;

    // Transfer the validator into the form control
    if (this.field.templateOptions.required) {
      this.formControl.addValidators(Validators.required);
    }

    this.dropdownOptions = this.to.options as Record<string, unknown>[];
    this.formControl.setValue({
      inputValue: initialValue?.inputValue,
      dropdownOption: initialValue?.dropdownOption ?? this.dropdownOptions[0],
    });
    this.inputMask = this.setInputMask(
      (this.formControl.value as DropdownInputItem).dropdownOption,
    );
    this.formControl.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((value: DropdownInputItem) => {
        this.inputMask = this.setInputMask(value.dropdownOption);
      });
  }

  private setInputMask(
    dropdownOption: Record<string, unknown>,
  ): NgxCurrencyConfig {
    switch (dropdownOption.label) {
      case $localize`:@@composite.field.percent:Prozentsatz`:
        return PERCENTAGE_MASK_CONFIG[this.regionalSettings.locale];
      case $localize`:@@composite.field.numeric:Numerisch`:
        return CURRENCY_MASK_CONFIG[this.regionalSettings.locale];
      default:
        return MASK_CONFIG_BASE[this.regionalSettings.locale];
    }
  }

  constructor(
    private destroyRef: DestroyRef,
    private regionalSettings: RegionalSettingsService,
  ) {
    super();
  }
}
