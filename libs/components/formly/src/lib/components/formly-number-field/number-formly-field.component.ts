import {
  CURRENCY_MASK_CONFIG,
  DECIMAL_MASK_CONFIG,
  MASK_CONFIG_BASE,
  MONTHS_MASK_CONFIG,
  PERCENTAGE_MASK_CONFIG,
} from '@fincloud/core/utils';

import { Component, OnInit } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { FieldType } from '@ngx-formly/core';
import { NgxCurrencyConfig } from 'ngx-currency';

@Component({
  selector: 'ui-number-formly-field',
  templateUrl: './number-formly-field.component.html',
  styleUrls: ['./number-formly-field.component.scss'],
})
export class FormlyNumberFieldComponent extends FieldType implements OnInit {
  get formControl(): UntypedFormControl {
    return super.formControl as UntypedFormControl;
  }
  modeOptions: NgxCurrencyConfig;

  ngOnInit(): void {
    this.modeOptions = this.getMaskConfigByFormatting(
      this.to.attributes.valueFormatting as string,
    );
  }

  constructor(private regionalSettings: RegionalSettingsService) {
    super();
  }

  private getMaskConfigByFormatting(
    valueFormatting: string,
  ): NgxCurrencyConfig {
    switch (valueFormatting) {
      case 'percent':
        return PERCENTAGE_MASK_CONFIG[this.regionalSettings.locale];
      case 'months':
        return MONTHS_MASK_CONFIG[this.regionalSettings.locale];
      case 'decimal':
        return DECIMAL_MASK_CONFIG[this.regionalSettings.locale];
      case 'monetary':
        return CURRENCY_MASK_CONFIG[this.regionalSettings.locale];
      case 'integer':
      default:
        return MASK_CONFIG_BASE[this.regionalSettings.locale];
    }
  }
}
