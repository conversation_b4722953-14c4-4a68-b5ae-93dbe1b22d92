import { Injectable } from '@angular/core';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { Locale } from '@fincloud/types/enums';
import {
  NgbDateParserFormatter,
  NgbDateStruct,
} from '@ng-bootstrap/ng-bootstrap';
/**
 * This Service handles how the date is rendered and parsed from keyboard i.e. in the bound input field.
 */
@Injectable()
export class CustomDateParserFormatter extends NgbDateParserFormatter {
  delimiter: string;

  constructor(private regionalSettings: RegionalSettingsService) {
    super();
    this.delimiter = this.regionalSettings.locale === Locale.EN ? '/' : '.';
  }

  parse(value: string): NgbDateStruct | null {
    if (value) {
      const date = value.split(this.delimiter);
      return {
        day: parseInt(date[0], 10),
        month: parseInt(date[1], 10),
        year: parseInt(date[2], 10),
      };
    }
    return null;
  }

  format(date: NgbDateStruct | null): string {
    return date
      ? date.day.toString().padStart(2, '0') +
          this.delimiter +
          date.month.toString().padStart(2, '0') +
          this.delimiter +
          date.year
      : '';
  }
}
