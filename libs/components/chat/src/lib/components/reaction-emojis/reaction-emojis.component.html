<div class="emoji-wrapper">
  @if (selectedIcon === 'none' && !feedbackSubmitted) {
    <div class="all-react-icons">
      <div class="rate-emoji-text" i18n="@@userExperience.feedback.invite.text">
        Können Sie uns bitte Ihre Meinung sagen? Sind Sie mit der Antwort
        zufrieden?
      </div>
      <div class="rate-emoji-icons">
        <div (click)="reactToOverallExperience(sentiment.Negative)">
          <ui-icon name="svgNegativeFace" size="small"></ui-icon>
        </div>
        <div (click)="reactToOverallExperience(sentiment.Neutral)">
          <ui-icon name="svgNeutralFace" size="small"></ui-icon>
        </div>
        <div (click)="reactToOverallExperience(sentiment.Positive)">
          <ui-icon name="svgPositiveFace" size="small"></ui-icon>
        </div>
      </div>
    </div>
  }
  <div class="selected-single-icon">
    @if (selectedIcon === 'negative') {
      <div class="icon-react">
        <div class="reaction-icon">
          <ui-icon name="svgNegativeFace" size="small"></ui-icon>
        </div>
        <form [formGroup]="form">
          <textarea
            cdkTextareaAutosize
            formControlName="text"
            class="feedback-textarea form-control"
            i18n-placeholder="@@neoGptChat.negative.feedback.placeholder"
            placeholder="Bitte teilen Sie uns Ihre Erfahrungen oder Gedanken mit."
          ></textarea>
          <ui-button
            class="feedback-button"
            label="Feedback abschicken"
            i18n-label="@@userExperience.feedback.submit.button.label"
            size="extra-small"
            type="outline"
            (clicked)="submitOverallExperienceFeedbackMessage(form.value)"
          ></ui-button>
        </form>
      </div>
    }
    @if (selectedIcon === 'neutral') {
      <div class="icon-react">
        <div class="reaction-icon">
          <ui-icon name="svgNeutralFace" size="small"></ui-icon>
        </div>
        <form [formGroup]="form">
          <textarea
            cdkTextareaAutosize
            formControlName="text"
            i18n-placeholder="@@neoGptChat.negative.feedback.placeholder"
            placeholder="Bitte teilen Sie uns Ihre Erfahrungen oder Gedanken mit."
            class="feedback-textarea form-control"
          ></textarea>
          <ui-button
            class="feedback-button"
            label="Feedback abschicken"
            i18n-label="@@userExperience.feedback.submit.button.label"
            type="outline"
            size="extra-small"
            (clicked)="submitOverallExperienceFeedbackMessage(form.value)"
          ></ui-button>
        </form>
      </div>
    }
    @if (selectedIcon === 'happy') {
      <div
        class="icon-react"
        (click)="reactToOverallExperience(sentiment.Positive)"
      ></div>
    }
  </div>
  @if (feedbackSubmitted) {
    <div
      class="thank-you-for-rating"
      i18n="@@dashboard.feedback.thankYouMessage"
    >
      Vielen Dank für Ihr Feedback!
    </div>
  }
</div>
