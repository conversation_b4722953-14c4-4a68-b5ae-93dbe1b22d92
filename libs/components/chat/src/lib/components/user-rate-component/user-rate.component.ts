import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import { IconColor, IconComponent, IconSize } from '@fincloud/components/icons';
import { Sentiment } from '@fincloud/swagger-generator/neo-gpt';

@Component({
  selector: 'app-user-rate',
  templateUrl: './user-rate.component.html',
  styleUrls: ['./user-rate.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UserRateComponent {
  @Input() likeColor: IconColor;
  @Input() dislikeColor: IconColor;
  @Input() iconSize: IconSize = 'large';
  @Input() sentiment: Sentiment | null;

  @Output() likeClicked = new EventEmitter();
  @Output() dislikeClicked = new EventEmitter();

  public readonly nonReactedColor = 'gray';
  public readonly fillTypeFill = 'fill';
  public readonly fillTypeOutline = 'outline';

  onMouseEnter(iconElement: IconComponent) {
    iconElement.type = 'fill';
  }

  onMouseOut(iconElement: IconComponent) {
    if (!this.isSentimentPositive() && iconElement.name === 'thumb_up') {
      iconElement.type = 'outline';
    }

    if (!this.isSentimentNegative() && iconElement.name === 'thumb_down') {
      iconElement.type = 'outline';
    }
  }

  onLike() {
    if (this.isSentimentPositive()) {
      return;
    }

    this.likeClicked.emit();
  }

  onDislike() {
    if (this.isSentimentNegative()) {
      return;
    }

    this.dislikeClicked.emit();
  }

  isSentimentNegative() {
    return this.sentiment === Sentiment.Negative;
  }

  isSentimentPositive() {
    return this.sentiment === Sentiment.Positive;
  }
}
