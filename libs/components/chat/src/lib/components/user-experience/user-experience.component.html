<div>
  @if (activeMessageIndex === messageIndex) {
    <div class="feedback-message">
      <form [formGroup]="form">
        <textarea
          cdkTextareaAutosize
          formControlName="text"
          i18n-placeholder="@@neoGptChat.negative.feedback.placeholder"
          placeholder="Bitte teilen Sie uns Ihre Erfahrungen oder Gedanken mit."
          class="feedback-textarea form-control"
        ></textarea>
        <div class="button">
          <ui-button
            class="feedback-button"
            label="Feedback abschicken"
            i18n-label="@@userExperience.feedback.submit.button.label"
            size="extra-small"
            type="outline"
            (clicked)="submitFeedback(form.value)"
          >
          </ui-button>
        </div>
      </form>
    </div>
  }

  @if (feedbackSubmitted) {
    <div i18n="@@neoGpt.thankYou.forRating.msg" class="thank-you-for-rating">
      Vielen Dank für Ihre Antwort! Wir werden versuchen, unseren Service auf
      der Grundlage Ihres Feedbacks zu verbessern!
    </div>
  }
</div>
