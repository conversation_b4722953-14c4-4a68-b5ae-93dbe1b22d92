import {
  Component,
  DestroyRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { FeedbackComment } from '@fincloud/swagger-generator/neo-gpt';
import { TourService } from 'ngx-ui-tour-ngx-bootstrap';

@Component({
  selector: 'ui-user-experience',
  templateUrl: './user-experience.component.html',
  styleUrls: ['./user-experience.component.scss'],
})
export class UserUserExperienceComponent implements OnInit {
  @Input()
  messageIndex: number;

  @Input()
  activeMessageIndex: number;

  @Output() commentOnChatGptAnswer = new EventEmitter<{
    feedbackComment: FeedbackComment;
    messageIndex: number;
  }>();

  time: Date;

  form: FormGroup;
  text: string;
  feedbackSubmitted = false;

  isTourActive = false;

  constructor(
    private destroyRef: DestroyRef,
    private fb: FormBuilder,
    private tourService: TourService,
  ) {}

  ngOnInit(): void {
    this.tourService.start$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => (this.isTourActive = true));

    this.tourService.end$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => (this.isTourActive = false));

    this.form = this.fb.group({
      text: ['', Validators.required],
    });
  }

  submitFeedback(value: { text: string }) {
    this.commentOnChatGptAnswer.emit({
      feedbackComment: {
        message: value.text,
      },
      messageIndex: this.messageIndex,
    });

    this.activeMessageIndex = null;
    this.feedbackSubmitted = true;
    this.form.reset();
  }
}
