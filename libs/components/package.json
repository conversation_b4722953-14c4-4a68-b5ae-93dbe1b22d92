{"name": "@fincloud/components", "peerDependencies": {"@ag-grid-community/angular": "30.1.0", "@ag-grid-community/client-side-row-model": "30.1.0", "@ag-grid-community/core": "30.1.0", "@ag-grid-enterprise/clipboard": "30.1.0", "@ag-grid-enterprise/excel-export": "30.1.0", "@ag-grid-enterprise/menu": "30.1.0", "@ag-grid-enterprise/range-selection": "30.1.0", "@angular/common": "17.3.11", "@angular/core": "17.3.11", "@angular/animations": "17.3.11", "@fincloud/state": "0.0.1", "@fincloud/core": "0.0.1", "@fincloud/types": "0.0.1", "@angular/cdk": "17.3.10", "@ngrx/store": "17.2.0", "rxjs": "7.8.1", "@angular/router": "17.3.11", "azure-maps-control": "3.3.0", "lodash-es": "4.17.21", "azure-maps-rest": "2.1.1", "@angular/forms": "17.3.11", "angular-mentions": "1.5.0", "ngx-scrollbar": "16.1.0", "@ng-bootstrap/ng-bootstrap": "16.0.0", "ngx-currency": "17.0.0", "ngx-permissions": "17.1.0", "@angular/platform-browser": "17.3.11", "@swimlane/ngx-charts": "20.5.0", "ng-circle-progress": "1.7.1", "ngx-echarts": "17.2.0", "echarts": "5.5.0", "@fincloud/swagger-generator": "0.0.1", "ngx-ui-tour-ngx-bootstrap": "12.1.0", "hot-formula-parser": "4.0.0", "dayjs": "1.11.11", "@ngx-formly/core": "6.3.3", "angular-cropperjs": "14.0.1", "ngx-infinite-scroll": "17.0.1", "@angular-slider/ngx-slider": "17.0.2", "@siemens/ngx-datatable": "22.4.1", "ngx-spinner": "17.0.0", "ngx-drag-scroll": "17.0.1", "ngx-extended-pdf-viewer": "20.2.0", "@ng-select/ng-select": "12.0.7", "@fincloud/ui": "~0.0.601", "@fincloud/utils": "0.0.1"}, "sideEffects": false, "version": "0.0.1"}