<div
  class="neoshare-background tw-flex tw-left-[0.5rem] tw-z-[999] tw-w-[101%]"
>
  @if (contentPosition === 'default') {
    <img
      src="./assets/images/neoshare-background-default.png"
      alt="background image"
      class="background-img tw-z-[2] tw-left-[-0.5rem]"
    />
  }

  @if (contentPosition === 'default') {
    <img
      src="./assets/images/neoshare-foreground-default-{{ locale }}.svg"
      alt="foreground image"
      class="regular-container-foreground tw-w-[98%] tw-z-[3] tw-absolute tw-bottom-0"
    />
  }

  @if (contentPosition === 'centered') {
    <img
      src="./assets/images/neoshare-background-centered.png"
      alt="background image"
      class="background-img tw-z-[2]"
    />
  }

  @if (contentPosition === 'centered') {
    <img
      src="assets/images/neoshare-foreground-default-small-{{ locale }}.svg"
      alt="foreground image"
      class="regular-container-foreground tw-w-full tw-z-[3] tw-absolute tw-bottom-0"
    />
  }

  <div class="content tw-z-[2]">
    <ng-content></ng-content>
  </div>
</div>
