import { DOCUMENT } from '@angular/common';
import { Injectable, inject } from '@angular/core';
import { Map, MapMouseEvent, Popup, Shape, data } from 'azure-maps-control';
import {
  BehaviorSubject,
  Observable,
  fromEvent,
  map,
  merge,
  of,
  switchMap,
  tap,
} from 'rxjs';

@Injectable()
export class AzureMapPopupService {
  private document: Document = inject(DOCUMENT);
  private popup = new Popup({
    closeButton: false,
  });

  private activePopupId$$ = new BehaviorSubject<string>('');
  readonly activePopupId$: Observable<string> = this.activePopupId$$
    .asObservable()
    .pipe(
      switchMap((popupId) =>
        popupId
          ? merge(
              fromEvent(
                this.document.querySelector('.atlas-map-canvas'),
                'click',
              ).pipe(
                tap(() => this.popup.close()),
                map(() => ''),
              ),
              of(popupId),
            )
          : of(popupId),
      ),
    );

  setPopupOptions(
    event: MapMouseEvent,
    popupContent: HTMLElement | string,
  ): void {
    const bubble = event.shapes[0];
    if (!(bubble instanceof Shape)) {
      return;
    }
    const properties = bubble.getProperties();
    if (
      !properties.objectName &&
      !properties.objectAddress &&
      !properties.caseName &&
      !properties.assetClass &&
      !properties.description &&
      !properties.title
    ) {
      return;
    }

    if (this.popup.isOpen()) {
      this.popup.close();
    }

    this.popup.setOptions({
      closeButton: false,
      sandboxContent: false,
      content: popupContent,
      position: bubble.getCoordinates() as data.Position,
      showPointer: false,
    });
    this.activePopupId$$.next(properties.caseId);
  }

  showPopup(map: Map): void {
    map.popups.add(this.popup);
    this.popup.open(map);
  }

  closePopup(): void {
    if (this.popup.isOpen()) {
      this.popup.close();
    }
  }

  movePopupToCenter(
    shapes: (data.Feature<data.Geometry, unknown> | Shape)[],
    map: Map,
  ): void {
    const [bubble] = shapes;
    if (bubble && bubble instanceof Shape) {
      const coordinates = bubble.getCoordinates();
      const currentZoom = map.getCamera().zoom;

      // Animate the camera to the clicked shape
      map.setCamera({
        center: coordinates,
        zoom: currentZoom,
        type: 'fly',
        duration: 400,
      });
    }
  }
}
