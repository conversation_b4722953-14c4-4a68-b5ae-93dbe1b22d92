import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  inject,
  Input,
  <PERSON><PERSON><PERSON>roy,
  OnInit,
  QueryList,
  ViewChildren,
} from '@angular/core';
import {
  ControlContainer,
  FormControl,
  UntypedFormGroup,
} from '@angular/forms';
import { CustomFinStructureGroup } from '@fincloud/types/models';
import { FinCheckboxComponent } from '@fincloud/ui/checkbox';
import { FinExpansionPanelComponent } from '@fincloud/ui/expansion-panel';
import { FinSize } from '@fincloud/ui/types';
import { REFS_BLOCKS_GROUP_KEY } from '@fincloud/utils';

@Component({
  selector: 'app-select-group-tree',
  templateUrl: './select-group-tree.component.html',
  styleUrls: ['./select-group-tree.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  viewProviders: [
    {
      provide: ControlContainer,
      useFactory: () => inject(ControlContainer, { skipSelf: true }),
    },
  ],
})
export class SelectGroupTreeComponent
  implements OnInit, AfterViewInit, OnD<PERSON>roy
{
  private readonly groupFormName = 'groupTreeForm';
  private readonly parentContainer: ControlContainer = inject(ControlContainer);
  public selectedGroups: string[] = [];
  public selectedSubGroups: string[] = [];
  public collectedGroupIds: string[] = [];

  @Input() disabledGroups: string[] = [];
  @Input() set sharedGroups(value: {
    staticGroups: CustomFinStructureGroup[];
    sharedEntities: string[];
  }) {
    if (!value || !value.staticGroups) {
      return;
    }
    this.groupTreeForm.reset();
    this.addControl(value.staticGroups);
    this.selectGroupPerParticipant(value.sharedEntities);
    this.collectedGroupIds = value.sharedEntities;
    this.groups = value.staticGroups;
  }
  groups: CustomFinStructureGroup[] = [];

  @ViewChildren(FinCheckboxComponent)
  checkboxesRefsList: QueryList<FinCheckboxComponent>;

  finSize = FinSize;

  get parentFormGroup() {
    return this.parentContainer.control as UntypedFormGroup;
  }

  get groupTreeForm() {
    return this.parentFormGroup.controls.groupTreeForm as UntypedFormGroup;
  }

  ngOnInit(): void {
    this.parentFormGroup.addControl(
      this.groupFormName,
      new UntypedFormGroup({}),
    );
  }

  ngAfterViewInit() {
    this.checkboxesRefsList.forEach((checkboxRef) => {
      const currentGroup = this.groups.find(
        (group) => group.id == checkboxRef.formControlName,
      );
      this.setCheckboxIndeterminateState(currentGroup, checkboxRef);
    });
  }

  preventClickPropagation(event: Event): void {
    event.stopImmediatePropagation();
  }

  handleOpenPanel(
    panel: FinExpansionPanelComponent,
    group: CustomFinStructureGroup,
  ): void {
    if (!group.subGroups.length) {
      //Do not open panel without subgroups
      panel.accordion.closeAll();
    }
  }

  ngOnDestroy(): void {
    this.parentFormGroup.removeControl(this.groupFormName);
  }

  toggleIndividual(
    value: boolean,
    subGroupId: string,
    mainGroup: CustomFinStructureGroup,
  ): void {
    const [checked, unchecked] = [value, !value];
    checked && this.handleCheckGroup(subGroupId, mainGroup);
    unchecked && this.handleUncheckGroup(subGroupId, mainGroup);
  }

  toggleAll(checked: boolean, group: CustomFinStructureGroup): void {
    this.selectedGroups = checked
      ? this.selectedGroups.concat([group.id])
      : this.selectedGroups.filter((id) => id !== group.id);

    group.subGroups.forEach((sg) =>
      this.disabledGroups.includes(sg.id)
        ? null
        : this.groupTreeForm.controls[sg.id]?.setValue(checked),
    );
    const subGroupsIds = group.subGroups
      .map((sg) => (this.disabledGroups.includes(sg.id) ? null : sg.id))
      .filter(Boolean);
    this.selectedSubGroups = checked
      ? this.selectedSubGroups.concat(subGroupsIds)
      : this.selectedSubGroups.filter((id) => !subGroupsIds.includes(id));

    this.collectedGroupIds = checked
      ? this.collectedGroupIds.concat([group.id])
      : this.collectedGroupIds.filter((id) => id !== group.id);
    this.collectSubGroupIds(group.subGroups, checked);

    const currentGroupCheckboxRef = this.checkboxesRefsList.find(
      (checkbox) => checkbox.formControlName == group.id,
    );

    this.setCheckboxIndeterminateState(group, currentGroupCheckboxRef);
  }

  selectedSubGroupsWithinGroup(
    groups: string[],
    subGroups: CustomFinStructureGroup[],
  ): number {
    return subGroups.filter((sb) => groups.includes(sb.id)).length;
  }

  private addControl(groups: CustomFinStructureGroup[]): void {
    groups.forEach((group) => {
      this.groupTreeForm.addControl(
        group.id,
        new FormControl({
          value: false,
          disabled: this.disabledGroups.includes(group.id),
        }),
      );
      if (group.subGroups?.length) {
        this.addControl(group.subGroups);
      }
    });
  }

  private selectGroupPerParticipant(groupIds: string[]): void {
    this.selectedGroups = [];
    this.selectedSubGroups = [];
    Object.keys(this.groupTreeForm.controls).forEach((c) =>
      this.groupTreeForm.controls[c].setValue(false),
    );

    const buildingBlockGroup = this.groups.find(
      (g) => g.key === REFS_BLOCKS_GROUP_KEY,
    );
    if (buildingBlockGroup) {
      this.handleBuildingBlockGroupPerParticipant(buildingBlockGroup, groupIds);
    }

    groupIds.forEach((groupId) => {
      this.groupTreeForm.controls[groupId]?.setValue(true);
      if (this.groups.find((group) => group.id === groupId)) {
        this.selectedGroups = this.selectedGroups.concat([groupId]);
        return;
      }
      this.selectedSubGroups = this.selectedSubGroups.concat([groupId]);

      const mainGroup = this.groups.find((group) =>
        group.subGroups.find((subGroup) => subGroup.id === groupId),
      );
      if (!mainGroup) {
        return;
      }
      if (this.selectedGroups.includes(mainGroup.id)) {
        return;
      }
      this.selectedGroups = this.selectedGroups.concat([mainGroup.id]);
    });
  }

  private collectSubGroupIds(
    groups: CustomFinStructureGroup[],
    checked: boolean,
  ): void {
    groups.forEach((group) => {
      this.collectedGroupIds = checked
        ? this.collectedGroupIds.concat([group.id])
        : this.collectedGroupIds.filter((id) => id !== group.id);
      if (group.subGroups.length) {
        this.collectSubGroupIds(group.subGroups, checked);
      }
    });
  }

  private handleBuildingBlockGroupPerParticipant(
    group: CustomFinStructureGroup,
    groupIds: string[],
  ): void {
    if (
      group.subGroups.length &&
      group.subGroups.every((subGroup) => groupIds.includes(subGroup.id)) &&
      !this.selectedGroups.includes(group.id)
    ) {
      this.selectedGroups = this.selectedGroups.concat([group.id]);
      this.groupTreeForm.controls[group.id]?.setValue(true);
    }
  }

  private handleCheckGroup(
    subGroupId: string,
    mainGroup: CustomFinStructureGroup,
  ): void {
    if (!this.collectedGroupIds.includes(subGroupId)) {
      this.collectedGroupIds = this.collectedGroupIds.concat([subGroupId]);
    }
    if (!this.selectedSubGroups.includes(subGroupId)) {
      this.selectedSubGroups = this.selectedSubGroups.concat([subGroupId]);
    }

    if (!this.selectedGroups.includes(mainGroup.id)) {
      this.selectedGroups = this.selectedGroups.concat([mainGroup.id]);
    }

    if (!this.collectedGroupIds.includes(mainGroup.id)) {
      this.collectedGroupIds = this.collectedGroupIds.concat([mainGroup.id]);
    }

    this.collectSubGroupIds(
      mainGroup.subGroups.find((subGroup) => subGroup.id === subGroupId)
        .subGroups,
      true,
    );

    const currentGroupCheckboxRef = this.checkboxesRefsList.find(
      (checkbox) => checkbox.formControlName == mainGroup.id,
    );
    const currentGroup = this.groups.find((group) => group.id == mainGroup.id);

    if (
      mainGroup.subGroups.every(
        (sg) =>
          this.selectedSubGroups.includes(sg.id) ||
          this.disabledGroups.includes(sg.id),
      )
    ) {
      currentGroupCheckboxRef.setIndeterminateState(false);
      this.groupTreeForm.controls[mainGroup.id]?.setValue(true);
    } else {
      this.setCheckboxIndeterminateState(currentGroup, currentGroupCheckboxRef);
    }
  }

  private handleUncheckGroup(
    subGroupId: string,
    mainGroup: CustomFinStructureGroup,
  ): void {
    this.collectedGroupIds = this.collectedGroupIds.filter(
      (id) => id !== subGroupId,
    );
    this.selectedSubGroups = this.selectedSubGroups.filter(
      (id) => id !== subGroupId,
    );

    this.collectSubGroupIds(
      mainGroup.subGroups.find((subGroup) => subGroup.id === subGroupId)
        .subGroups,
      false,
    );

    const currentGroupCheckboxRef = this.checkboxesRefsList.find(
      (checkbox) => checkbox.formControlName == mainGroup.id,
    );
    const currentGroup = this.groups.find((group) => group.id == mainGroup.id);
    this.setCheckboxIndeterminateState(currentGroup, currentGroupCheckboxRef);

    if (
      !mainGroup.subGroups.some((sb) =>
        this.selectedSubGroups.includes(sb.id),
      ) &&
      mainGroup.fields.length === 0
    ) {
      this.selectedGroups = this.selectedGroups.filter(
        (id) => id !== mainGroup.id,
      );
      this.collectedGroupIds = this.collectedGroupIds.filter(
        (id) => id !== mainGroup.id,
      );
      currentGroupCheckboxRef.setIndeterminateState(false);
      this.groupTreeForm.controls[mainGroup.id]?.setValue(false);
      return;
    }

    if (
      !this.collectedGroupIds.includes(mainGroup.id) &&
      !!mainGroup.fields.length
    ) {
      this.collectedGroupIds.push(mainGroup.id);
    }

    currentGroupCheckboxRef.setIndeterminateState(true);
  }

  private setCheckboxIndeterminateState(
    group: CustomFinStructureGroup,
    checkboxRef: FinCheckboxComponent,
  ): void {
    const currentGroupSelectedSubgroupsCount: number =
      this.selectedSubGroupsWithinGroup(
        this.selectedSubGroups,
        group.subGroups,
      );

    const disabledSubGroupsCount = group.subGroups.filter((sb) =>
      this.disabledGroups?.includes(sb.id),
    ).length;

    const areLessThanAllSubgroupsSelected =
      group.subGroups.length > 0 &&
      currentGroupSelectedSubgroupsCount + disabledSubGroupsCount <
        group.subGroups.length &&
      currentGroupSelectedSubgroupsCount != 0;

    const areGroupFieldsSelected =
      this.selectedGroups.includes(group.id) &&
      group.subGroups.length > 0 &&
      !currentGroupSelectedSubgroupsCount &&
      group.fields.length > 0;

    checkboxRef.setIndeterminateState(
      areLessThanAllSubgroupsSelected || areGroupFieldsSelected,
    );
  }
}
