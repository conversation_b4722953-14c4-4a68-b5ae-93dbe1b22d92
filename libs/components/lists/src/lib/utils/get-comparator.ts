import { TableRow } from '@fincloud/types/models';

/**
 * Returns comparator for columns in ngx-datatable based components
 *
 * @param {((row: TRow) => string | number | Date)} valueGetter
 */
export const getComparator = <TRow extends TableRow>(
  valueGetter: (row: TRow) => string | number | Date,
) => {
  return ((
    _valueA: unknown,
    _valueB: unknown,
    rowA: TRow,
    rowB: TRow,
    _sortDirection: 'asc' | 'desc',
  ) => {
    const valueA = valueGetter(rowA);
    const valueB = valueGetter(rowB);

    if (valueA === valueB) {
      return 0;
    }

    return valueA < valueB ? -1 : 1;
  }) as (
    _valueA: unknown,
    _valueB: unknown,
    rowA: TableRow,
    rowB: TableRow,
    sortDirection: 'asc' | 'desc',
  ) => 0 | -1 | 1;
};
