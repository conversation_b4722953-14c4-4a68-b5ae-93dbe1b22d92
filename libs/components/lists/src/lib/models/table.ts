/* eslint @typescript-eslint/no-explicit-any: 0 */
import { PipeTransform, Type } from '@angular/core';
import { TableRow } from '@fincloud/types/models';

export interface TableColumn<RowData = TableRow> {
  id?: string;
  prop?: string;
  // Extracting a value may be more complex than a single prop access
  // E.g. when a custom template is used in a column and >1 row values are displayed together in it
  propExpression?: (row: RowData) => string;
  name?: string;
  width: number;
  maxWidth?: number;
  sortable?: boolean;
  filterable?: boolean;
  component?: Type<unknown>;
  // if custom template is used and columns is also sortable, it's highly likely that a comparator needs to be provided
  isCustomCellTemplate?: boolean;
  inputName?: string;
  pipe?: PipeTransform;
  inputClass?: string;
  comparator?: (
    valueA: unknown,
    valueB: unknown,
    rowA: any,
    rowB: any,
    sortDirection: 'asc' | 'desc',
  ) => -1 | 0 | 1;
  cellClass?: string;
  headerClass?: string | ((data: any) => string | any);
}
