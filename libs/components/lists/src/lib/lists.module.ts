import { AgGridModule } from '@ag-grid-community/angular';
import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AvatarComponent } from '@fincloud/components/avatar';
import { NsUiBooleansModule } from '@fincloud/components/booleans';
import { NsUiButtonsModule } from '@fincloud/components/buttons';
import { NsUiChartsModule } from '@fincloud/components/charts';
import { NsUiDataGridModule } from '@fincloud/components/data-grid';
import { NsUiDropdownsModule } from '@fincloud/components/dropdowns';
import { NsUiHorizontalDividerModule } from '@fincloud/components/horizontal-divider';
import { NsUiIconsModule } from '@fincloud/components/icons';
import { NsUiListsFiltersModule } from '@fincloud/components/lists-filters';
import { NsUiLoadersModule } from '@fincloud/components/loaders';
import { NsUiNavigationModule } from '@fincloud/components/navigation';
import { NsUiSelectsModule } from '@fincloud/components/selects';
import { NsUiStepperModule } from '@fincloud/components/stepper';
import { NsUiTextModule } from '@fincloud/components/text';
import { NsUiTooltipModule } from '@fincloud/components/tooltip';
import { NsUiTruncatedTextModule } from '@fincloud/components/truncated-text';
import { NsCoreFocusModule } from '@fincloud/core/focus';
import { NsCorePipesModule } from '@fincloud/core/pipes';
import { FinLoaderModule } from '@fincloud/ui/loader';
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import { NgxDatatableModule } from '@siemens/ngx-datatable';
import { CompanyCellComponent } from './components/company-cell/company-cell.component';
import { CustomerCellComponent } from './components/customer-cell/customer-cell.component';
import { DynamicDisplayerComponent } from './components/dynamic-displayer/dynamic-displayer.component';
import { ExpandableListComponent } from './components/expandable-list/expandable-list.component';
import { FluidCheckboxTableComponent } from './components/fluid-table-checkbox/fluid-table-checkbox.component';
import { FluidTableComponent } from './components/fluid-table/fluid-table.component';
import { InteractiveListItemComponent } from './components/interactive-list-item/interactive-list-item.component';
import { InteractiveListComponent } from './components/interactive-list/interactive-list.component';
import { KpiMetricListComponent } from './components/kpi-metric-list/kpi-metric-list.component';
import { SortArrowsComponent } from './components/sort-arrows/sort-arrows.component';
import { SortCriteriaDropdownComponent } from './components/sort-criteria-dropdown/sort-criteria-dropdown.component';
import { SortableListComponent } from './components/sortable-list/sortable-list.component';
import { SplitButtonComponent } from './components/split-button/split-button.component';
import { TableComponent } from './components/table/table.component';
import { UserCarouselComponent } from './components/user-carousel/user-carousel.component';

@NgModule({
  imports: [
    CommonModule,
    NsUiBooleansModule,
    NsUiButtonsModule,
    NsUiChartsModule,
    DragDropModule,
    FormsModule,
    NsUiIconsModule,
    NsUiNavigationModule,
    NgxDatatableModule,
    NsUiTruncatedTextModule,
    NsUiTextModule,
    AgGridModule,
    ReactiveFormsModule,
    NgxSliderModule,
    NsCorePipesModule,
    NsUiSelectsModule,
    NsUiStepperModule,
    NsUiTooltipModule,
    NsCoreFocusModule,
    NsUiDropdownsModule,
    NgbDropdownModule,
    AvatarComponent,
    NsUiDataGridModule,
    NsUiListsFiltersModule,
    NsUiLoadersModule,
    NsUiHorizontalDividerModule,
    FinLoaderModule,
  ],
  declarations: [
    FluidCheckboxTableComponent,
    CompanyCellComponent,
    CustomerCellComponent,
    DynamicDisplayerComponent,
    ExpandableListComponent,
    FluidTableComponent,
    InteractiveListComponent,
    InteractiveListItemComponent,
    SortArrowsComponent,
    SortCriteriaDropdownComponent,
    SortableListComponent,
    SplitButtonComponent,
    TableComponent,
    UserCarouselComponent,
    KpiMetricListComponent,
  ],
  exports: [
    FluidCheckboxTableComponent,
    CompanyCellComponent,
    CustomerCellComponent,
    DynamicDisplayerComponent,
    ExpandableListComponent,
    FluidTableComponent,
    InteractiveListComponent,
    InteractiveListItemComponent,
    SortArrowsComponent,
    SortCriteriaDropdownComponent,
    SortableListComponent,
    SplitButtonComponent,
    TableComponent,
    UserCarouselComponent,
    KpiMetricListComponent,
  ],
})
export class NsUiListsModule {}
