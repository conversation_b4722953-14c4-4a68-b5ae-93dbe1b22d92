import { LayoutCommunicationService } from '@fincloud/core/layout';

import {
  AfterViewChecked,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FluidTableSorting, TableRow } from '@fincloud/types/models';
import {
  ColumnMode,
  DataTablePagerComponent,
  DatatableComponent,
  SelectionType,
} from '@siemens/ngx-datatable';
import { FluidTableColumn } from '../../models/fluid-table';
import { Page } from '../../models/page';

@Component({
  selector: 'ui-fluid-table',
  templateUrl: './fluid-table.component.html',
  styleUrls: ['./fluid-table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FluidTableComponent
  implements OnInit, OnChanges, AfterViewChecked
{
  @ViewChild('table', { static: true }) table: DatatableComponent;

  @ViewChild('tablePaginator')
  tablePaginator: DataTablePagerComponent;

  @ViewChild('table', { read: ViewContainerRef, static: true })
  tableView: ViewContainerRef;

  // @ViewChild('tableWrapper') tableWrapper: ElementRef;

  @Input() columns: FluidTableColumn[];

  @Input() set rows(tableRows: TableRow[]) {
    this._rows = tableRows;
  }
  get rows(): TableRow[] {
    return this._rows;
  }
  _rows: TableRow[] = [];

  @Input() templates: { [columnName: string]: TemplateRef<unknown> };
  @Input() rowDetailTemplate: TemplateRef<unknown>;
  @Input() searchTerm: string;
  @Input()
  noDataAvailableMessage =
    $localize`:@@table.noDataAvailable.message:Keine Daten vorhanden.`;
  @Input() defaultSort: FluidTableSorting[] = [];
  @Input() selectable = true;
  @Input() highlightSelectedRow = false;
  @Input() rowDetailHeight: number;
  @Input() selectionType: SelectionType = SelectionType.single;
  @Input() dashboardStyling = false;
  @Input() scrollV = false;
  @Input() pageLimit: number;
  @Input() rowHeight = 40;
  @Input() headerHeight = 50;
  @Input() footerHeight = 0;
  @Input() showSpinner = false;
  @Input() materialClassEnabled = false;
  @Input() externalPaging = false;
  @Input() count: number = undefined;
  @Input() offset = 0;
  @Input() externalSorting = false;

  @Input() highlightedRowValue = '';
  @Input() highlightedRowKey = '';

  @Output() pageChange = new EventEmitter<Page>();
  @Output() sortChange = new EventEmitter<FluidTableSorting>();

  @Output()
  rowSelected = new EventEmitter<TableRow>();
  selected: [TableRow] | [] = [];

  @Output()
  loadNewData = new EventEmitter();

  ColumnMode = ColumnMode;

  scrollIsLoading = false;

  messages = {
    emptyMessage: '',
  };

  currentComponentWidth: number;

  constructor(
    private destroyRef: DestroyRef,
    private layoutCommunicationService: LayoutCommunicationService,
    private el: ElementRef,
    private changeDetectorRef: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    if (!this.showSpinner && this.noDataAvailableMessage) {
      this.messages.emptyMessage = this.noDataAvailableMessage;
    }

    this.layoutCommunicationService.layoutChanged$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: () => this.table?.recalculate(),
      });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.noDataAvailableMessage) {
      this.messages.emptyMessage = this.noDataAvailableMessage;
    }
  }

  forceChangeDetection() {
    const changeDetectorRef = this.tableView.injector.get(ChangeDetectorRef);
    changeDetectorRef.detectChanges();
  }

  ngAfterViewChecked() {
    if (
      this.table &&
      this.table.recalculate &&
      this.el.nativeElement.clientWidth !== this.currentComponentWidth
    ) {
      this.currentComponentWidth = this.el.nativeElement.clientWidth;
      this.table.recalculate();
      this.changeDetectorRef.detectChanges();
    }
  }

  onSelect(selection: { selected: TableRow[] }) {
    if (this.selectable) {
      const [selectedRow] = selection.selected;
      this.rowSelected.emit(selectedRow);
    }
  }

  toggleExpandRow(row: TableRow) {
    this.table.rowDetail.toggleExpandRow(row);
  }

  collapseAllRows() {
    this.table.rowDetail.collapseAllRows();
  }

  getRowClass = (row: TableRow) => {
    return {
      highlighted: row[this.highlightedRowKey] === this.highlightedRowValue,
      [row[this.highlightedRowKey] as string]: true,
      'highlight-selected-row': this.highlightSelectedRow,
    };
  };

  onScroll(offsetY: number) {
    const viewHeight =
      this.el.nativeElement.getBoundingClientRect().height - this.headerHeight;
    const endScroll = offsetY + viewHeight >= this.rows.length * this.rowHeight;
    if (endScroll) {
      this.loadNewData.emit();
    }
  }

  onSort(sorting: { sorts: FluidTableSorting[] }) {
    if (!this.externalSorting) {
      return;
    }

    // Fix to automatic reset to page 1 when sorting (default behaviour of ngx datatable) -> Read more here: https://github.com/swimlane/ngx-datatable/issues/765
    this.table.offset = this.table?.bodyComponent?.offset;

    /* sorting.sorts is an array of all sorting currently selected.
     We do not support combined sortings,
     so this is why we are always using the first element in the array */
    this.sortChange.emit(sorting.sorts[0]);
  }

  onPage(paging: Page) {
    if (!this.externalPaging) {
      return;
    }

    const page = { ...paging };
    page.offset = paging.offset * paging.limit;

    this.pageChange.emit(page);
  }

  getFirstNumberText(page: number) {
    if (isNaN(page)) {
      return '0';
    }

    return (page * this.pageLimit - (this.pageLimit - 1)).toString();
  }

  getSecondNumberText(page: number) {
    if (isNaN(page)) {
      return '0';
    }

    return this.tablePaginator?.canNext()
      ? `${page * this.pageLimit}`
      : `${this.count}`;
  }
}
