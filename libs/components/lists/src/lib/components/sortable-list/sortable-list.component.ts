import {
  CDK_DRAG_CONFIG,
  CdkDragDrop,
  moveItemInArray,
} from '@angular/cdk/drag-drop';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  AbstractValueAccessor,
  makeControlValueAccessorProvider,
} from '@fincloud/core/form';
import { filter, maxBy, sortBy } from 'lodash-es';
import { Subject, debounceTime, distinctUntilChanged } from 'rxjs';
import { SortableListItem } from '../../models/sortable-list-item';
import { DRAG_CONFIG } from '../../utils/drag-config';

@Component({
  selector: 'ui-sortable-list',
  templateUrl: './sortable-list.component.html',
  styleUrls: ['./sortable-list.component.scss'],
  providers: [
    makeControlValueAccessorProvider(SortableListComponent),
    { provide: CDK_DRAG_CONFIG, useValue: DRAG_CONFIG },
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SortableListComponent
  extends AbstractValueAccessor<SortableListItem[]>
  implements OnInit, OnChanges
{
  @Input()
  options: string[] = [];

  @Input()
  addCustomItemText: string;

  @Output()
  itemSearch = new EventEmitter<string>();

  constructor(
    private destroyRef: DestroyRef,
    changeDetectorRef: ChangeDetectorRef,
  ) {
    super(changeDetectorRef);
  }

  get items() {
    return this.value;
  }

  set items(items: SortableListItem[]) {
    this.value = items;
  }

  suggestedItems: Record<string, unknown>[] = [];
  itemsSearch$ = new Subject<string>();

  ngOnInit(): void {
    this.itemsSearch$
      .pipe(
        distinctUntilChanged(),
        debounceTime(200),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((term) => this.itemSearch.emit(term));
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.options) {
      this.suggestedItems =
        this.options?.map((o) => ({ label: o, value: o })) ?? [];
    }
  }

  writeValue(value: SortableListItem[]): void {
    super.writeValue(sortBy(value, (v) => v.order));
  }

  drop(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.items, event.previousIndex, event.currentIndex);
    let startingIndex = 1;
    this.items = this.items.map((item) => {
      item.order = startingIndex++;
      return item;
    });
  }

  addNewEmptyItem() {
    const lastItem = maxBy(this.value, (v) => v.order);
    this.items = [
      ...this.items,
      {
        label: '',
        order: lastItem.order + 1,
        required: false,
        isExcludedFromCalculations: false,
      },
    ];
  }

  removeItem(index: number) {
    this.items = filter(this.items, (_, i) => i !== index);
  }

  onLabelUpdated() {
    this.valueChanged(this.items);
  }

  onIsAdditionalChanged() {
    this.valueChanged(this.items);
  }

  trackByLabel(index: number, item: SortableListItem) {
    return item.label;
  }
}
