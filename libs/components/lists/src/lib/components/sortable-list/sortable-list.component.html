<div class="sortable-list-container">
  <div cdkDropList (cdkDropListDropped)="drop($event)">
    @for (item of items; track trackByLabel(i, item); let i = $index) {
      <div>
        <div cdkDrag class="mb-5">
          <div class="item-wrapper">
            <ui-select
              [(ngModel)]="item.label"
              [options]="suggestedItems"
              [typeAhead]="itemsSearch$"
              labelKey="label"
              valueKey="label"
              [addTag]="true"
              [addTagText]="addCustomItemText"
              size="large"
              [hideBorder]="false"
              (selectionChange)="onLabelUpdated()"
              class="sortable-list-autocomplete"
            >
            </ui-select>
            <div class="drag-remove-wrapper">
              <div class="d-flex justify-content-end">
                <ui-icon
                  name="drag-indicator"
                  class="drag"
                  size="medium"
                ></ui-icon>
                @if (items?.length > 1) {
                  <ui-icon
                    name="remove-circle"
                    class="remove"
                    (click)="removeItem(i)"
                  ></ui-icon>
                }
              </div>
            </div>
          </div>
          <ui-checkbox
            [(ngModel)]="item.isExcludedFromCalculations"
            (ngModelChange)="onIsAdditionalChanged()"
            label="Sicherheit"
            i18n-label="@@sortableList.label.checkbox"
            type="dark"
          ></ui-checkbox>
        </div>
      </div>
    }

    <div class="d-flex justify-content-end">
      <ui-button
        size="small"
        type="outline"
        corners="rounded"
        label="Hinzufügen"
        i18n-label="@@sortableList.label.addNew"
        (clicked)="addNewEmptyItem()"
      >
      </ui-button>
    </div>
  </div>
</div>
