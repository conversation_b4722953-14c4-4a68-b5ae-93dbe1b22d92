<div class="kpis">
  @for (kpiRow of kpiRows; track kpiRow; let i = $index) {
    @if (i > 0) {
      <ui-horizontal-divider
        color="gray"
        borderSize="small"
      ></ui-horizontal-divider>
    }
    <div class="kpi-row">
      @for (kpi of kpiRow; track kpi; let last = $last) {
        <div
          class="kpi"
          [style.flex-basis]="kpi?.flexBasis"
          [class]="kpi?.cssClasses"
        >
          <div>
            <span class="label">
              {{ kpi.title }}
            </span>
            <span class="value">{{ kpi.value | removeTrailingZeros }}</span>
          </div>
        </div>
        @if (!last) {
          <div class="divider"></div>
        }
      }
    </div>
  }
</div>
