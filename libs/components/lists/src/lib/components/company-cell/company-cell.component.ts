import { Component, Input } from '@angular/core';
import { BusinessCaseTableRow } from '@fincloud/types/models';
import {
  BUSINESS_CASE_TYPE_ICONS,
  BUSINESS_CASE_TYPE_LABELS,
} from '@fincloud/utils';

@Component({
  selector: 'ui-company-cell',
  templateUrl: './company-cell.component.html',
  styleUrls: ['./company-cell.component.scss'],
})
export class CompanyCellComponent {
  @Input()
  row: BusinessCaseTableRow;

  @Input()
  showCompanyName = true;

  @Input()
  showCaseType = true;

  @Input()
  idPrefix = 'ID';

  caseIcons = BUSINESS_CASE_TYPE_ICONS;
  caseLabels = BUSINESS_CASE_TYPE_LABELS;
}
