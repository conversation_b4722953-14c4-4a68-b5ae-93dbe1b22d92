import { DOCUMENT } from '@angular/common';
import {
  Component,
  DestroyRef,
  EventEmitter,
  HostListener,
  Inject,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { LayoutCommunicationService } from '@fincloud/core/layout';
import { setTimeoutUnpatched } from '@fincloud/core/utils';
import { TableRow } from '@fincloud/types/models';
import {
  ColumnMode,
  DatatableComponent,
  SelectionType,
} from '@siemens/ngx-datatable';
import { BehaviorSubject, debounceTime, filter, merge } from 'rxjs';
import { FluidTableColumn } from '../../models/fluid-table';
import { CANCEL_LABEL_BTN } from '../../utils/cancel-label-btn';
import { EMPTY_MESSAGE } from '../../utils/empty-message';

@Component({
  selector: 'ui-fluid-table-checkbox',
  templateUrl: './fluid-table-checkbox.html',
  styleUrls: ['./fluid-table-checkbox.scss'],
})
export class FluidCheckboxTableComponent implements OnInit, OnChanges {
  @Input() defaultSort: { prop: string; dir: 'asc' | 'desc' }[] = [];
  @Input() templates: { [columnName: string]: TemplateRef<unknown> };
  @Input() rowDetailTemp: TemplateRef<unknown>;
  @Input() actionsTemplate: TemplateRef<unknown>;
  @Input() tableHeight: number;
  @Input() columns: FluidTableColumn[];
  @Input() headerHeight: number;
  @Input() verticalScroll = false;
  @Input() virtualization = false;
  @Input() rowHeigh = 40;
  @Input() highlightSelectedRow = false;
  @Input() columnMode: ColumnMode = ColumnMode.force;
  @Input() selectionType: SelectionType = SelectionType.checkbox;
  @Input() rowClassFunc: (row: TableRow) => { [ket: string]: boolean };
  @Input() rows: unknown[];
  @Input() conditionRowToShowTemplate: string;
  @Input() trackByProp: string;
  @Input() disableDrag: boolean;
  @Input() focusId: string;

  @ViewChild('table', { static: true }) table: DatatableComponent;
  @Output() rowsSelected = new EventEmitter<TableRow[]>();
  @Output() dismissDocumentWarning = new EventEmitter<TableRow>();
  @Output() clickRow = new EventEmitter<TableRow>();
  @Output() blurRow = new EventEmitter<void>();

  draggedDocumentsMetaData: TableRow[];
  draggedRowElement: HTMLElement;
  isItemDragging = false;
  selectedRows: TableRow[] = [];
  emptyMessage = EMPTY_MESSAGE;
  cancelLabel = CANCEL_LABEL_BTN;
  defaultTableHeight = 450;

  onTableResize$ = new BehaviorSubject(false);

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.onTableResize$.next(true);
  }

  constructor(
    private destroyRef: DestroyRef,
    @Inject(DOCUMENT) private document: Document,
    private layoutCommunicationService: LayoutCommunicationService,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.rows) {
      //why 500 ? 200 didnot work
      setTimeoutUnpatched(() => {
        this.table?.recalculate();
      }, 500);
    }
  }

  ngOnInit(): void {
    merge(
      this.layoutCommunicationService.layoutChanged$,
      this.onTableResize$.pipe(filter(Boolean)),
    )
      .pipe(takeUntilDestroyed(this.destroyRef), debounceTime(500))
      .subscribe({
        next: () => {
          this.table?.recalculate();
          this.table?.recalculateColumns();
        },
      });
  }

  onActivate(event: {
    event: Event;
    row: TableRow;
    rowElement: HTMLElement;
    type: string;
  }) {
    if (!this.isItemDragging) {
      this.draggedRowElement = event.rowElement;
    }
  }

  onSelect(selection: { selected: TableRow[] }) {
    this.selectedRows.splice(0, this.selectedRows.length);
    if (selection?.selected?.length) {
      this.selectedRows.push(...selection.selected);
    }

    this.rowsSelected.emit(this.selectedRows);
  }

  onDragStart(row: TableRow) {
    const rowIndex = this.selectedRows.indexOf(row);
    this.resetMetaData();
    this.draggedDocumentsMetaData =
      rowIndex > -1 ? this.selectedRows.slice() : [row];

    this.highlightSelectedRow = rowIndex > -1;

    if (!this.highlightSelectedRow && this.draggedRowElement) {
      this.draggedRowElement.classList.add('highlight-dragging-row');
    }

    this.isItemDragging = true;
    this.document.body.classList.add('dragging');
  }

  onDragEnd() {
    if (this.highlightSelectedRow) {
      this.clearSelection();
    }
    this.highlightSelectedRow = false;
    this.resetMetaData();
    if (this.draggedRowElement) {
      this.draggedRowElement.classList.remove('highlight-dragging-row');
    }

    this.isItemDragging = false;
    this.document.body.classList.remove('dragging');
  }

  isDraggingRow(row: TableRow) {
    return this.draggedDocumentsMetaData?.indexOf(row) > -1;
  }

  resetMetaData() {
    this.draggedDocumentsMetaData = [];
  }

  clearSelection() {
    this.table.selected = [];
    this.selectedRows = [];
    this.rowsSelected.emit([]);
  }

  extendRow(row: unknown) {
    this.table.rowDetail.toggleExpandRow(row);
  }

  dismissWarning(row: TableRow) {
    this.dismissDocumentWarning.emit(row);
  }

  onClick(row: TableRow) {
    this.clickRow.emit(row);
  }

  onBlur(): void {
    this.blurRow.emit();
  }
}
