import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import {
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { InteractiveListItem } from '../../models/interactive-list-item';

@Component({
  selector: 'ui-interactive-list-item',
  templateUrl: './interactive-list-item.component.html',
  styleUrls: ['./interactive-list-item.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InteractiveListItemComponent implements OnInit {
  @Input() item: InteractiveListItem<unknown>;

  @Input() mode: 'add' | 'edit' = 'edit';

  @Input()
  allowEmptyText = true;

  @Output()
  remove = new EventEmitter<InteractiveListItem<unknown>>();

  @Output()
  add = new EventEmitter<InteractiveListItem<unknown>>();

  @Output()
  edit = new EventEmitter<InteractiveListItem<unknown>>();

  interactiveItemText: UntypedFormControl = new UntypedFormControl();

  interactiveItemForm = new UntypedFormGroup({
    interactiveItemText: this.interactiveItemText,
  });

  ngOnInit(): void {
    if (this.item) {
      this.interactiveItemText.setValue(this.item.text);
    } else {
      this.resetForm();
    }

    if (!this.allowEmptyText) {
      this.interactiveItemText.addValidators(Validators.required);
    }
  }

  onRemove() {
    this.remove.next(this.getItemFromForm());
  }

  onEdit() {
    if (this.interactiveItemForm.valid) {
      this.edit.next(this.getItemFromForm());
    }
  }

  onAdd() {
    if (this.interactiveItemForm.valid) {
      this.add.next(this.getItemFromForm());
      this.resetForm();
    }
  }

  getItemFromForm(): InteractiveListItem<unknown> {
    return {
      ...this.item,
      text: this.interactiveItemText.value as string,
    };
  }

  resetForm() {
    this.interactiveItemForm.reset({
      interactiveItemText: '',
    });
  }
}
