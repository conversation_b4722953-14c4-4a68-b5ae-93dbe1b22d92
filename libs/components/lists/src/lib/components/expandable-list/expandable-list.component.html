<div
  class="expandable-list-header"
  [hidden]="!displayHeader"
  [class.expanded]="expanded"
  (click)="toggleExpand()"
>
  <span class="title">{{ listLabel }}</span>
  @if (numberOfItems) {
    <ui-step-number
      class="number"
      [number]="numberOfItems"
      [backgroundColor]="expanded ? 'dark' : 'light-blue'"
      [color]="expanded ? 'white' : 'dark'"
    >
    </ui-step-number>
  }
</div>
@if (expanded) {
  <div class="expandable-list-items">
    @for (item of items; track item) {
      <div
        class="expandable-list-item"
        [class.selected]="selectedItem === item"
        (click)="selectItem(item)"
      >
        {{ item.label }}
      </div>
    }
  </div>
}
