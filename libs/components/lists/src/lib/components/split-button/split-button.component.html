<div class="sort-list__wrapper">
  <div class="split-button d-flex" ngbDropdown #dropdown="ngbDropdown">
    <ui-button
      class="main-button"
      size="medium"
      type="fill"
      [label]="mainButtonLabel"
      [disabled]="disabled"
      (clicked)="setMainButton()"
    >
    </ui-button>
    <div class="vertical-divider" [ngClass]="{ disabled }"></div>
    <ui-button
      ngbDropdownToggle
      class="split-dropdown-toggle"
      size="medium"
      type="fill"
      [disabled]="disabled"
      [icon]="'expand_more'"
    ></ui-button>
    @if (!disabled) {
      <div class="dropdown-menu" ngbDropdownMenu>
        @for (option of options; track option) {
          <ui-dropdown-item
            class="split-options"
            [icon]="option.icon"
            [label]="option.displayValue"
            (clicked)="setOptionButton(option)"
          ></ui-dropdown-item>
        }
      </div>
    }
  </div>
</div>
