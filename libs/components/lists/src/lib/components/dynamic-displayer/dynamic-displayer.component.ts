import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  SimpleChanges,
  Type,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { DynamicComponentInput } from '../../models/dynamic-displayer';

@Component({
  selector: 'ui-dynamic-displayer',
  templateUrl: './dynamic-displayer.component.html',
  styleUrls: ['./dynamic-displayer.component.scss'],
})
export class DynamicDisplayerComponent implements AfterViewInit, OnChanges {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  @Input() component: Type<any>;
  @Input() inputNames: string[];
  @Input() inputValues: unknown[];
  @Input() input: DynamicComponentInput;
  @ViewChild('dynamic', { read: ViewContainerRef, static: false })
  viewRef: ViewContainerRef;

  constructor(private changeDetectorRef: ChangeDetectorRef) {}

  private loadComponent(): void {
    this.viewRef.clear();
    const componentRef = this.viewRef.createComponent(this.component);
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    componentRef.instance[this.input.name] = this.input.value;
  }

  ngAfterViewInit(): void {
    this.loadComponent();
    this.changeDetectorRef.detectChanges();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.input && !changes.input?.firstChange) {
      this.loadComponent();
    }
  }
}
