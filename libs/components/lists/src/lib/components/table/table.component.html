<ngx-datatable
  #table
  [rows]="rows"
  [headerHeight]="showHeader ? 48 : 0"
  [rowHeight]="undefined"
  [externalSorting]="externalSorting"
  [messages]="messages"
  [columnMode]="ColumnMode.force"
  [selected]="selected"
  [rowClass]="rowClass"
  [trackByProp]="trackByProp"
  [selectionType]="SelectionType.single"
  [reorderable]="false"
  (sort)="onSort($event)"
  [sorts]="defaultSort"
  [ngClass]="[activeRowEffect]"
>
  <ngx-datatable-row-detail [rowHeight]="rowDetailHeight">
    <ng-template let-row="row" ngx-datatable-row-detail-template>
      <ng-template
        [ngTemplateOutlet]="rowDetailTemplate"
        [ngTemplateOutletContext]="{ $implicit: row }"
      ></ng-template>
    </ng-template>
  </ngx-datatable-row-detail>
  @for (column of columns; track column; let first = $first; let last = $last) {
    @if (!column.component && !column.isCustomCellTemplate) {
      <ngx-datatable-column
        [name]="column.name"
        [prop]="column.prop"
        [sortable]="column.sortable"
        [comparator]="column.comparator"
        [pipe]="column.pipe"
        [headerClass]="first ? 'first-column' : ''"
        [cellClass]="first ? 'first-column' : last ? 'last-column' : ''"
        [minWidth]="column.width"
      >
        <ngx-datatable-column>
          <ng-template let-columnProp="value" ngx-datatable-cell-template>
            {{ columnProp }}
          </ng-template>
        </ngx-datatable-column>
      </ngx-datatable-column>
    }
    @if (column.isCustomCellTemplate) {
      <ngx-datatable-column
        [cellTemplate]="
          templates[column.name] ||
          templates[column.id] ||
          templates[column.prop]
        "
        [prop]="column.prop"
        [name]="column.name"
        [sortable]="column.sortable"
        [comparator]="column.comparator"
        [cellClass]="
          (first ? 'first-column' : last ? 'last-column' : '') +
          ' ' +
          (column.inputClass ?? '')
        "
        [minWidth]="column.width"
      >
      </ngx-datatable-column>
    }
    @if (column.component) {
      <ngx-datatable-column
        [prop]="column.prop"
        [name]="column.name"
        [sortable]="column.sortable"
        [comparator]="column.comparator"
        [cellClass]="
          (first ? 'first-column' : last ? 'last-column' : '') +
          ' ' +
          (column.inputClass ?? '')
        "
        [minWidth]="column.width"
      >
        <ng-template let-value="value" ngx-datatable-cell-template>
          <ui-dynamic-displayer
            [component]="column.component"
            [input]="{
              name: column.inputName,
              value: value,
            }"
          ></ui-dynamic-displayer>
        </ng-template>
      </ngx-datatable-column>
    }
  }
</ngx-datatable>
