import { DOCUMENT } from '@angular/common';
import {
  Component,
  EventEmitter,
  Inject,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { TableRow } from '@fincloud/types/models';
import {
  ColumnMode,
  DatatableComponent,
  SelectionType,
} from '@siemens/ngx-datatable';
import { SortParams } from '../../models/sort-params';
import { TableColumn } from '../../models/table';

@Component({
  selector: 'ui-table',
  templateUrl: './table.component.html',
  styleUrls: ['./table.component.scss'],
})
export class TableComponent implements OnChanges {
  @ViewChild('table', { static: true }) table: DatatableComponent;
  @Input() defaultSort: { prop: string; dir: 'asc' | 'desc' }[] = [];
  @Input() columns: TableColumn[];
  @Input() rows: TableRow[];
  @Input() templates: { [columnNameOrId: string]: TemplateRef<unknown> };
  @Input() rowDetailTemplate: TemplateRef<unknown>;
  @Input() searchTerm: string;
  @Input() loading = true;
  @Input() rowDetailHeight: number;
  @Input() showHeader = true;
  @Input() externalSorting = false;
  @Input() trackByProp = 'name';
  @Input() rowClass: (row: TableRow) => { [ket: string]: boolean };
  @Input()
  noDataAvailableMessage =
    $localize`:@@table.noDataAvailable.message:Keine Daten vorhanden.`;
  @Input() activeRowEffect = 'border-effect';

  @Output() sort = new EventEmitter<SortParams[]>();

  selected: [TableRow] | [] = [];

  // TableComponent::selected property mutations cause timing issues for our add-on logic
  // of setting class 'active' on the wrapper when selected => cached version
  selectedCache: [TableRow] | [] = [];

  filterColumns: TableColumn[];
  tableData: TableRow[];
  ColumnMode = ColumnMode;
  SelectionType = SelectionType;

  loadingMessage = $localize`:@@userManagement.users.noData:Daten werden geladen...`;
  messages = {
    emptyMessage: '',
  };

  constructor(@Inject(DOCUMENT) private document: Document) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.columns?.firstChange) {
      this.filterColumns = this.columns?.filter(
        (column) => column.filterable && column.filterable,
      );
    }
    if (changes.rows) {
      this.tableData = this.rows;
    }
    if (changes.loading) {
      this.messages = {
        emptyMessage: this.loading
          ? this.loadingMessage
          : this.noDataAvailableMessage,
      };
    }
    if (changes.searchTerm && !changes.searchTerm.firstChange) {
      this.updateFilter(changes.searchTerm.currentValue);
    }
  }

  toggleRowDetail(row: TableRow) {
    if (this.selectedCache?.some((r) => r.id === row.id)) {
      this.deselectRow(row);
    } else {
      this.selectRow(row);
    }
  }

  onSort(ev: { sorts: SortParams[] }) {
    if (!this.externalSorting) {
      return;
    }

    this.sort.emit(ev.sorts);
  }

  selectRow(row: TableRow) {
    this.selectedCache = [row];
    this.table.rowDetail.toggleExpandRow(row);
  }

  deselectRow(row: TableRow) {
    this.table.rowDetail.toggleExpandRow(row);
    this.selectedCache = [];
  }

  updateFilter(searchTerm: string): void {
    if (!this.filterColumns) {
      return;
    }

    const filterValue = searchTerm.toLowerCase();

    this.rows = this.tableData.filter((row) =>
      this.filterColumns.some((columnProp) => {
        let rowValue: string;
        if (columnProp.prop) {
          rowValue = row[columnProp.prop].toString();
        } else if (columnProp.propExpression) {
          rowValue = columnProp.propExpression(row);
        }
        const valueMatched =
          !rowValue ||
          rowValue?.toString().toLowerCase().indexOf(filterValue) !== -1;
        const noFilterValue = !filterValue;

        return valueMatched || noFilterValue;
      }),
    );
  }
}
