import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
} from '@angular/core';
import { ButtonSize } from '@fincloud/components/buttons';
import { SortCriteria, SortField } from '@fincloud/types/models';

@Component({
  selector: 'ui-sort-criteria-dropdown',
  templateUrl: './sort-criteria-dropdown.component.html',
  styleUrls: ['./sort-criteria-dropdown.component.scss'],
})
export class SortCriteriaDropdownComponent implements OnInit, OnChanges {
  @Input() sortCriteria: SortCriteria;
  @Input() sortFields: SortField[];
  @Input() size: ButtonSize = 'extra-small';

  @Input() aligned: 'left' | 'right' = 'right';

  @Output() sortChanged = new EventEmitter<SortCriteria>();

  currenSortField: SortField;
  isAscending: boolean;

  ngOnInit() {
    this.reInitializeSort();
  }

  ngOnChanges() {
    this.reInitializeSort();
  }

  private reInitializeSort() {
    this.sortFields = this.sortFields.filter((sf: SortField) => sf.show);
    if (this.sortCriteria) {
      this.currenSortField = this.sortFields.find(
        (sf: SortField) => sf.name === this.sortCriteria.sortBy,
      );
      this.isAscending = this.sortCriteria.sortOrder === 'asc';
    }
  }

  sortByField(selectedSortField: SortField) {
    this.currenSortField = selectedSortField;
    this.isAscending = true;

    this.emitSortChange();
  }

  emitSortChange() {
    const direction = this.isAscending ? 'asc' : 'desc';

    this.sortChanged.emit({
      sortOrder: direction,
      sortBy: this.currenSortField.name,
    });
  }

  changeSortFieldOrder() {
    this.isAscending = !this.isAscending;

    this.emitSortChange();
  }
}
