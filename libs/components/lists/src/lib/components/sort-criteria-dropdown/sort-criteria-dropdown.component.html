<div class="sort-list__wrapper">
  <div class="dropdown d-flex" ngbDropdown #dropdown="ngbDropdown">
    <ui-button
      class="arrow"
      type="stealth"
      corners="pointy"
      [size]="size"
      borderColor="transparent"
      [icon]="isAscending ? 'upward-arrow' : 'downward-arrow'"
      (clicked)="changeSortFieldOrder()"
    >
    </ui-button>
    <div class="vertical-divider"></div>
    <ui-button
      class="dropdown-toggle"
      ngbDropdownToggle
      type="stealth"
      corners="pointy"
      [label]="currenSortField?.displayValue ?? 'Sortieren'"
      borderColor="transparent"
      [size]="size"
    ></ui-button>
    <ui-icon
      class="toggle-icon"
      ngbDropdownToggle
      size="large"
      color="primary"
      [name]="dropdown.isOpen() ? 'expand_less' : 'expand_more'"
    ></ui-icon>
    <div class="dropdown-menu" ngbDropdownMenu [ngClass]="[aligned]">
      @for (sortField of sortFields; track sortField) {
        <ui-dropdown-item
          i18n-[label]="@@test.id"
          [label]="sortField.displayValue"
          (click)="sortByField(sortField)"
        ></ui-dropdown-item>
      }
    </div>
  </div>
</div>
