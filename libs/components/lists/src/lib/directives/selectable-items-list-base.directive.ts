import { SelectableItemBase } from '@fincloud/core/selects';

import { Directive, Input, QueryList } from '@angular/core';

@Directive({})
export abstract class SelectableItemsListBase<T extends SelectableItemBase> {
  abstract selectableItems: QueryList<T>;

  allSelected = false;

  @Input() selectionMode: boolean;

  get selectedItemsCount() {
    return this.selectableItems.filter(
      (selectableItem) =>
        selectableItem.selectionMode && selectableItem.selected === true,
    ).length;
  }

  selectAll() {
    this.selectableItems.forEach((selectableItem) => {
      selectableItem.select();
    });
  }

  deSelectAll() {
    this.selectableItems.forEach((selectableItem) => {
      selectableItem.deSelect();
    });
  }
}
