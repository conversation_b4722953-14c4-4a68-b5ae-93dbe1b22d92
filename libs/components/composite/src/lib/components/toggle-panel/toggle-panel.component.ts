import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  Output,
  ViewChild,
} from '@angular/core';
import {
  NgbAccordionDirective,
  NgbAccordionItem,
} from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'ui-toggle-panel',
  templateUrl: './toggle-panel.component.html',
  styleUrls: ['./toggle-panel.component.scss'],
})
export class TogglePanelComponent {
  private _open: boolean;

  @Input() shouldIgnoreToggleAttempt = false;
  @Input() shouldEmitClickEventOnToggle = false;
  @Input() defaultBg = true;

  @Input()
  set open(isOpen: boolean) {
    if (this.shouldIgnoreToggleAttempt) {
      return;
    }

    this._open = isOpen;

    if (this.ngbAccordion) {
      this.togglePanel(isOpen);
    }
  }

  get open() {
    return this._open;
  }

  @Input()
  showToggleIcon = true;
  @Input() active = true;

  @ViewChild(NgbAccordionDirective) ngbAccordion: NgbAccordionDirective;

  @ViewChild(NgbAccordionItem)
  set panel(value: NgbAccordionItem) {
    this._panel = value;

    this.togglePanel(this.open);

    this.cd.detectChanges();
  }

  get panel(): NgbAccordionItem {
    return this._panel;
  }

  private _panel: NgbAccordionItem;

  @Input()
  type: 'regular' | 'highlighted' | 'light' = 'regular';

  @Input()
  expandedStyle: 'shadow' | 'transparent' | 'default' | 'dark' | 'dark-darker' =
    'default';

  @Input()
  hasShadow = false;

  @Input()
  contentScrollbarHeight: string;

  @Output()
  panelOpen = new EventEmitter();

  @Output()
  panelClose = new EventEmitter();

  constructor(private cd: ChangeDetectorRef) {}

  onPanelHeaderClicked(event: MouseEvent) {
    this.open = !this.open;

    if (!this.shouldEmitClickEventOnToggle) {
      event.stopPropagation();
    }
  }

  public togglePanel(open: boolean) {
    if (!this.active) {
      return;
    }

    if (open) {
      this.ngbAccordion.expand(this._panel.id);
      this.panelOpen.emit();
    } else {
      this.ngbAccordion.collapse(this._panel.id);
      this.panelClose.emit();
    }
  }
}
