<div
  class="carousel-container"
  (mouseenter)="showToggleArea()"
  (mouseleave)="hideToggleArea()"
  [class.flipped]="flipped"
>
  <div class="front">
    <ng-content select="[front]"></ng-content>
    @if (flipped || flipping) {
      <div class="back" [class.hide]="flipping">
        <ng-content select="[back]"></ng-content>
      </div>
    }
  </div>

  @if (showFlipToggle && hasBack) {
    <div
      class="flip-toggle"
      @toggleArea
      [class.disabled]="disabled"
      [class.flipped]="flipped"
      (click)="flip()"
    >
      <ui-icon name="arrow-down" color="primary" size="small"></ui-icon>
    </div>
  }
</div>
