export interface SearchResultAddress {
  /**
   * Building Number property
   * **NOTE: This property will not be serialized. It can only be populated by the server.**
   */
  readonly buildingNumber?: string;
  /**
   * Street property
   * **NOTE: This property will not be serialized. It can only be populated by the server.**
   */
  readonly street?: string;
  /**
   * Cross Street property
   * **NOTE: This property will not be serialized. It can only be populated by the server.**
   */
  readonly crossStreet?: string;
  /**
   * Street Number property
   * **NOTE: This property will not be serialized. It can only be populated by the server.**
   */
  readonly streetNumber?: string;
  /**
   * number of routes
   * **NOTE: This property will not be serialized. It can only be populated by the server.**
   */
  readonly routeNumbers?: number[];
  /**
   * Street Name property
   * **NOTE: This property will not be serialized. It can only be populated by the server.**
   */
  readonly streetName?: string;
  /**
   * Street Name and Number property
   * **NOTE: This property will not be serialized. It can only be populated by the server.**
   */
  readonly streetNameAndNumber?: string;
  /**
   * Municipality property
   * **NOTE: This property will not be serialized. It can only be populated by the server.**
   */
  readonly municipality?: string;
  /**
   * Municipality Subdivision property
   * **NOTE: This property will not be serialized. It can only be populated by the server.**
   */
  readonly municipalitySubdivision?: string;
  /**
   * Country Tertiary Subdivision property
   * **NOTE: This property will not be serialized. It can only be populated by the server.**
   */
  readonly countryTertiarySubdivision?: string;
  /**
   * Country Secondary Subdivision property
   * **NOTE: This property will not be serialized. It can only be populated by the server.**
   */
  readonly countrySecondarySubdivision?: string;
  /**
   * Country Subdivision property
   * **NOTE: This property will not be serialized. It can only be populated by the server.**
   */
  readonly countrySubdivision?: string;
  /**
   * Postal Code property
   * **NOTE: This property will not be serialized. It can only be populated by the server.**
   */
  readonly postalCode?: string;
  /**
   * Extended Postal Code property
   * **NOTE: This property will not be serialized. It can only be populated by the server.**
   */
  readonly extendedPostalCode?: string;
  /**
   * Country Code property
   * **NOTE: This property will not be serialized. It can only be populated by the server.**
   */
  readonly countryCode?: string;
  /**
   * Country property
   * **NOTE: This property will not be serialized. It can only be populated by the server.**
   */
  readonly country?: string;
  /**
   * Country Code ISO3 property
   * **NOTE: This property will not be serialized. It can only be populated by the server.**
   */
  readonly countryCodeISO3?: string;
  /**
   * Free form Address property
   * **NOTE: This property will not be serialized. It can only be populated by the server.**
   */
  readonly freeformAddress?: string;
  /**
   * Country Subdividion Name property
   * **NOTE: This property will not be serialized. It can only be populated by the server.**
   */
  readonly countrySubdivisionName?: string;
  /**
   * An address component which represents the name of a geographic area or locality that groups a
   * number of addressable objects for addressing purposes, without being an administrative unit.
   * This field is used to build the `freeformAddress` property.
   * **NOTE: This property will not be serialized. It can only be populated by the server.**
   */
  readonly localName?: string;

  readonly position?: {
    lat?: number;
    lon?: number;
  };
}
