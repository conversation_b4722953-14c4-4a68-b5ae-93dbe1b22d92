<div class="expanded-checkbox-list-container">
  @if (!disabled) {
    <div class="select-header">
      <div class="all-selector">
        <ui-checkbox
          size="medium"
          type="dark"
          label="Alle auswählen"
          i18n-label="@@expandedSelect.list.toggleAll"
          (change)="toggleAllCheckboxes()"
          [(ngModel)]="allChecked"
          [indeterminate]="someComplete()"
          [isDisabled]="disabled"
        >
        </ui-checkbox>
      </div>
      @if (hasSearch) {
        <ui-text-input
          size="medium"
          [placeholder]="typeToSearchText"
          (inputChange)="search($event)"
        ></ui-text-input>
      }
    </div>
  }
  <div class="select-body">
    <ng-template [ngTemplateOutlet]="headerTemplate"></ng-template>

    @if (hasScrollbar && options?.length) {
      <ng-scrollbar>
        <ng-container *ngTemplateOutlet="tempOutlet"></ng-container>
      </ng-scrollbar>
    }

    @if (!hasScrollbar) {
      <ng-container *ngTemplateOutlet="tempOutlet"></ng-container>
    }

    @if (showNotFoundText) {
      <div class="no-results">{{ notFoundText }}</div>
    }
  </div>
</div>

<ng-template #tempOutlet>
  @if (options?.length) {
    <form
      [ngStyle]="{ height: scrollbarHeightRem + 'rem' }"
      [formGroup]="checkboxFormGroup"
      [ngClass]="{ 'without-scrollbar': !hasScrollbar }"
    >
      @for (option of options; track option) {
        <div class="option-row">
          @if (!disabled) {
            <ui-checkbox
              (change)="checkboxValueChanged()"
              [formControlName]="option.label"
              size="medium"
              type="dark"
              [isDisabled]="option?.disabled"
            >
            </ui-checkbox>
          }
          <ng-template
            [ngTemplateOutlet]="optionTemplate"
            [ngTemplateOutletContext]="{ item: option }"
          ></ng-template>
        </div>
      }
    </form>
  }
</ng-template>
