<div #dropdown="ngbDropdown" class="select-input-wrapper" ngbDropdown>
  <ui-search-filter
    #searchFilterComponent
    (click)="expandDropdown()"
    (keyup.enter)="selectFirstOption()"
    (search)="filterOptions($event)"
    [size]="size"
    [placeholder]="placeholder"
    ngbDropdownToggle
  ></ui-search-filter>

  <div class="option-items" ngbDropdownMenu>
    @for (
      option of optionsToShow | slice: 0 : maxDropdownItemsToShow;
      track option
    ) {
      <button
        class="dropdown-item"
        (click)="selectOption(option)"
        ngbDropdownItem
      >
        <span [innerHTML]="option.name | boldSubstring: searchTerm"></span>
      </button>
    }
  </div>
</div>
