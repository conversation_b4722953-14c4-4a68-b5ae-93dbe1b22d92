import {
  AbstractValueAccessor,
  makeControlValidatorProvider,
  makeControlValueAccessorProvider,
} from '@fincloud/core/form';

import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { SearchFilterComponent } from '@fincloud/components/search-filter';
import { SelectOptionItem } from '@fincloud/types/models';
import { NgbDropdown } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'ui-single-select-input',
  templateUrl: './single-select-input.component.html',
  styleUrls: ['./single-select-input.component.scss'],
  providers: [
    makeControlValueAccessorProvider(SingleSelectInputComponent),
    makeControlValidatorProvider(SingleSelectInputComponent),
  ],
})
export class SingleSelectInputComponent<T>
  extends AbstractValueAccessor<T>
  implements OnChanges
{
  @ViewChild('searchFilterComponent', { static: true })
  searchFilter: SearchFilterComponent;
  @ViewChild('dropdown', { static: true }) dropdown: NgbDropdown;

  @Input()
  options: SelectOptionItem<T>[];

  @Input()
  searchTerm = '';

  @Input()
  placeholder = '';

  @Input()
  maxDropdownItemsToShow = 4;

  @Input()
  isFilteringExternal: boolean;

  @Input()
  shouldPreserveSelectedOption: boolean;

  @Input()
  size: 'small' | 'large' = 'small';

  @Output()
  optionSelect: EventEmitter<SelectOptionItem<T>> = new EventEmitter<
    SelectOptionItem<T>
  >();

  @Output()
  filterExternal: EventEmitter<string> = new EventEmitter<string>();

  constructor(changeDetectorRef: ChangeDetectorRef) {
    super(changeDetectorRef);
  }

  optionsToShow: SelectOptionItem<T>[];

  expandDropdown() {
    !this.dropdown.isOpen() && this.dropdown.open();
  }

  selectFirstOption() {
    if (this.searchTerm && this.optionsToShow.length) {
      this.selectOption(this.optionsToShow[0]);
    }
  }

  selectOption(option: SelectOptionItem<T>) {
    this.searchFilter.setSearchPhrase('');
    this.dropdown.close();
    this.value = option.value;

    if (this.shouldPreserveSelectedOption) {
      this.optionsToShow = [];
      this.searchFilter.setSearchPhrase(option.name);
    }

    this.optionSelect.emit(option);
  }

  filterOptions(searchTerm: string) {
    if (this.isFilteringExternal) {
      return this.filterExternal.emit(searchTerm);
    }

    this.searchTerm = searchTerm;
    searchTerm && this.expandDropdown();
    this.optionsToShow = this.options.filter((option) =>
      option.name.toLowerCase().includes(searchTerm),
    );
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.options) {
      this.optionsToShow = this.options;
    }
  }
}
