<ul
  ngbNav
  #nav="ngbNav"
  [(activeId)]="value"
  class="nav-pills nav-fill"
  (navChange)="onPillChange($event)"
>
  @for (item of items; track item) {
    <li
      class="item"
      [class.active]="item?.key === value?.key"
      [ngbNavItem]="item"
    >
      <ui-tooltip
        [disabled]="!item.tooltipText"
        [text]="item.tooltipText || ''"
      >
        <a ngbNavLink class="link">{{ item.value }}</a>
      </ui-tooltip>
    </li>
  }
</ul>
