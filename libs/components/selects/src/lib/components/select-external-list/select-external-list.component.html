<div
  class="selected-items-panel"
  [ngClass]="{ 'selected-items-container': !hasScrollbar }"
>
  @if (panelHeaderTitle) {
    <div class="header">{{ panelHeaderTitle }}</div>
    <ui-horizontal-divider
      class="divider"
      color="gray"
      borderSize="small"
    ></ui-horizontal-divider>
  }
  @if (!itemsSelectedList?.length && emptyPanelText) {
    <div class="empty-list">
      {{ emptyPanelText }}
    </div>
  }
  @if (itemsSelectedList?.length) {
    <div class="selected-list d-flex flex-column">
      @if (hasScrollbar) {
        <ng-scrollbar>
          <ng-template [ngTemplateOutlet]="content"></ng-template>
        </ng-scrollbar>
      } @else {
        <ng-template [ngTemplateOutlet]="content"></ng-template>
      }
    </div>
  }
</div>

<ng-template #content>
  @for (item of itemsSelectedList; track item) {
    <div class="deselectable-row">
      <ng-template
        [ngTemplateOutlet]="itemTemplate"
        [ngTemplateOutletContext]="{ item: item }"
      ></ng-template>
      <div>
        @if (editMode) {
          <ui-icon
            title="Teilen beenden"
            i18n-title="@@selectExternal.title"
            class="deselect"
            name="close"
            color="subtle"
            (click)="deselectItem(item)"
            size="medium"
          ></ui-icon>
        }
      </div>
    </div>
  }
</ng-template>
