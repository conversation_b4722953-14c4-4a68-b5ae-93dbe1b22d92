import {
  Component,
  DestroyRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  TemplateRef,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { distinctUntilChanged } from 'rxjs/operators';
import { SelectComponent } from '../select/select.component';

@Component({
  selector: 'ui-select-external-list',
  templateUrl: './select-external-list.component.html',
  styleUrls: ['./select-external-list.component.scss'],
})
export class SelectExternalListComponent implements OnInit {
  @Input() selectComponent: SelectComponent;
  @Input() itemTemplate: TemplateRef<unknown>;
  @Input() panelHeaderTitle: string;
  @Input() emptyPanelText: string;
  @Input() itemsSelectedList: unknown[];
  @Input() editMode = true;
  @Input() clearOnSelection = true;
  @Input() hasScrollbar = true;

  @Output() itemDeselected = new EventEmitter();

  ngOnInit() {
    if (this.clearOnSelection) {
      this.selectComponent.selectionChange
        .pipe(distinctUntilChanged(), takeUntilDestroyed(this.destroyRef))
        .subscribe(() => {
          this.selectComponent.clearSelect();
        });
    }
  }

  deselectItem(item: unknown) {
    this.itemDeselected.emit(item);
  }

  constructor(private destroyRef: DestroyRef) {}
}
