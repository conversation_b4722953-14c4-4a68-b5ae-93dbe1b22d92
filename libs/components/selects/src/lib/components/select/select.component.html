<ng-select
  [(ngModel)]="value"
  #select
  [dropdownPosition]="dropdownPosition"
  [compareWith]="compareWith"
  class="ui-select"
  [ngClass]="[size, background, borderRadius]"
  [class.hideBorder]="hideBorder"
  [class.error]="(hasError && hasInstantFeedback) || forceHasError"
  [class.multiple]="multiple"
  [virtualScroll]="virtualScroll"
  [placeholder]="placeholder"
  [typeToSearchText]="typeToSearchText"
  [notFoundText]="notFoundText"
  [items]="options"
  [bindLabel]="labelKey"
  [bindValue]="valueKey"
  [typeahead]="typeAhead"
  [groupBy]="groupBy"
  [disabled]="isDisabled"
  [class.disabled]="isDisabled"
  [class.hide-arrow]="hideArrow"
  [addTag]="addTag"
  [addTagText]="addTagText"
  [multiple]="multiple"
  [searchable]="searchable"
  [appendTo]="appendTo"
  [editableSearchTerm]="editableSearchTerm"
  [searchFn]="searchFn"
  [clearable]="isClearable"
  [closeOnSelect]="closeOnSelect"
  [loading]="loading"
  [loadingText]="loadingText"
  (blur)="onBlur()"
  (search)="onSearchTextChange($event)"
  (focus)="onFocus()"
  (ngModelChange)="onModelChange($event)"
  clearAllText=""
>
  @if (maxPlaceholderItems) {
    <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
      @for (item of items | slice: 0 : maxPlaceholderItems; track item) {
        <div class="ng-value">
          @if (truncateItem) {
            <ui-truncated-text
              class="ng-value-label"
              [widthCalculationExpression]="
                'calc(' +
                maxWithTruncated +
                ' - ' +
                (items.length > maxPlaceholderItems ? '5rem' : '0px') +
                ')'
              "
              >{{ $any(item)[labelKey] }}</ui-truncated-text
            >
          } @else {
            <span class="ng-value-label"> {{ $any(item)[labelKey] }}</span>
          }

          <span
            class="ng-value-icon right"
            (click)="clear(item)"
            aria-hidden="true"
            >×</span
          >
        </div>
      }
      @if (items.length > maxPlaceholderItems) {
        <div class="ng-value">
          <div class="ng-value-label more-options-counter d-flex">
            <span>+</span> <span>{{ items.length - maxPlaceholderItems }}</span>
          </div>
        </div>
      }
    </ng-template>
  }

  @if (customSelectedItemTemplate) {
    <ng-template ng-label-tmp let-item="item">
      <ng-template
        [ngTemplateOutlet]="customSelectedItemTemplate"
        [ngTemplateOutletContext]="{ item }"
      >
      </ng-template>
    </ng-template>
  }

  @if (useCustomOptionTemplate) {
    <ng-template ng-option-tmp let-item="item$">
      <div class="option-container d-flex justify-content-start">
        <div class="d-flex" style="width: 2.5rem">
          @if (item.selected) {
            <ui-icon
              class="me-3"
              name="done"
              size="medium"
              [color]="'primary'"
            ></ui-icon>
          }
        </div>
        <span class="ng-option-label">{{ item.label }}</span>
        @if (customTmplDelete) {
          <ui-icon
            style="margin-left: auto"
            name="delete"
            size="medium"
            (click)="deleteItem($event, item)"
          ></ui-icon>
        }
      </div>
    </ng-template>
  }

  @if (customLabelTemplate) {
    <ng-template ng-label-tmp let-item="item">
      <ng-template
        [ngTemplateOutlet]="customLabelTemplate"
        [ngTemplateOutletContext]="{ item }"
      >
      </ng-template>
    </ng-template>
  }

  @if (customOptionItemTemplate) {
    <ng-template ng-option-tmp let-item="item$">
      <ng-template
        [ngTemplateOutlet]="customOptionItemTemplate"
        [ngTemplateOutletContext]="{ item }"
      >
      </ng-template>
    </ng-template>
  }

  @if (customTagTemplate; as searchTerm) {
    <ng-template ng-tag-tmp let-searchTerm="searchTerm">
      <ng-template
        [ngTemplateOutlet]="customTagTemplate"
        [ngTemplateOutletContext]="{ searchTerm }"
      >
      </ng-template>
    </ng-template>
  }

  @if (customFooterTemplate && isCustomFooterVisible) {
    <ng-template ng-footer-tmp>
      <ng-template [ngTemplateOutlet]="customFooterTemplate"></ng-template>
    </ng-template>
  }

  @if (loading) {
    <ng-template ng-loadingspinner-tmp>
      <div class="dots-loader-container">
        <ui-dots-loader size="large"></ui-dots-loader>
      </div>
    </ng-template>
  }

  <ng-content></ng-content>
</ng-select>
