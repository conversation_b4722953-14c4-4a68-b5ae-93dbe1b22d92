<!-- The check is on the left side. If needed, component can be made configurable to display the check on right side for example -->

<div class="wrapper" [class.pointer]="selectionMode === true">
  @if (selectionMode === true) {
    <div class="check-area" [class.outside]="checkBoxOutsideContent">
      <div class="check" [class.selected]="selected">
        @if (selected) {
          <ui-icon
            class="icon"
            size="medium"
            name="done"
            color="white"
          ></ui-icon>
        }
      </div>
    </div>
  }

  <div class="content">
    <ng-content></ng-content>
  </div>
</div>
