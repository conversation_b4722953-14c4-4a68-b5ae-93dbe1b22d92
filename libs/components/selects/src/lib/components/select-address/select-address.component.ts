import {
  AbstractValueAccessor,
  makeControlValidatorProvider,
  makeControlValueAccessorProvider,
} from '@fincloud/core/form';
import { AddressHelperService } from '@fincloud/core/services';

import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { AzureMapSearchService } from '@fincloud/components/azure-map';
import { BusinessCaseLocation } from '@fincloud/core/formly';
import { AddressType } from '@fincloud/core/location';
import { Address } from '@fincloud/swagger-generator/authorization-server';
import { Address as CompanyAddress } from '@fincloud/swagger-generator/company';
import { FinSize } from '@fincloud/ui/types';
import { isObject, isString } from 'lodash-es';
import {
  BehaviorSubject,
  Observable,
  Subject,
  catchError,
  debounceTime,
  filter,
  map,
  merge,
  of,
  switchMap,
} from 'rxjs';
import { SearchResultAddress } from '../../models/search-result-address';
import { AzureHelperService } from '../../service/azure-helper.service';

@Component({
  selector: 'ui-select-address',
  templateUrl: './select-address.component.html',
  styleUrls: ['./select-address.component.scss'],
  providers: [
    makeControlValueAccessorProvider(SelectAddressComponent),
    makeControlValidatorProvider(SelectAddressComponent),
    AzureMapSearchService,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SelectAddressComponent
  extends AbstractValueAccessor<Address | CompanyAddress | BusinessCaseLocation>
  implements OnInit
{
  @Input() addressType: AddressType = 'customer';
  @Input() placeholder: string =
    $localize`:@@select.typeToSearchText:Text eingeben zum Suchen`;
  @Input() address: Address | CompanyAddress | BusinessCaseLocation;
  @Input() streetSuggestionsOnly = false;
  @Input() forceHasError: boolean;
  @Input() label: string;
  @Input() readonly: boolean;
  @Input() size: FinSize.L | FinSize.M = FinSize.L;
  @Input() dynamicErrorSpace: 'fixed' | 'dynamic' = 'fixed';
  @Input() isFieldRequestHighlight: boolean;
  @Input() highlightLabel: boolean;

  @Output() addressFocus = new EventEmitter(false);
  @Output() addressBlur = new EventEmitter();
  @Output() addressSelected = new EventEmitter<
    Address | CompanyAddress | BusinessCaseLocation
  >();

  selectedOption: Address | CompanyAddress | BusinessCaseLocation;
  searchInput$ = new Subject<string>();
  currentAddress: Record<string, unknown>;
  addressOptions$: Observable<Record<string, unknown>[]> = of([]);

  streetResultTypes = [
    'Point Address',
    'Address Range',
    'Street',
    'POI', // point of interest
  ];

  private manualUpdateAddressOptions$$ = new BehaviorSubject([]);

  constructor(
    private azureHelperService: AzureHelperService,
    private azureMapSearchService: AzureMapSearchService,
    private addressHelper: AddressHelperService,
    changeDetectorRef: ChangeDetectorRef,
  ) {
    super(changeDetectorRef);
  }

  ngOnInit() {
    const search$ = this.searchInput$.pipe(
      debounceTime(200),
      filter((searchTerm) => searchTerm && isString(searchTerm)),
      switchMap((searchTerm: string) => {
        return this.azureMapSearchService
          .searchAddress(searchTerm, {
            limit: this.streetSuggestionsOnly ? 30 : 10,
          })
          .pipe(
            catchError(() => of(null)),
            map((result) => result?.results || []),
            map((addressesFound) =>
              addressesFound
                .filter(
                  (a) =>
                    !this.streetSuggestionsOnly ||
                    this.streetResultTypes.includes(a.type),
                )
                .map((address) => {
                  return this.getValueAsTypeAddress({
                    ...address.address,
                    position: address.position,
                  });
                }),
            ),
          );
      }),
    );

    this.addressOptions$ = merge(
      this.manualUpdateAddressOptions$$,
      search$,
    ).pipe(
      map((addresses) =>
        addresses.map((addressDto) => ({
          addressAsString: this.getAddressString(addressDto),
          addressDto,
        })),
      ),
    );
  }

  writeValue(value: unknown) {
    super.writeValue(value);
    this.setAddressOptions(this.value ? [this.value] : []);
  }

  onSelectionChanged(place: Address | CompanyAddress | BusinessCaseLocation) {
    if (!place) {
      this.setAddressOptions([]);
      // Clicking X on autocomplete should save the value (blur is not triggered)
      this.addressBlur.emit();
    }
    this.selectedOption = place;
    this.value = place;
    this.addressSelected.emit(this.value);
  }

  onBlur() {
    const isSelectionChosen = isObject(this.formControl.value);

    if (!isSelectionChosen && this.formControl.value) {
      return;
    }

    if (!this.formControl.value) {
      this.setAddressOptions([]);
    }

    this.addressBlur.emit();
  }

  private setAddressOptions(options: unknown[]) {
    this.manualUpdateAddressOptions$$.next(options);
  }

  private getValueAsTypeAddress(place: SearchResultAddress) {
    switch (this.addressType) {
      case 'customer':
        return this.azureHelperService.placeToCustomerAddress(place);
      case 'company':
        return this.azureHelperService.placeToCompanyAddress(place);
      case 'businessCaseLocation':
        return this.azureHelperService.placeToFormlyLocation(place);
    }
  }

  private getAddressString(address: Address | BusinessCaseLocation): string {
    switch (this.addressType) {
      case 'customer':
        return this.addressHelper.asCustomerAddressString(address as Address);
      case 'businessCaseLocation':
        return this.addressHelper.asFormlyLocationString(
          address as BusinessCaseLocation,
        );
      case 'company':
        return this.addressHelper.asCompanyAddressString(address as Address);
    }
  }
}
