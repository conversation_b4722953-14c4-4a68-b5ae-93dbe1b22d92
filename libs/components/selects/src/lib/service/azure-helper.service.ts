import { Injectable } from '@angular/core';
import { BusinessCaseLocation } from '@fincloud/core/formly';
import { Address } from '@fincloud/swagger-generator/authorization-server';
import { Address as CompanyAddress } from '@fincloud/swagger-generator/company';
import { SearchResultAddress } from '../models/search-result-address';

@Injectable({
  providedIn: 'root',
})
export class AzureHelperService {
  placeToCustomerAddress(place: SearchResultAddress) {
    if (!place) {
      return null;
    }

    return {
      streetNameAndNumber: [place.streetName, place.streetNumber]
        .filter(Boolean)
        .join(' '),
      cityName: place.localName || '',
      postalCode: place.postalCode || '',
    } as Address;
  }

  placeToCompanyAddress(place: SearchResultAddress) {
    if (!place) {
      return null;
    }

    return {
      coordinates: {
        lat: place.position.lat,
        lng: place.position.lon,
      },
      street: `${place.streetName || ''} ${place.streetNumber || ''}`,
      city: place.localName || place.municipality || '',
      postalCode: place.postalCode || '',
      country: place.country || '',
      state: place.countrySubdivision || '',
    } as CompanyAddress;
  }

  placeToFormlyLocation(place: SearchResultAddress) {
    if (!place) {
      return null;
    }

    return {
      coordinates: {
        lat: place.position.lat,
        lng: place.position.lon,
      },
      address: {
        street: `${place.streetName || ''} ${place.streetNumber || ''}`,
        zip: place.postalCode || '',
        city: place.localName || place.municipality || '',
        state: place.countrySubdivision || '',
        country: place.country || '',
      },
    } as BusinessCaseLocation;
  }
}
