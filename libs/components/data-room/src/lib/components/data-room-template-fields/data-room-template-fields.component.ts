import { Component, OnInit } from '@angular/core';
import { DraggableTemplateField } from '@fincloud/core/business-case';
import { FieldType } from '@fincloud/types/enums';
import { Observable, of } from 'rxjs';
import { DRAGGABLE_TEMPLATE_FIELDS } from '../../utils/draggable-fields';

@Component({
  selector: 'app-data-room-template-fields',
  templateUrl: './data-room-template-fields.component.html',
  styleUrls: ['./data-room-template-fields.component.scss'],
})
export class DataRoomTemplateFieldsComponent implements OnInit {
  draggableFields: Observable<DraggableTemplateField[]>;

  ngOnInit(): void {
    this.getDraggableFields();
  }

  private getDraggableFields() {
    this.draggableFields = of(
      DRAGGABLE_TEMPLATE_FIELDS.filter(
        (draggableTemplateField) =>
          draggableTemplateField.fieldType !== FieldType.FOLDER,
      ),
    );
  }
}
