import { PERCENTAGE_MASK_CONFIG } from '@fincloud/core/utils';

import { ChangeContext, Options } from '@angular-slider/ngx-slider';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { merge } from 'lodash-es';

@Component({
  selector: 'ui-range-slider',
  templateUrl: './range-slider.component.html',
  styleUrls: ['./range-slider.component.scss'],
})
export class RangeSliderComponent implements OnInit {
  @Input()
  options: Options;

  @Output()
  changed = new EventEmitter();

  @Input()
  rangeValue = {
    min: 0,
    max: 100,
  };

  defaultOptions: Options = {
    animate: true,
    ceil: 100,
    floor: 0,
    step: 0.01,
    hideLimitLabels: true,
    hidePointerLabels: true,
  };

  percentageMaskConfig = PERCENTAGE_MASK_CONFIG[this.regionalSettings.locale];

  constructor(private regionalSettings: RegionalSettingsService) {}

  ngOnInit(): void {
    this.options = merge(this.defaultOptions, this.options);
  }

  onChange(event: ChangeContext) {
    this.changed.emit(event.value);
  }
}
