import { ChangeContext, Options } from '@angular-slider/ngx-slider';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { merge } from 'lodash-es';

@Component({
  selector: 'ui-zoom-slider',
  templateUrl: './zoom-slider.component.html',
  styleUrls: ['./zoom-slider.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ZoomSliderComponent implements OnInit {
  @Input()
  options: Options;

  @Input()
  value = 1;

  @Output()
  changed = new EventEmitter<number>();

  manualRefresh: EventEmitter<void> = new EventEmitter<void>();

  defaultOptions: Options = {
    floor: 0.1,
    ceil: 2,
    step: 0.1,
    vertical: true,
    showTicks: false,
  };

  sliderOptions: Options;

  ngOnInit(): void {
    this.sliderOptions = merge(this.defaultOptions, this.options);
  }

  onChange(event: ChangeContext): void {
    this.changed.emit(event.value);
    this.value = event.value;
  }

  onZoomIn(): void {
    if (this.value + this.sliderOptions.step <= this.sliderOptions.ceil) {
      this.value += this.sliderOptions.step;
    }
    this.changed.emit(this.value);
  }

  onZoomOut(): void {
    if (this.value - this.sliderOptions.step >= this.sliderOptions.floor) {
      this.value -= this.sliderOptions.step;
    }
    this.changed.emit(this.value);
  }
}
