<div
  class="zoom-slider-container tw-shadow-[0_0.1rem_0.8rem] tw-shadow-color-transparency-dark-subtle"
>
  <button
    type="button"
    class="btn-clean tw-cursor-pointer"
    (click)="onZoomIn()"
  >
    <fin-icon
      class="tw-text-buttons-main-set-informative-color-icon-default"
      name="add"
    ></fin-icon>
  </button>
  <ngx-slider
    [options]="sliderOptions"
    class="zoom-slider"
    [value]="value"
    [manualRefresh]="manualRefresh"
    (userChange)="onChange($event)"
  >
  </ngx-slider>
  <button
    type="button"
    class="btn-clean tw-cursor-pointer"
    (click)="onZoomOut()"
  >
    <fin-icon
      class="tw-text-buttons-main-set-informative-color-icon-default"
      name="remove"
    ></fin-icon>
  </button>
</div>
