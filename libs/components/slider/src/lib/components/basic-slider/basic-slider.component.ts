import { Options } from '@angular-slider/ngx-slider';
import {
  Component,
  DestroyRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Subject, debounceTime } from 'rxjs';
import { SliderDirection } from '../../enums/slider-direction';

@Component({
  selector: 'ui-basic-slider',
  templateUrl: './basic-slider.component.html',
  styleUrls: ['./basic-slider.component.scss'],
})
export class BasicSliderComponent implements OnInit {
  @Input() floor: number;
  @Input() ceil: number;
  @Input() value: number;
  @Input() prefix: string;
  @Input() suffix: string;

  @Output() valueChanged = new EventEmitter<number>();

  _sliderChangeSubject = new Subject<number>();
  sliderChange$ = this._sliderChangeSubject.asObservable();
  direction: SliderDirection = SliderDirection.UP;
  options: Options = {
    showSelectionBar: true,
  };

  private initialValue: number;
  ngOnInit(): void {
    this.sliderChange$
      .pipe(debounceTime(10), takeUntilDestroyed(this.destroyRef))
      .subscribe((slideChange: number) => {
        this.valueChanged.emit(slideChange);
      });
    this.options.floor = this.floor;
    this.options.ceil = this.ceil;
    this.initialValue = this.value;
    if (this.prefix) {
      this.options.translate = (value) => this.prefix + value;
    }
    if (this.suffix) {
      this.options.translate = (value) => value + this.suffix;
    }
  }

  valueChange(valueChange: number) {
    this._sliderChangeSubject.next(valueChange);
    this.direction =
      this.initialValue < valueChange
        ? SliderDirection.UP
        : SliderDirection.DOWN;
    this.initialValue = valueChange;
  }

  constructor(private destroyRef: DestroyRef) {}
}
