import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NsUiIconsModule } from '@fincloud/components/icons';
import { NsUiNumberModule } from '@fincloud/components/number';
import { FinIconModule } from '@fincloud/ui/icon';
import { NgxCurrencyDirective } from 'ngx-currency';
import { BasicSliderComponent } from './components/basic-slider/basic-slider.component';
import { RangeSliderComponent } from './components/range-slider/range-slider.component';
import { ZoomSliderComponent } from './components/zoom-slider/zoom-slider.component';

@NgModule({
  imports: [
    CommonModule,
    NgxSliderModule,
    NgxCurrencyDirective,
    NsUiIconsModule,
    NsUiNumberModule,
    FormsModule,
    FinIconModule,
  ],
  declarations: [
    BasicSliderComponent,
    RangeSliderComponent,
    ZoomSliderComponent,
  ],
  exports: [BasicSliderComponent, RangeSliderComponent, ZoomSliderComponent],
})
export class NsUiSliderModule {}
