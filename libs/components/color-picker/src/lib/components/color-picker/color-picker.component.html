@for (colorArr of colors; track colorArr) {
  <div class="color-panel">
    @for (color of colorArr; track color) {
      <div
        (click)="setColor(color)"
        class="color"
        [style.background]="color?.value"
        [style.border-color]="color?.borderColor"
      >
        @if (color?.value === value) {
          <ui-icon
            name="check"
            [color]="color?.checkmarkIconColor || 'white'"
            size="small"
          >
          </ui-icon>
        }
      </div>
    }
  </div>
}
