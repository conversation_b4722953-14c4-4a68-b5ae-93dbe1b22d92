import { transition, trigger, useAnimation } from '@angular/animations';
import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnInit,
} from '@angular/core';
import { SLIDE_IN_DOWN } from '@fincloud/components/animations';
import { AlertType } from '../../enums/alert-type';
import { Alert } from '../../models/alert';

@Component({
  selector: 'ui-alert-panel',
  templateUrl: './alert-panel.component.html',
  styleUrls: ['./alert-panel.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    trigger('slide', [transition(':enter', useAnimation(SLIDE_IN_DOWN))]),
  ],
})
export class AlertPanelComponent implements OnInit {
  @Input()
  alert: Alert;

  ngOnInit(): void {
    if (this.alert.type !== AlertType.SUCCESS) {
      throw new Error(
        `Component ${AlertPanelComponent.name} does not support alert type: ${this.alert.type}`,
      );
    }
  }
}
