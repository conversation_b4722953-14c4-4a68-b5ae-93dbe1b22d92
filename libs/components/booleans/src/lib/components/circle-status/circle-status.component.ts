import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnChanges,
} from '@angular/core';
import { merge } from 'lodash-es';
import { CircleStatusConfig } from '../../models/circle-status-config';

@Component({
  selector: 'ui-circle-status',
  templateUrl: './circle-status.component.html',
  styleUrls: ['./circle-status.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CircleStatusComponent implements OnChanges {
  @Input() status: boolean;
  @Input() statusConfig: CircleStatusConfig;

  effectiveStatusConfig: CircleStatusConfig;

  private defaultStatusConfig: CircleStatusConfig = {
    size: 'large',
  } as CircleStatusConfig;

  tooltipText = {
    active: $localize`:@@accountManagement.users.active:Activ`,
    inactive: $localize`:@@graph.legend.label3:Inaktiv`,
  };

  ngOnChanges() {
    this.effectiveStatusConfig = merge(
      this.defaultStatusConfig,
      this.statusConfig,
    );
  }
}
