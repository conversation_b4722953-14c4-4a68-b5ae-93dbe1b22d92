<label
  class="cl-switch"
  [class.cl-disabled]="disabled"
  (click)="onClick($event)"
  [ngClass]="[labelPosition, size]"
>
  @if (labelPosition === 'left' && !!label) {
    <ng-container *ngTemplateOutlet="spanLabel"></ng-container>
  }

  <input type="checkbox" [(ngModel)]="value" [disabled]="disabled" />
  <span class="switcher"></span>

  @if (labelPosition === 'right' && !!label) {
    <ng-container *ngTemplateOutlet="spanLabel"></ng-container>
  }
</label>
<ng-template #spanLabel>
  @if (label) {
    <span class="label label-span" [ngClass]="[labelColor, labelSize]">{{
      label
    }}</span>
  }
</ng-template>
