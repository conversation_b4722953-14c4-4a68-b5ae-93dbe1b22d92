import {
  AbstractValueAccessor,
  makeControlValidatorProvider,
  makeControlValueAccessorProvider,
} from '@fincloud/core/form';

import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';

@Component({
  selector: 'ui-checkbox',
  templateUrl: './checkbox.component.html',
  styleUrls: ['./checkbox.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    makeControlValueAccessorProvider(CheckboxComponent),
    makeControlValidatorProvider(CheckboxComponent),
  ],
})
export class CheckboxComponent
  extends AbstractValueAccessor<boolean>
  implements OnInit, AfterViewInit
{
  // Increasing integer for generating unique ids for checkbox components.
  static uniqueId = 1;

  @Input()
  label = '';

  @Input()
  type: 'regular' | 'dark' = 'regular';

  @Input()
  size: 'small' | 'medium' | 'large' = 'medium';

  @Input()
  isDisabled = false;

  @Input()
  unselectValueOnCheck = false;

  /**
   * Whether the checkbox is indeterminate. This is also known as "mixed" mode and can be used to
   * represent a checkbox with three states, e.g. a checkbox that represents a nested list of
   * checkable items. Note that whenever checkbox is manually clicked, indeterminate is immediately
   * set to false.
   */
  @Input()
  get indeterminate(): boolean {
    return this._indeterminate;
  }

  set indeterminate(value: boolean) {
    const changed = value !== this._indeterminate;
    this._indeterminate = value;

    if (changed) {
      this.indeterminateChange.emit(this._indeterminate);
    }

    this.syncIndeterminate(this._indeterminate);
  }

  private _indeterminate = false;

  /** Event emitted when the checkbox's `indeterminate` value changes. */
  @Output()
  readonly indeterminateChange: EventEmitter<boolean> =
    new EventEmitter<boolean>();

  @ViewChild('input')
  _inputElement: ElementRef<HTMLInputElement>;

  inputId: string;

  /**
   * Represents one of the three states the checkbox is in
   */
  get mode() {
    if (this.value) {
      return 'checked';
    }
    if (this.indeterminate) {
      return 'mixed';
    }
    return 'unchecked';
  }

  constructor(changeDetectorRef: ChangeDetectorRef) {
    super(changeDetectorRef);
  }

  ngOnInit(): void {
    this.inputId = `${this.constructor.name}-${CheckboxComponent.uniqueId++}`;
  }

  ngAfterViewInit() {
    this.syncIndeterminate(this._indeterminate);
  }

  onChanged(value: boolean): void {
    if (this.indeterminate) {
      this._indeterminate = false;
      this.indeterminateChange.emit(this._indeterminate);
    }

    if (this.unselectValueOnCheck) {
      this._inputElement.nativeElement.checked = false;
      this.value = false;
      this.onTouch();

      return;
    }

    this.value = value;
    this.onTouch();
  }

  /**
   * Syncs the indeterminate value with the checkbox DOM node.
   *
   * We sync `indeterminate` directly on the DOM node, because in Ivy the check for whether a
   * property is supported on an element boils down to `if (propName in element)`. Domino's
   * HTMLInputElement doesn't have an `indeterminate` property so Ivy will warn during
   * server-side rendering.
   */
  private syncIndeterminate(value: boolean) {
    const nativeCheckbox = this._inputElement;

    if (nativeCheckbox) {
      nativeCheckbox.nativeElement.indeterminate = value;
    }
  }
}
