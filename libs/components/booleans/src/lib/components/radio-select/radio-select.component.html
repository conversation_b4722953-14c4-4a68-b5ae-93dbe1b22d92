<div class="radio-group" ngbRadioGroup [(ngModel)]="value" ngDefaultControl>
  @for (
    option of options;
    track trackByLabel($index, option);
    let last = $last
  ) {
    <div
      [class.hideBorder]="hideBorder"
      class="radio-item"
      [ngStyle]="{ 'margin-bottom': last ? '0' : verticalGap }"
      ngbButtonLabel
    >
      <input
        ngbButton
        [checked]="option.value === value"
        [attr.data-checked]="option.value === value"
        [id]="option.label"
        [name]="option.value"
        type="radio"
        [value]="option.value"
        #radioSelect
      />
      <label [for]="option.label">{{ option.label }}</label>
    </div>
    @if (option.description) {
      <label class="description" [for]="option.description">{{
        option.description
      }}</label>
    }
  }
</div>
