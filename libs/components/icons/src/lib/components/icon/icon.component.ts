import { HostDirective } from '@fincloud/core/host';

import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnChanges,
  ViewChild,
} from '@angular/core';
import { SafeHtml } from '@angular/platform-browser';
import { IconName } from '@fincloud/types/models';
import { ICON_NAMES_MAPPING } from '@fincloud/utils';
import { Observable, of } from 'rxjs';
import { IconColor } from '../../models/icon-color';
import { IconSize } from '../../models/icon-size';
import { IconRegistryService } from '../../services/icon-registry.service';

@Component({
  selector: 'ui-icon',
  templateUrl: './icon.component.html',
  styleUrls: ['./icon.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IconComponent implements OnChanges {
  @Input()
  title: string;

  @Input()
  name: IconName | string;

  @Input()
  size: IconSize = 'large';

  @Input()
  color: IconColor = 'regular';

  @Input()
  type: 'fill' | 'outline' = 'outline';

  @Input()
  path: string;

  @ViewChild(HostDirective, { static: false }) host!: HostDirective;

  get isSvgIcon() {
    return this.name?.startsWith('svg');
  }

  get iconName() {
    return this.iconNames[this.name as keyof typeof ICON_NAMES_MAPPING];
  }

  iconNames = ICON_NAMES_MAPPING;

  svgIcon$: Observable<SafeHtml>;

  constructor(private iconRegistryService: IconRegistryService) {}

  ngOnChanges(): void {
    if (this.isSvgIcon || this.path) {
      this.svgIcon$ = this.iconRegistryService.getSvgIcon(
        this.name,
        this.size,
        this.path,
      );
    } else {
      this.svgIcon$ = of(null);
    }
  }
}
