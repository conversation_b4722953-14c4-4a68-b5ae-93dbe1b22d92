import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import { SupportedIcons } from '@fincloud/components/icons';
import { ButtonLinkSize } from '../../models/button-link-size';
import { LinkColors } from '../../models/link-colors';

@Component({
  selector: 'ui-button-link',
  templateUrl: './button-link.component.html',
  styleUrls: ['./button-link.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ButtonLinkComponent {
  @Input()
  color: LinkColors = 'dark';

  @Input()
  label = 'Weitere Infos';

  @Input()
  size: ButtonLinkSize = 'medium';

  @Input()
  iconName: SupportedIcons = 'right-arrow';

  @Input()
  iconFirst = false;

  @Input()
  hasIcon = true;

  @Output()
  clicked = new EventEmitter();

  onClick() {
    this.clicked.emit();
  }
}
