<a
  class="btn"
  [class.shadow-none]="!elevate"
  [class.clickable]="clickable"
  [ngClass]="[type, size]"
  (click)="onClick()"
>
  @if (icon && iconPosition === 'left') {
    <ui-icon [name]="icon" size="medium" class="btn__icon--left"> </ui-icon>
  }
  <span class="btn__label">
    {{ label }}
  </span>
  @if (icon && iconPosition === 'right') {
    <ui-icon [name]="icon" size="medium" class="btn__icon--right"> </ui-icon>
  }
</a>
