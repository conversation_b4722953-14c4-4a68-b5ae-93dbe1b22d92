<button
  class="btn"
  [class.selected]="selected"
  [class.selectable]="selectable"
  [class.btn-outline]="type === 'outline'"
  [class.no-transition]="noTransition"
  [class.with-icon]="icon"
  [ngClass]="[
    type,
    size,
    colorClass,
    blockClass,
    borderType,
    customClass,
    corners,
  ]"
  [disabled]="disabled"
  [type]="nativeType"
  (click)="onClick()"
>
  @if (selected) {
    <ui-icon
      name="check"
      [size]="size === 'extra-small' ? 'medium' : 'large'"
      class="btn__selected_icon"
    >
    </ui-icon>
  }

  @if (icon) {
    <ui-icon
      [name]="icon"
      [size]="size === 'extra-small' ? 'medium' : 'large'"
      class="btn__icon"
    >
    </ui-icon>
  }
  @if (hasDotsLoader) {
    <ui-dots-loader [size]="size"></ui-dots-loader>
  }
  @if (!hasDotsLoader) {
    <span class="btn__label">
      {{ label }}
    </span>
  }
  @if (iconRight) {
    <ui-icon
      [name]="iconRight"
      [size]="size === 'extra-small' ? 'medium' : 'large'"
      class="btn__icon-right"
    >
    </ui-icon>
  }
</button>
