import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import {
  DOMAIN_BASED_ENVIRONMENT,
  EnvironmentKey,
} from '@fincloud/core/config';

@Component({
  selector: 'ui-page-ribbon',
  templateUrl: './page-ribbon.component.html',
  styleUrls: ['./page-ribbon.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PageRibbonComponent {
  private readonly blacklistedEnvKeys = [
    EnvironmentKey.PRODUCTION,
    EnvironmentKey.OTHER,
  ];

  get hideRibbon() {
    return this.blacklistedEnvKeys.includes(this.environment);
  }

  get ribbonLabel() {
    return EnvironmentKey[this.environment];
  }

  constructor(
    @Inject(DOMAIN_BASED_ENVIRONMENT)
    private environment: EnvironmentKey,
  ) {}
}
