import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  Output,
  ViewChild,
} from '@angular/core';
import { ScrollPosition } from '../../enums/scroll-position';

@Component({
  selector: 'ui-infinite-scroll',
  templateUrl: './infinite-scroll.component.html',
  styleUrls: ['./infinite-scroll.component.scss'],
})
export class InfiniteScrollComponent implements AfterViewInit {
  @ViewChild('scrollContainer', { static: false })
  scrollContainerRef!: ElementRef;

  @Input()
  shouldScrollToBottom = true;

  @Output() scrolledDown = new EventEmitter<void>();
  @Output() scrolledUp = new EventEmitter<void>();

  infiniteScrollDistance = 2;
  infiniteScrollThrottle = 50;

  ngAfterViewInit() {
    if (this.shouldScrollToBottom) {
      setTimeout(() => {
        this.scrollTo(ScrollPosition.BOTTOM);
      }, 500);
    }
  }

  onScrolledDown() {
    this.scrolledDown.emit();
  }

  onScrolledUp() {
    this.scrolledUp.emit();
  }

  scrollTo(position: ScrollPosition) {
    const container = this.scrollContainerRef.nativeElement;
    const targetPosition =
      position === ScrollPosition.BOTTOM ? container.scrollHeight : 0;
    container.scrollTo({
      top: targetPosition,
      behavior: 'smooth',
    });
  }
}
