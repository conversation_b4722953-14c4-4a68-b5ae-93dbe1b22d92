import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FileService } from '@fincloud/core/files';
import { DownloadLink } from '@fincloud/core/types';

@Component({
  selector: 'ui-dropdown-item-link',
  templateUrl: './dropdown-item-link.component.html',
  styleUrls: ['./dropdown-item-link.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DropdownItemLinkComponent {
  @Input()
  label = '';

  @Input()
  linkConfig: DownloadLink;

  constructor(private fileService: FileService) {}

  public itemClicked() {
    if (this.linkConfig) {
      this.fileService.downloadUrl(
        this.linkConfig.url,
        this.linkConfig.fileName,
      );
    }
  }
}
