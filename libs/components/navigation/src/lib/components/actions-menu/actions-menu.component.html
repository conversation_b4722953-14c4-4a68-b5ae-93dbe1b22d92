<div class="menu-trigger">
  <ng-content select="[trigger]"></ng-content>
  <ng-content></ng-content>
  <ui-icon
    appMenuTrigger
    [appOverlayContent]="menu"
    [appMenuOffset]="menuOffset"
    [isInverseArrow]="isInverseArrow"
    [inverseArrowOffset]="inverseArrowOffset"
    [showMenuBottomAndCenter]="showMenuBottomAndCenter"
    [showMenuBottomAndLeft]="showMenuBottomAndLeft"
    [showMenuBottom]="showMenuBottom"
    (closed)="toggleMenu()"
    (opened)="toggleMenu()"
    [class.hide]="hideDotsTrigger"
    [class.interaction-state]="hasInteractionState"
    [class.opened]="opened"
    name="three-dots-vert"
    [size]="iconSize"
    [class]="'menu-icon ' + customIconClass"
    [color]="iconColor"
  >
  </ui-icon>
</div>

<ng-template #menu>
  <div
    class="actions-container"
    [class.show-arrow]="!hideArrow"
    [class.extra-padding]="isInternalPortal"
    [ngClass]="[isInverseArrow ? 'inverse' : 'regular', customClass]"
    (click)="handleCursorType($event)"
  >
    <div class="action-items-container">
      <ng-template
        [ngTemplateOutletContext]="{ actionValue }"
        [ngTemplateOutlet]="optionsTemplate"
      ></ng-template>
    </div>
  </div>
</ng-template>
