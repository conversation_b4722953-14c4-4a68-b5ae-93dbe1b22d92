<div [class.enabled]="!disabled">
  @if (!disabled) {
    <a
      [routerLink]="routerLinkText"
      [queryParams]="routerLinkQueryParams"
      class="nav-link d-flex flex-row align-items-center"
      [routerLinkActive]="'active'"
      [routerLinkActiveOptions]="{ exact: routerLinkText === '' }"
    >
      <ui-tooltip [text]="setTooltipText">
        <ui-icon [name]="icon"></ui-icon>
      </ui-tooltip>
      <span class="title tw-ml-[1rem]">
        {{ navTitle }}
      </span>
      <span class="content-right ms-auto">
        <ng-content></ng-content>
      </span>
    </a>
  }
</div>
