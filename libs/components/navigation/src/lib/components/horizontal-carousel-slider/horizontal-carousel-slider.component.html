<div #dragContainer class="drag-container">
  <drag-scroll
    #dragScroll
    [scrollbar-hidden]="true"
    [scroll-x-wheel-enabled]="true"
    [drag-scroll-y-disabled]="true"
    (reachesLeftBound)="setArrowVisibilityLeft($event)"
    (reachesRightBound)="setArrowVisibilityRight($event)"
  >
    @for (element of allElements; track element) {
      <div drag-scroll-item>
        <ng-template
          [ngTemplateOutlet]="elementTemplate"
          [ngTemplateOutletContext]="{ element: element }"
        ></ng-template>
      </div>
    }
  </drag-scroll>
  <div class="carousel-actions">
    @if (arrowsVisibility.left) {
      <div class="left arrow heading6" (click)="moveLeft()">
        <ui-icon name="chevron_left"></ui-icon>
      </div>
    }
    @if (arrowsVisibility.right) {
      <div class="right arrow heading6" (click)="moveRight()">
        <ui-icon name="right"></ui-icon>
      </div>
    }
  </div>
</div>
