import { Color } from '@fincloud/components/styles';
import { merge } from 'lodash-es';
import { CircleProgressOptions } from 'ng-circle-progress';

export class CircleProgressConfig extends CircleProgressOptions {
  radius: number;
  outerStrokeWidth: number;
  outerStrokeColor: string;
  backgroundStroke: string;
  showTitle: boolean;
  showUnits: boolean;
  titleColor: string;
  subtitleColor: string;
  unitsFontSize: string;
  unitsColor: string;
  titleFontSize: string;
  showSubtitle: boolean;

  constructor() {
    super();

    this.clockwise = true;
    this.outerStrokeColor = Color.PRIMARY;
    this.startFromZero = false;
    this.animation = true;
    this.animationDuration = 300;
    this.showTitle = false;
    this.showSubtitle = false;
    this.showUnits = false;
    this.showInnerStroke = false;
    this.backgroundStroke = Color.GRAY;
    this.renderOnClick = false;
  }

  static withOptions(
    overrides: Partial<CircleProgressConfig>,
  ): CircleProgressConfig {
    return merge(new CircleProgressConfig(), overrides);
  }

  withStrokeWidth(strokeWidth: number) {
    this.outerStrokeWidth = strokeWidth;
    this.backgroundStrokeWidth = strokeWidth;
    this.backgroundPadding = -1 * (strokeWidth / 2);

    return this;
  }
}
