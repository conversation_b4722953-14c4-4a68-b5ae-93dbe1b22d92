<div class="circle-progress">
  @if (config) {
    <circle-progress
      [percent]="percent"
      [radius]="config.radius"
      [clockwise]="config.clockwise"
      [outerStrokeWidth]="config.outerStrokeWidth"
      [outerStrokeColor]="config.outerStrokeColor"
      [startFromZero]="config.startFromZero"
      [animation]="config.animation"
      [animationDuration]="config.animationDuration"
      [showTitle]="config.showTitle"
      [showSubtitle]="config.showSubtitle"
      [showUnits]="config.showUnits"
      [showInnerStroke]="config.showInnerStroke"
      [backgroundStroke]="config.backgroundStroke"
      [backgroundStrokeWidth]="config.backgroundStrokeWidth"
      [backgroundPadding]="config.backgroundPadding"
      [renderOnClick]="config.renderOnClick"
      [showZeroOuterStroke]="config.showZeroOuterStroke"
      [titleColor]="config.titleColor"
      [unitsColor]="config.unitsColor"
      [unitsFontSize]="config.unitsFontSize"
      [titleFontSize]="config.titleFontSize"
      [responsive]="config.responsive"
      [showImage]="config.showImage"
      [imageSrc]="config.imageSrc"
      [backgroundColor]="config.backgroundColor"
      [imageHeight]="config.imageWidth"
      [imageWidth]="config.imageHeight"
    >
    </circle-progress>
  }
  <ng-content></ng-content>
</div>
