<div
  class="chart"
  [ngClass]="classes"
  [class.full-width]="showAsFullWidth"
  appTooltipContainer
>
  @for (item of items; track item; let last = $last) {
    <div
      class="block"
      [style.width.%]="item.ratio"
      [ngbTooltip]="tipContent"
      appTooltipTrigger
      [context]="item"
      [class.cursor-pointer]="item.clickable"
      [tooltipEnabled]="showPercentage"
      tooltipClass="dark-blue hide-arrow left-aligned padding-small"
      container="body"
      [class.transparent-block]="showLastItemAsTransparent && last"
      [autoClose]="true"
      [closeDelay]="0"
      [triggers]="'manual'"
      (click)="elementClicked(item)"
      placement="bottom"
    ></div>
  }
</div>

<ng-template #tipContent let-context>
  <div class="tooltip-container">
    <p>{{ context.name }}</p>
    <p>
      {{
        (showExtraInfo ? (!!context?.extra ? context?.extra + ' ' : '') : '') +
          (context.value | currency | removeTrailingZeros)
      }}
    </p>
  </div>
</ng-template>
