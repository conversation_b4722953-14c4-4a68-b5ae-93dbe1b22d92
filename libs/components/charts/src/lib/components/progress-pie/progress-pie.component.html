<ngx-charts-chart
  class="pie-progress-chart"
  [ngClass]="color"
  [view]="[width, height]"
  [showLegend]="false"
  [animations]="animations"
>
  <svg:g [attr.transform]="transform" class="pie-grid chart">
    @for (series of series; track series) {
      <svg:g class="pie-grid-item" [attr.transform]="series.transform">
        <svg:g
          ngx-charts-pie-grid-series
          [colors]="series.colors"
          [data]="series.data"
          [innerRadius]="series.innerRadius"
          [outerRadius]="series.outerRadius"
          [animations]="animations"
        />
        <svg:g
          ngx-charts-circle
          class="outer-circle"
          [cx]="outerCircle.cx"
          [cy]="outerCircle.cy"
          [r]="outerCircle.radius"
          [fill]="outerCircle.color"
          [class.active]="false"
          [pointerEvents]="'none'"
          [data]="outerCircle.value"
          [classNames]="outerCircle.classNames"
        />
        <svg:g
          ngx-charts-circle
          class="inner-circle"
          [cx]="innerCircle.cx"
          [cy]="innerCircle.cy"
          [r]="innerCircle.radius"
          [fill]="innerCircle.color"
          [class.active]="false"
          [pointerEvents]="'none'"
          [data]="innerCircle.value"
          [classNames]="innerCircle.classNames"
        />
        @if (animations) {
          <svg:text
            class="label percent-label"
            dy="0"
            x="0"
            y="5"
            ngx-charts-count-up
            [countTo]="progressPercent"
            [countSuffix]="'%'"
            text-anchor="middle"
          ></svg:text>
        }
        @if (!animations) {
          <svg:text
            class="label percent-label"
            dy="-0.5em"
            x="0"
            y="5"
            text-anchor="middle"
          >
            {{ progressPercent }}
          </svg:text>
        }
      </svg:g>
    }
  </svg:g>
</ngx-charts-chart>
