import { MathUtils } from '@fincloud/core/math';

import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import { Circle, PieGridComponent } from '@swimlane/ngx-charts';
import { ProgressValue } from '../../models/progress-value';

@Component({
  selector: 'ui-progress-pie',
  templateUrl: './progress-pie.component.html',
  styleUrls: ['./progress-pie.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProgressPieComponent extends PieGridComponent implements OnInit {
  @Input()
  progress: ProgressValue;

  @Input()
  view: [number, number] = [200, 200];

  @Input()
  color: 'primary' | 'secondary' = 'primary';

  outerCircle: Partial<Circle>;
  innerCircle: Partial<Circle>;

  seriesName = 'progress-pie-series';
  progressPercent: number;

  ngOnInit() {
    super.ngOnInit();

    this.progressPercent = MathUtils.roundToInteger(this.progress.ratio * 100);
    this.margin = [0, 0, 0, 0];
    this.designatedTotal = this.progress.total;

    this.results = [
      {
        name: this.seriesName,
        value: this.progress.value,
      },
    ];
  }

  update() {
    super.update();

    const series = this.series[0] as {
      innerRadius: number;
      transform: string;
    };

    if (series) {
      const xPos = this.view[0] / 2;
      const yPos = this.view[1] / 2;
      series.transform = `translate(${xPos}, ${yPos})`;

      this.outerCircle = {
        cx: 0,
        cy: 0,
        radius: series.innerRadius * 1.1,
      };
      this.innerCircle = {
        cx: 0,
        cy: 0,
        radius: series.innerRadius * 0.8,
      };
    }
  }
}
