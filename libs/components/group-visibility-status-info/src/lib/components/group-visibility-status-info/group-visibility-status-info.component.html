@if (!disableTooltip) {
  <ui-tooltip
    placement="top"
    [template]="groupVisibilityTooltip"
    padding="small"
    class="button-link-hover-tooltip hoverable"
    [closeDelay]="0"
    [openDelay]="400"
  >
    <fin-icon
      class="group-visibility"
      [name]="groupVisibilityIcon"
      [size]="finSize.M"
    ></fin-icon>
  </ui-tooltip>
}
@if (disableTooltip) {
  <fin-icon
    class="group-visibility"
    [name]="groupVisibilityIcon"
    [size]="finSize.M"
  ></fin-icon>
}
<ng-template #groupVisibilityTooltip>
  <div class="tooltip-wrapper-visibility">
    <div class="main">{{ tooltipTitle }}</div>
    <div class="sub">{{ tooltipDescription }}</div>
  </div>
</ng-template>
