<div>
  <div class="wrapper" [class.selected]="status === currentStatus">
    <ui-icon
      [name]="icon"
      [color]="status === currentStatus ? 'primary' : 'subtle'"
      class="btn__selected_icon"
    >
    </ui-icon>
    <span class="label">{{ label }}</span>
    @if (expandable && status !== currentStatus) {
      <ui-icon name="expand_more" color="subtle" class="ms-auto"> </ui-icon>
    }
  </div>
</div>
