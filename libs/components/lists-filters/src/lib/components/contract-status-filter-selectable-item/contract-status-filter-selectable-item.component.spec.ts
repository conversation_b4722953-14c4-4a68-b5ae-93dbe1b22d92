import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ContractStatusFilterSelectableItemComponent } from './contract-status-filter-selectable-item.component';

describe('ContractStatusFilterSelectableItemComponent', () => {
  let component: ContractStatusFilterSelectableItemComponent;
  let fixture: ComponentFixture<ContractStatusFilterSelectableItemComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ContractStatusFilterSelectableItemComponent],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(
      ContractStatusFilterSelectableItemComponent,
    );
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
