<div class="quick-filter-card">
  <ui-switch
    [label]="switchLabel"
    labelPosition="left"
    [(ngModel)]="filter.isActive"
    (ngModelChange)="emitFiltersChangedEvent()"
  ></ui-switch>

  <div class="filter-expand-button" (click)="onExpandedCollapsed()">
    <span>
      {{ floorCeilPlaceholder }}
    </span>
    <ui-icon
      [name]="expanded ? 'expand_less' : 'expand_more'"
      size="large"
    ></ui-icon>
  </div>
  @if (expanded) {
    <div class="slider-wrapper">
      <ngx-slider
        [(value)]="filter.min"
        [(highValue)]="filter.max"
        [options]="options"
        (userChangeEnd)="emitFiltersChangedEvent()"
      >
      </ngx-slider>
      <div class="slider-min-max">
        <span>
          {{ options.floor | currency | removeTrailingZeros }}
        </span>
        <span>
          {{ options.ceil | currency | removeTrailingZeros }}
        </span>
      </div>
    </div>
  }
</div>
