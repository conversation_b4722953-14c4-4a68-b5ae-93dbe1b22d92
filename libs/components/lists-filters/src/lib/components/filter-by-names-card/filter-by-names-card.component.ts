import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
} from '@angular/core';
import { SelectOptionItem } from '@fincloud/types/models';

@Component({
  selector: 'ui-filter-by-names-card',
  templateUrl: './filter-by-names-card.component.html',
  styleUrls: ['./filter-by-names-card.component.scss'],
})
export class FilterByNamesCardComponent<T> implements OnChanges {
  @Output() filterChanged = new EventEmitter<SelectOptionItem<T>[]>();

  @Input() switchLabel: string;
  @Input() options: SelectOptionItem<T>[];

  optionsToSelectFrom: SelectOptionItem<T>[] = [];
  selectedOptions: SelectOptionItem<T>[] = [];
  filterActive = false;

  ngOnChanges(changes: SimpleChanges): void {
    if ('options' in changes) {
      this.optionsToSelectFrom = this.options;
    }
  }

  selectOption(selectOptionItem: SelectOptionItem<T>) {
    this.selectedOptions = [...this.selectedOptions, selectOptionItem];
    this.optionsToSelectFrom = this.optionsToSelectFrom.filter(
      (option) => option !== selectOptionItem,
    );

    if (this.filterActive) {
      this.filterChanged.emit(this.selectedOptions);
    }
  }

  unselectOption(selectOptionItem: SelectOptionItem<T>) {
    this.optionsToSelectFrom = [...this.optionsToSelectFrom, selectOptionItem];
    this.selectedOptions = this.selectedOptions.filter(
      (option) => option !== selectOptionItem,
    );

    if (this.filterActive) {
      this.filterChanged.emit(this.selectedOptions);
    }
  }

  toggleFilterActive() {
    this.filterActive
      ? this.filterChanged.emit(this.selectedOptions)
      : this.filterChanged.emit([]);
  }
}
