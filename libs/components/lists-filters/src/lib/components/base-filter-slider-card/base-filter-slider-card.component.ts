import { Options } from '@angular-slider/ngx-slider';
import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { FiltersService } from '@fincloud/core/filters';
import {
  CustomerFilter,
  MinMaxFilter,
} from '@fincloud/swagger-generator/watchlist';

@Component({ template: '' })
export class BaseFilterSliderCardComponent<
  T extends MinMaxFilter | CustomerFilter,
> implements OnChanges
{
  @Input() switchLabel: string;
  @Input() options: Options;
  @Input() filter: T;

  manualRefresh = new EventEmitter();

  constructor(private filtersService: FiltersService) {}

  ngOnChanges(_changes: SimpleChanges): void {
    this.manualRefresh.emit();
  }

  emitFiltersChangedEvent(isSlider = false) {
    if (isSlider) {
      this.filter.isActive = true;
    }

    this.filtersService.filtersChange(this.filter);
  }
}
