import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import {
  AbstractControl,
  UntypedFormControl,
  ValidationErrors,
  ValidatorFn,
} from '@angular/forms';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { DateRange } from '@fincloud/types/models';
import * as dayjs from 'dayjs';

@Component({
  selector: 'ui-date-range-filter-card',
  templateUrl: './date-range-filter-card.component.html',
  styleUrls: ['./date-range-filter-card.component.scss'],
})
export class DateRangeFilterCardComponent implements OnInit {
  @Output() filterChanged = new EventEmitter<DateRange>();

  fromDateControl = new UntypedFormControl();
  toDateControl = new UntypedFormControl();
  filterActive = false;
  dateFormat: string;
  dateFormatAndTime: string;

  constructor(private regionalSettings: RegionalSettingsService) {
    this.dateFormat = this.regionalSettings.dateFormat;
    this.dateFormatAndTime = `${this.dateFormat} HH:mm`;
  }

  ngOnInit(): void {
    this.fromDateControl.setValidators([
      (control: AbstractControl) =>
        this.isBeforeDateValidator(control.value)(control),
    ]);
    this.toDateControl.setValidators([
      (control: AbstractControl) =>
        this.isAfterDateValidator(control.value)(control),
    ]);
  }

  get fromToValues(): DateRange {
    return {
      from: this.fromDateControl.value as string,
      to: this.toDateControl.value as string,
    };
  }

  saveValue() {
    this.filterActive = true;
    this.filterChanged.next(this.fromToValues);
  }

  private isBeforeDateValidator(date: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (control.value === null || this.toDateControl.value === null) {
        return null;
      }

      return dayjs(date).isAfter(dayjs(this.toDateControl.value))
        ? { invalidDate: "'From' date is after 'to' date" }
        : null;
    };
  }

  private isAfterDateValidator(date: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (control.value === null || this.fromDateControl.value === null) {
        return null;
      }

      return dayjs(date).isBefore(dayjs(this.fromDateControl.value))
        ? { invalidDate: "'To' date is before 'from' date" }
        : null;
    };
  }
}
