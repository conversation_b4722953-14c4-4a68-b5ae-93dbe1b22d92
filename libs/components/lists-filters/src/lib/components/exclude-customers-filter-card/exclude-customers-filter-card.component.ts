import { Component, OnChanges, SimpleChanges } from '@angular/core';
import {
  Customer,
  CustomerFilter,
} from '@fincloud/swagger-generator/watchlist';
import { SelectOptionItem } from '@fincloud/types/models';
import { BaseFilterSliderCardComponent } from '../base-filter-slider-card/base-filter-slider-card.component';

@Component({
  selector: 'ui-exclude-customers-filter-card',
  templateUrl: './exclude-customers-filter-card.component.html',
  styleUrls: ['./exclude-customers-filter-card.component.scss'],
})
export class ExcludeCustomersFilterCardComponent
  extends BaseFilterSliderCardComponent<CustomerFilter>
  implements OnChanges
{
  excludedCustomers: SelectOptionItem<Customer>[] = [];
  includedCustomers: SelectOptionItem<Customer>[] = [];

  ngOnChanges(changes: SimpleChanges): void {
    super.ngOnChanges(changes);
    if (changes.filter) {
      this.excludedCustomers = [];
      this.includedCustomers = [];
      this.filter.customerEntries.forEach((customer) =>
        customer.show
          ? this.addToIncludedCustomers(customer)
          : this.addToExcludedCustomers(customer),
      );
    }
  }

  excludeCustomer(selectedCustomer: SelectOptionItem<Customer>) {
    this.excludedCustomers = [...this.excludedCustomers, selectedCustomer];
    this.includedCustomers = this.includedCustomers.filter(
      (customer) => customer !== selectedCustomer,
    );
    selectedCustomer.value.show = false;

    if (this.excludedCustomers.length) {
      this.filter.isActive = true;
    }

    if (this.filter.isActive) {
      this.emitFiltersChangedEvent();
    }
  }

  includeCustomer(selectedCustomer: SelectOptionItem<Customer>) {
    this.includedCustomers = [...this.includedCustomers, selectedCustomer];
    this.excludedCustomers = this.excludedCustomers.filter(
      (customer) => customer !== selectedCustomer,
    );
    selectedCustomer.value.show = true;

    if (!this.filter.isActive) {
      return;
    }

    this.emitFiltersChangedEvent();
  }

  private addToIncludedCustomers(customer: Customer) {
    if (
      !this.includedCustomers.find(({ name }) => name === customer.customerName)
    ) {
      this.includedCustomers.push({
        name: customer.customerName,
        value: customer,
      });
    }
  }

  private addToExcludedCustomers(customer: Customer) {
    if (
      !this.excludedCustomers.find(({ name }) => name === customer.customerName)
    ) {
      this.excludedCustomers.push({
        name: customer.customerName,
        value: customer,
      });
    }
  }
}
