<div class="filter-card">
  <ui-switch
    [label]="switchLabel"
    labelPosition="left"
    [(ngModel)]="filter.isActive"
    (ngModelChange)="emitFiltersChangedEvent()"
  ></ui-switch>
  <ng-content></ng-content>
  <div class="slider-wrapper">
    @if (switchLabel !== 'Finanzierungsvolumen') {
      <span class="hyphen">-</span>
    }
    <ngx-slider
      [(value)]="filter.min"
      [(highValue)]="filter.max"
      [options]="options"
      (userChange)="manualRefresh.emit()"
      (userChangeEnd)="emitFiltersChangedEvent(true)"
      [manualRefresh]="manualRefresh"
    >
    </ngx-slider>
  </div>
</div>
