<div class="quick-filter-card">
  <ui-switch
    [label]="switchLabel"
    labelPosition="left"
    [(ngModel)]="filter.isActive"
    (ngModelChange)="emitFiltersChangedEvent()"
  ></ui-switch>
  <div>
    @if (!expanded) {
      <div class="collapsed-values">
        {{ floorCeilPlaceholder }}
      </div>
    }
    <div class="expand-collapse-icon" (click)="onExpandedCollapsed()">
      @if (!expanded) {
        <ui-icon name="expand"></ui-icon>
      }
      @if (expanded) {
        <ui-icon name="collapse"></ui-icon>
      }
    </div>
  </div>

  @if (expanded) {
    <div class="slider-wrapper">
      <ngx-slider
        [(value)]="filter.min"
        [(highValue)]="filter.max"
        [options]="options"
        (userChange)="manualRefresh.emit()"
        (userChangeEnd)="emitFiltersChangedEvent(true)"
        [manualRefresh]="manualRefresh"
      >
      </ngx-slider>
    </div>
  }
</div>
