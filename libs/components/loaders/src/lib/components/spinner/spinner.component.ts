import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { NgxSpinnerService, Size } from 'ngx-spinner';

@Component({
  selector: 'ui-spinner',
  templateUrl: './spinner.component.html',
  styleUrls: ['./spinner.component.scss'],
})
export class SpinnerComponent implements OnChanges {
  @Input()
  theme: 'dark' | 'light' | 'black' | 'blendIn' | 'purple' = 'dark';

  @Input()
  size: Size = 'medium';

  @Input()
  customSize = false;

  @Input()
  showSpinner = false;

  @Input()
  loadingText = '';

  @Input()
  spinnerName?: string = '';

  //Default from ngx-spinner is 99_999
  @Input()
  zIndex?: number = 1;

  constructor(private spinnerService: NgxSpinnerService) {}

  ngOnChanges(changes: SimpleChanges): void {
    if ('showSpinner' in changes) {
      this.showSpinner
        ? this.spinnerService.show(this.spinnerName)
        : this.spinnerService.hide(this.spinnerName);
    }
  }
}
