<input
  class="text-input"
  [type]="'text'"
  [tabindex]="tabIndex"
  [ngClass]="[size, bckgColor]"
  [ngModel]="value"
  [class.text-size-inherit]="textSizeStrategy === 'inherited'"
  (blur)="onBlur()"
  [class.left-addon]="leftAddon"
  [class.right-addon]="rightAddon"
  [class.error]="(hasError && hasInstantFeedback) || forceHasError"
  [class.shadow]="hasShadow"
  [class.no-border]="!hasBorder"
  [class.no-focus]="!hasFocus"
  [placeholder]="placeholder"
  [disabled]="disabled"
  [class.disabled]="disabled"
  [readonly]="isReadonly"
  [maxlength]="maxLength"
  [appFocus]="focused"
  (ngModelChange)="onInputChanged($event)"
  #field
  (keydown)="checkForPattern($event)"
  (keydown.enter)="field.blur()"
  (paste)="onPaste($event)"
  [class.isPointer]="isPointer"
  [ngStyle]="{ 'padding-right.px': suffix ? setPaddingSuffix() : '' }"
/>
@if (suffix) {
  <span class="suffix" #suffixTemplate>{{ suffix }}</span>
}
