import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NsUiIconsModule } from '@fincloud/components/icons';
import { NsUiSelectsModule } from '@fincloud/components/selects';
import { NsUiTooltipModule } from '@fincloud/components/tooltip';
import { NsCoreFocusModule } from '@fincloud/core/focus';
import { NsCorePipesModule } from '@fincloud/core/pipes';
import { NgxCurrencyDirective } from 'ngx-currency';
import { CurrencyInputComponent } from './components/currency-input/currency-input.component';
import { DropdownInputComponent } from './components/dropdown-input/dropdown-input.component';
import { IncrementInputComponent } from './components/increment-input/increment-input.component';
import { MonetaryExpressionComponent } from './components/monetary-expression/monetary-expression.component';
import { NumberInputComponent } from './components/number-input/number-input.component';
import { NumberTagComponent } from './components/number-tag/number-tag.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    NsCoreFocusModule,
    NgxCurrencyDirective,
    NsUiSelectsModule,
    NsUiIconsModule,
    NsUiTooltipModule,
    NsCorePipesModule,
  ],
  declarations: [
    CurrencyInputComponent,
    DropdownInputComponent,
    IncrementInputComponent,
    MonetaryExpressionComponent,
    NumberInputComponent,
    NumberTagComponent,
  ],
  exports: [
    CurrencyInputComponent,
    DropdownInputComponent,
    IncrementInputComponent,
    MonetaryExpressionComponent,
    NumberInputComponent,
    NumberTagComponent,
  ],
  providers: [],
})
export class NsUiNumberModule {}
