import { AbstractValueAccessor } from '@fincloud/core/form';

import {
  AfterViewInit,
  ChangeDetectorRef,
  Directive,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import { ObservableInput } from 'rxjs';
import { FileUploaderComponent } from '../components/file-uploader/file-uploader.component';
import { FileInfo } from '../models/file';
import { FileHolder } from '../models/file-holder';
import { FileUploaderOptions } from '../models/file-uploader-options';

@Directive()
export abstract class FileUploadBase
  extends AbstractValueAccessor<File[] | File | FileInfo>
  implements AfterViewInit
{
  @Output() public uploadFinished = new EventEmitter();
  private _options: FileUploaderOptions;
  protected files: FileHolder[];

  @Input() onUpload(handler: (file: FileHolder) => ObservableInput<unknown>) {
    this.fileUploader.onUpload(handler);
  }

  @Input() set options(options: FileUploaderOptions) {
    this._options = options;

    if (this.fileUploader) {
      this.fileUploader.options = this._options;
    }
  }
  get options() {
    return this._options;
  }

  public abstract fileUploader: FileUploaderComponent;

  get isUploading() {
    return this.fileUploader.files.some((f) => {
      return f.status === 'uploading';
    });
  }

  constructor(changeDetectorRef: ChangeDetectorRef) {
    super(changeDetectorRef);
  }

  ngAfterViewInit() {
    this.fileUploader.fileSelected.subscribe((files: FileList) =>
      this.fileSelected(files),
    );
    this.fileUploader.fileRemoved.subscribe((file: FileHolder) =>
      this.fileRemoved(file),
    );
    this.fileUploader.uploadStarted.subscribe((files: FileList) =>
      this.uploadStarted(files),
    );
    this.fileUploader.uploadFinished.subscribe(() =>
      this.uploadFinishedCallback(),
    );

    if (this._options) {
      this.fileUploader.options = this._options;
    }
  }

  public upload() {
    this.fileUploader.upload();
  }

  public abort() {
    this.fileUploader.abort();
  }

  public chooseFile() {
    this.fileUploader.chooseFile();
  }

  protected fileSelected(_files: FileList) {
    this.updateValue();
  }

  protected fileRemoved(_fileHolder: FileHolder) {
    this.updateValue();
  }

  protected uploadStarted(_files: FileList) {
    // Intentionally empty
  }

  protected uploadFinishedCallback() {
    this.uploadFinished.emit();
  }

  protected getFileValue(fileHolder: FileHolder) {
    return {
      fileName: fileHolder?.file?.name,
    };
  }

  private updateValue() {
    let value: File | File[] | FileInfo;
    const valueCloned = { ...this.value };
    if (this.fileUploader.options.maxFilesCount === 1) {
      value = Object.assign(
        valueCloned,
        this.getFileValue(this.fileUploader.files[0]),
      );
    } else {
      value = this.fileUploader.files.map((f) => {
        const fileValue =
          (<File[]>valueCloned).find(
            (file: File) => file.name === f.file.name,
          ) || {};
        Object.assign(fileValue, this.getFileValue(this.fileUploader.files[0]));

        return <File>fileValue;
      });
    }

    this.value = value;
  }
}
