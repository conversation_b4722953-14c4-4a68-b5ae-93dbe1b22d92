import { defer, ObservableInput, Subscription, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { FileHolderStatus } from './file-holder-status';

export class FileHolder {
  private _status: FileHolderStatus = 'ready';
  private _progress = 0;
  private _error: string;
  private _src: string;

  private activeUploadSubscription: Subscription;

  get uploading() {
    return this.status === 'uploading';
  }

  get done() {
    return this.status === 'done';
  }

  get fileSrc() {
    return this._src;
  }

  set fileSrc(value: string) {
    this._src = value;
  }

  get status() {
    return this._status;
  }

  get progress() {
    return this._progress;
  }

  get error() {
    return this._error;
  }

  updateProgress(value: number) {
    this._progress = value;
  }

  constructor(
    public file: File,
    private uploadHandler: (file: FileHolder) => ObservableInput<unknown>,
  ) {}

  upload(onUploaded: () => unknown, onError: (err: Error) => unknown) {
    this.activeUploadSubscription = this.startUpload().subscribe({
      next: () => {
        onUploaded();
      },
      error: (err: Error) => {
        onError(err);
      },
    });
  }

  cancel() {
    this.activeUploadSubscription.unsubscribe();
    this.activeUploadSubscription = null;
    this._status = 'aborted';
  }

  private startUpload() {
    this._status = 'uploading';
    return defer(() => this.uploadHandler(this)).pipe(
      map(() => {
        this._status = 'done';
      }),
      catchError((err: string) => {
        this._status = 'failed';
        this._error = err;
        return throwError(err);
      }),
    );
  }
}
