import {
  makeControlValidatorProvider,
  makeControlValueAccessorProvider,
} from '@fincloud/core/form';
import { ManageComponentRefService } from '@fincloud/core/services';

import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { FinButtonAppearance, FinButtonShape } from '@fincloud/ui/button';
import { FinSize } from '@fincloud/ui/types';
import { ObjectHelper } from '@fincloud/utils';
import { take } from 'rxjs/operators';
import { FileUploadBase } from '../../directives/file-upload-base.directive';
import { FileInfo } from '../../models/file';
import { FileHolder } from '../../models/file-holder';
import { FileUploaderOptions } from '../../models/file-uploader-options';
import { FileUploaderComponent } from '../file-uploader/file-uploader.component';

@Component({
  selector: 'ui-single-file-upload',
  templateUrl: './single-file-upload.component.html',
  styleUrls: ['./single-file-upload.component.scss'],
  providers: [
    makeControlValueAccessorProvider(SingleFileUploadComponent),
    makeControlValidatorProvider(SingleFileUploadComponent),
    ManageComponentRefService,
  ],
})
export class SingleFileUploadComponent
  extends FileUploadBase
  implements OnInit, AfterViewInit
{
  @ViewChild(FileUploaderComponent) fileUploader: FileUploaderComponent;

  @Input() isFileUploadInProgress: boolean;
  @Input() documentId: string;
  @Input() downloadButtonTemplate: TemplateRef<unknown>;

  public file: FileHolder;
  public isFileRemoved: boolean;
  public fileUploaded = new EventEmitter<boolean>();

  readonly finButtonAppearance = FinButtonAppearance;
  readonly finSize = FinSize;
  readonly finShape = FinButtonShape;

  constructor(
    changeDetectorRef: ChangeDetectorRef,
    viewContainerRef: ViewContainerRef,
    private manageComponentRefService: ManageComponentRefService,
  ) {
    super(changeDetectorRef);
    this.manageComponentRefService.viewContainerRef = viewContainerRef;
  }

  ngOnInit(): void {
    this.options = ObjectHelper.mergeDeep(
      {
        maxFilesCount: 1,
        replaceFiles: true,
        autoUpload: true,
      } as FileUploaderOptions,
      this.options,
    );
  }

  ngAfterViewInit() {
    super.ngAfterViewInit();

    this.fileUploader.fileUploaded
      .pipe(take(1))
      .subscribe(() => this.fileUploaded.emit(true));
  }

  public removeDocument() {
    this.fileUploader.removeAll();
    this.isFileRemoved = true;
  }

  public get fileName() {
    if (this.value && (<FileInfo>this.value).fileName) {
      return (<FileInfo>this.value).fileName;
    } else {
      return null;
    }
  }

  public chooseFile() {
    this.fileUploader.chooseFile();
  }

  protected fileRemoved(_fileHolder: FileHolder) {
    this.value = null;
    this.file = null;
  }

  protected fileSelected(files: FileList) {
    super.fileSelected(files);

    this.file = this.fileUploader.files[0];
  }
}
