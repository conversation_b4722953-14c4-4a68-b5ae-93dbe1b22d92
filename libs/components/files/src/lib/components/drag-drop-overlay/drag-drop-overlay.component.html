<div class="drag-overlay">
  <div
    class="background"
    [class.dashed]="hasDashedOffset"
    [class.rounded]="hasRoundedCorners"
  ></div>
  <div class="dragzone-info">
    <ui-button
      size="large"
      corners="pointy"
      borderType="dashed"
      color="white"
      type="outline"
      label="Drag-and-Drop"
    >
    </ui-button>
    @if (label) {
      <div class="label">{{ label }}</div>
    }
  </div>
</div>
