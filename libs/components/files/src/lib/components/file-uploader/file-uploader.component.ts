import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { FileService } from '@fincloud/core/files';
import { Toast } from '@fincloud/core/toast';
import { FinToastService } from '@fincloud/ui/toast';
import { ObservableInput } from 'rxjs';
import { FileHolder } from '../../models/file-holder';
import { FileUploaderOptions } from '../../models/file-uploader-options';
import { FileValidationError } from '../../models/file-validation-error';
import { IFileUploader } from '../../models/ifile-uploader';
import { UploadValidationErrors } from '../../models/upload-validation-errors';

@Component({
  selector: 'ui-file-uploader',
  templateUrl: './file-uploader.component.html',
  styleUrls: ['./file-uploader.component.scss'],
})
export class FileUploaderComponent implements IFileUploader, OnInit {
  private _options: FileUploaderOptions;

  @Input() set options(options: FileUploaderOptions) {
    this._options = options;
    this.changeDetectorRef.detectChanges();
  }

  get options() {
    return this._options;
  }

  @Output() public fileValidationError =
    new EventEmitter<FileValidationError>();

  @Output() public fileSelected = new EventEmitter<Array<File>>();

  @Output() public fileRemoved = new EventEmitter<FileHolder>();
  @Output() public fileUploaded = new EventEmitter<FileHolder>();

  @Output() public uploadStarted = new EventEmitter<FileHolder[]>();
  @Output() public uploadFinished = new EventEmitter<FileHolder[]>();
  @Output() public uploadAborted = new EventEmitter<FileHolder[]>();

  public validationError: UploadValidationErrors;

  public toastErrorMaxFileCountReachedPrefix = $localize`:@@fileUploader.toast.error.maxFileCount.prefix:Maximale Dateianzahl erreicht. Es können maximal`;
  public toastErrorMaxFileCountReachedSuffix = $localize`:@@fileUploader.toast.error.maxFileCount.suffix:gleichzeitig hochgeladen werden.`;
  public toastErrorUnsupportedFileTypePrefix = $localize`:@@fileUploader.toast.error.unsupportedType.prefix:Nicht unterstützter Dateityp. Unterstützte Dateitypen sind:`;

  @ViewChild('input', { static: true })
  private fileElementRef: ElementRef<HTMLInputElement>;

  private uploadHandler: (file: FileHolder) => ObservableInput<unknown>;

  private get fileElement() {
    return this.fileElementRef.nativeElement;
  }

  private _files: FileHolder[] = [];
  private fileCounter = 0;
  private get pendingFilesCounter() {
    return this._files.filter((f) => ['uploading', 'ready'].includes(f.status))
      .length;
  }

  private get countRemainingSlots() {
    return this._options.maxFilesCount - this.fileCounter;
  }

  private get defaultUploadOptions(): FileUploaderOptions {
    return {
      replaceFiles: null,
      autoLoad: false,
      autoUpload: false,
      removeAfterUpload: false,
      maxFilesCount: 10,
    };
  }

  constructor(
    private finToastService: FinToastService,
    private changeDetectorRef: ChangeDetectorRef,
    private fileService: FileService,
  ) {}

  ngOnInit() {
    if (!this.options) {
      this.options = this.defaultUploadOptions;
    }
  }

  public get files(): ReadonlyArray<FileHolder> {
    return this._files;
  }

  public chooseFile() {
    this.fileElement.click();
  }

  public onFileChange(fileList: FileList) {
    const fileListArray = Array.from(fileList).filter((file) => !!file.type);
    if (this.options.replaceFiles) {
      this.removeAll();
    }

    const files = this.getFilesToUpload(fileListArray);

    if (files.length > 0) {
      this.fileCounter += files.length;

      void this.initFiles(files);
      this.fileSelected.emit(files);
    }
  }

  public removeAll() {
    this.abort();

    const canceledFiles = this._files;
    this._files = [];
    this.fileCounter = 0;
    this.fileElement.value = '';
    canceledFiles.forEach((f) => {
      this.fileRemoved.emit(f);
    });
  }

  public removeFile(fileName: string) {
    const index = this._files.findIndex((f) => f.file.name === fileName);

    if (index > -1) {
      this.cancelUpload(this._files[index]);
      this.fileRemoved.emit(this._files[index]);

      this._files.splice(index, 1);
      this.fileCounter--;
      this.fileElement.value = '';
    }
  }

  public upload() {
    if (!this.uploadHandler) {
      throw Error('Upload Handler should be set');
    }

    this.uploadStarted.emit(this._files);

    this._files.forEach((fh) => {
      this.uploadSingleFile(fh);
    });
  }

  public onUpload(handler: (file: FileHolder) => ObservableInput<unknown>) {
    this.uploadHandler = handler;
  }

  public load() {
    this._files.forEach(async (fh) => {
      if (!fh.fileSrc) {
        fh.fileSrc = await this.loadFile(fh);
      }
    });
  }

  public abort() {
    const abortedFiles = [];
    for (const file of this._files) {
      const aborted = this.cancelUpload(file);

      if (aborted && aborted.status === 'aborted') {
        abortedFiles.push(aborted);
      }
    }

    this.uploadAborted.emit(abortedFiles);
  }

  private cancelUpload(file: FileHolder) {
    if (file.uploading) {
      file.cancel();

      return file;
    } else {
      return null;
    }
  }

  private async initFiles(files: Array<File>) {
    for (const file of files) {
      const fileHolder = new FileHolder(file, (f) => {
        return this.uploadHandler(f);
      });

      this._files.push(fileHolder);

      if (this.options.autoLoad) {
        fileHolder.fileSrc = await this.loadFile(fileHolder);
      }

      if (this.options.autoUpload) {
        this.uploadSingleFile(fileHolder);
      }
    }
  }

  private getFilesToUpload(fileList: File[]) {
    const files = new Array<File>();

    let maxFilesSlots = this.countRemainingSlots;

    for (const file of fileList) {
      const validationResult = this.validate(file, this._files, maxFilesSlots);
      if (validationResult.error) {
        this.showError(validationResult.error);
        this.validationError = validationResult.error;

        this.fileValidationError.emit({
          file: file,
          type: validationResult.error,
        });
      } else {
        files.push(file);
      }

      if (maxFilesSlots === 0) {
        break;
      }

      maxFilesSlots--;
    }

    this.fileElement.value = '';

    return files;
  }

  private validate(
    file: File,
    alreadySelectedFiles: FileHolder[],
    maxFileSlots: number,
  ) {
    const result: {
      error: UploadValidationErrors;
    } = {
      error: null,
    };

    if (maxFileSlots === 0) {
      result.error = 'maxFileCountReached';
    } else if (
      alreadySelectedFiles.find(
        (fh) => fh.file.name === file.name && fh.file.size === file.size,
      )
    ) {
      result.error = 'fileAlreadySelected';
    } else if (file.size > this.options.maxFileSize) {
      result.error = 'fileTooLarge';
    } else if (
      this.options.allowedMimeType?.length &&
      !this.options.allowedMimeType.includes(file.type)
    ) {
      result.error = 'unsupportedFileType';
    }

    return result;
  }

  private showError(type: UploadValidationErrors) {
    if (type === 'fileAlreadySelected') {
      this.finToastService.show(
        Toast.error(
          $localize`:@@fileUploader.toast.error.fileAlreadySelected:Datei bereits ausgewählt.`,
        ),
      );
    }

    if (type === 'maxFileCountReached') {
      this.finToastService.show(
        Toast.error(
          `${this.toastErrorMaxFileCountReachedPrefix} ${this.options.maxFilesCount} ${this.toastErrorMaxFileCountReachedSuffix}`,
        ),
      );
    }

    if (type === 'unsupportedFileType') {
      this.finToastService.show(
        Toast.error(
          `${
            this.toastErrorUnsupportedFileTypePrefix
          } ${this.options.allowedMimeType.join(', ')}.`,
        ),
      );
    }

    if (type === 'fileTooLarge') {
      // eslint-disable-next-line no-console
      console.log('file exceeded size');
    }
  }

  private loadFile(fh: FileHolder): Promise<string> {
    return this.fileService.blobToDataURL(fh.file);
  }

  private uploadSingleFile(fileHolder: FileHolder) {
    fileHolder.upload(
      () => {
        this.markAsFinished(fileHolder);

        if (this.options.removeAfterUpload) {
          this.removeFile(fileHolder.file.name);
        }
      },
      () => {
        // This is intentional
      },
    );
  }

  private markAsFinished(fileHolder: FileHolder) {
    this.fileUploaded.emit(fileHolder);
    if (this.pendingFilesCounter === 0) {
      this.uploadFinished.emit(this._files);
    }
  }
}
