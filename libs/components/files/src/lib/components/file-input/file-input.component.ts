import { FileService } from '@fincloud/core/files';
import {
  makeControlValidatorProvider,
  makeControlValueAccessorProvider,
} from '@fincloud/core/form';

import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  Output,
  ViewChild,
} from '@angular/core';
import { TextInputComponent } from '@fincloud/components/text';
import { take } from 'rxjs';
import { FileUploadBase } from '../../directives/file-upload-base.directive';
import { FileInfo } from '../../models/file';
import { FileHolder } from '../../models/file-holder';
import { FileValidationError } from '../../models/file-validation-error';
import { FileUploaderComponent } from '../file-uploader/file-uploader.component';

@Component({
  selector: 'ui-file-input',
  templateUrl: './file-input.component.html',
  styleUrls: ['./file-input.component.scss'],
  providers: [
    makeControlValueAccessorProvider(FileInputComponent),
    makeControlValidatorProvider(FileInputComponent),
  ],
})
export class FileInputComponent
  extends FileUploadBase
  implements AfterViewInit
{
  @ViewChild(FileUploaderComponent) fileUploader: FileUploaderComponent;

  @ViewChild(TextInputComponent) textInput: TextInputComponent;

  @Input()
  isDisabled = false;
  @Input()
  isFileDownloadable = false;

  @Output() validationErrorType = new EventEmitter<string>();

  public file: FileHolder;
  public fileUploaded = new EventEmitter<boolean>();

  constructor(
    changeDetectorRef: ChangeDetectorRef,
    private fileService: FileService,
  ) {
    super(changeDetectorRef);
  }

  ngAfterViewInit() {
    super.ngAfterViewInit();

    this.fileUploader.options.maxFilesCount = 1;
    this.fileUploader.options.replaceFiles = true;

    this.fileUploader.fileUploaded
      .pipe(take(1))
      .subscribe(() => this.fileUploaded.emit(true));
  }

  public handleValidationError(validationError: FileValidationError) {
    if (validationError.type === 'fileTooLarge') {
      this.formControl.setErrors({
        maxFileSize: true,
      });
      this.formControl.markAsTouched();
    }
    this.validationErrorType.emit(validationError.type);
  }

  public chooseFile() {
    if (!this.isDisabled) {
      this.fileUploader.chooseFile();
    }
  }

  public clear() {
    this.fileUploader.removeAll();
  }

  public downloadFile() {
    if (this.file) {
      this.fileService.downloadBinary(
        this.file.file,
        this.file.file.name,
        this.file.file.type,
      );
    }
  }

  protected fileRemoved(_fileHolder: FileHolder) {
    this.value = null;
    this.file = null;
  }

  protected fileSelected(files: FileList) {
    super.fileSelected(files);

    this.file = this.fileUploader.files[0];
  }

  get fileName() {
    if (this.value && (<FileInfo>this.value).fileName) {
      return (<FileInfo>this.value).fileName;
    } else {
      return null;
    }
  }
}
