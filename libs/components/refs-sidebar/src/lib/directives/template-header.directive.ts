import { Directive, Input } from '@angular/core';
import { CtxTemplateDirective } from '@fincloud/types/models';

@Directive({
  selector: 'ng-template[appTemplateHeader]',
})
export class TemplateHeaderDirective<T> {
  @Input('appTemplateHeader') data: T[] | '';

  static ngTemplateContextGuard<T>(
    directive: TemplateHeaderDirective<T>,
    context: unknown,
  ): context is CtxTemplateDirective<T> {
    return true;
  }
}
