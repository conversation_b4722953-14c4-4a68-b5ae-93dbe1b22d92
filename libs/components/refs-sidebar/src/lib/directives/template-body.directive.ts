import { Directive, Input } from '@angular/core';
import { CtxTemplateDirective } from '@fincloud/types/models';

@Directive({
  selector: 'ng-template[appTemplateBody]',
})
export class TemplateBodyDirective<T> {
  @Input('appTemplateBody') data: T[] | '';

  static ngTemplateContextGuard<T>(
    directive: TemplateBodyDirective<T>,
    context: unknown,
  ): context is CtxTemplateDirective<T> {
    return true;
  }
}
