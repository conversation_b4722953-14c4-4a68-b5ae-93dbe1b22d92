<div class="cropper-container">
  @if (isImageLoaded && showTooltipMessage) {
    <div class="message-container">
      <ui-icon name="svgAdjustProfilePictureIcon"></ui-icon>
      <span
        class="message"
        i18n="@@imageCropper.adjustProfilePhoto.message.tooltip"
      >
        <PERSON><PERSON>hen zum Bewegen
      </span>
    </div>
  }
  <angular-cropper
    (ready)="imageLoaded()"
    (pointerdown)="showTooltipMessage = false"
    [cropperOptions]="cropperOptions"
  ></angular-cropper>

  @if (isImageLoaded) {
    <div class="file">
      <div class="name">{{ file?.name }}</div>
      <ui-tooltip
        i18n-text="@@userSettings.deletePhoto.tooltip"
        text="Foto löschen"
        placement="top"
        [closeDelay]="0"
        [openDelay]="300"
      >
        <ui-icon name="close" size="medium" (click)="removePhoto()"></ui-icon>
      </ui-tooltip>
    </div>
    <div class="slider">
      <span class="sign" (click)="updateZoom(currentScale - 10)">-</span>
      <ui-basic-slider
        [floor]="0"
        [ceil]="100"
        [value]="currentScale"
        (valueChanged)="updateZoom($event)"
      ></ui-basic-slider>
      <span class="sign" (click)="updateZoom(currentScale + 10)">+</span>
    </div>
  }
</div>
