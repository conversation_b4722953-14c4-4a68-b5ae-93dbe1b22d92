import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { CropperComponent } from 'angular-cropperjs';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BasicSliderComponent } from '@fincloud/components/slider';
import { debounceTime, Subject } from 'rxjs';
import { CROPPER_CONFIG_OPTIONS } from '../../utils/cropper-config-options';

@Component({
  selector: 'ui-image-cropper-viewer',
  templateUrl: './image-cropper-viewer.component.html',
  styleUrls: ['./image-cropper-viewer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ImageCropperComponent implements OnInit, OnChanges {
  @Input() file: File;

  @Output() cropperImageLoaded = new EventEmitter();
  @Output() cropperImageCropped = new EventEmitter<Blob>();
  @Output() cropperImageReset = new EventEmitter();

  @ViewChild(BasicSliderComponent) slider: BasicSliderComponent;
  @ViewChild(CropperComponent) public cropperJsComponent: CropperComponent;

  cropperOptions: unknown;
  showTooltipMessage = true;

  isImageLoaded: boolean;
  currentScale = 0;
  private cropEventSubject$$ = new Subject();
  cropEventHandler$ = this.cropEventSubject$$.asObservable();

  constructor(
    private destroyRef: DestroyRef,
    private sanitizer: DomSanitizer,
  ) {}

  ngOnInit(): void {
    this.cropperOptions = {
      ...CROPPER_CONFIG_OPTIONS,
      crop: this.cropEmitter.bind(this),
    };

    this.cropEventHandler$
      .pipe(debounceTime(400), takeUntilDestroyed(this.destroyRef))
      .subscribe(() => this.cropEventHandler());
  }

  cropEmitter() {
    this.cropEventSubject$$.next(true);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.file && !changes.file.firstChange && this.file) {
      this.cropperJsComponent.imageUrl = this.sanitizer.bypassSecurityTrustUrl(
        URL.createObjectURL(this.file),
      );
    }
  }

  imageCropped(blob: Blob) {
    if (!blob) {
      return;
    }

    this.cropperImageCropped.emit(blob);
  }

  imageLoaded() {
    this.cropperImageLoaded.emit();
    this.showTooltipMessage = true;
    this.isImageLoaded = true;

    const imgLoaded = document.querySelector('.cropper-canvas').firstChild;

    const isImgRatioHorizontal =
      (imgLoaded as HTMLElement).offsetWidth /
        (imgLoaded as HTMLElement).offsetHeight >
      3 / 2;

    const cropperDiameter = isImgRatioHorizontal
      ? (imgLoaded as HTMLElement).offsetHeight - 5
      : 255;
    const leftCropperPadding = isImgRatioHorizontal
      ? (400 - (imgLoaded as HTMLElement).offsetHeight) / 2
      : 72.5;
    const topCropperPadding = 3;
    this.cropperJsComponent.cropper.setCropBoxData({
      top: topCropperPadding,
      left: leftCropperPadding,
      width: cropperDiameter,
      height: cropperDiameter,
    });

    if (!isImgRatioHorizontal) {
      this.cropperJsComponent.cropper.setCanvasData({
        left: (400 - (imgLoaded as HTMLElement).offsetWidth) / 2,
        height: 260,
      });
    }
  }

  removePhoto() {
    this.file = null;
    this.isImageLoaded = false;
    this.cropperJsComponent.imageUrl = '';
    this.showTooltipMessage = false;
    this.cropperJsComponent.cropper.clear();
    this.cropperJsComponent.cropper.destroy();
    this.cropperJsComponent.cropper.reset();
    this.currentScale = 0;
    this.cropperImageReset.emit();
  }

  loadImageFailed() {
    // show message
  }

  updateZoom(scaleUpdated: number) {
    this.cropperJsComponent.cropper.zoom(
      (scaleUpdated - this.currentScale) / 50,
    );
    this.cropperJsComponent.cropper.crop();
    this.currentScale = scaleUpdated;
  }

  private cropEventHandler() {
    this.cropperJsComponent.cropper.getCroppedCanvas()?.toBlob(
      (blob) => {
        this.imageCropped(blob);
      },
      'image/png',
      1,
    );
  }
}
