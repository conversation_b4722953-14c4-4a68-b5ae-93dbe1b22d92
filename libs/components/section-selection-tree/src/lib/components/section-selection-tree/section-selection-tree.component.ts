import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import { Store } from '@ngrx/store';

import { selectEditTemplateMode } from '@fincloud/state/business-case';
import {
  StateLibBusinessCaseRealEstatePageActions,
  StateLibScrollToSectionPageActions,
} from '@fincloud/state/business-case-real-estate';
import {
  IconName,
  Section,
  SectionSelectionTreeField,
  SubSection,
} from '@fincloud/types/models';
import { REFS_BLOCKS_GROUP_KEY } from '@fincloud/utils';
import { Observable, shareReplay } from 'rxjs';

@Component({
  selector: 'app-section-selection-tree',
  templateUrl: './section-selection-tree.component.html',
  styleUrls: ['./section-selection-tree.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SectionSelectionTreeComponent {
  @Input() section: Section<IconName>;
  @Input() businessCaseId: string;

  @Output() addField = new EventEmitter<{
    field: SectionSelectionTreeField;
    groupId: string;
  }>();
  activeId = '';

  readonly refsBlocksGroupKey = REFS_BLOCKS_GROUP_KEY;
  isEditMode$: Observable<boolean> = this.store
    .select(selectEditTemplateMode)
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));

  constructor(private store: Store) {}

  toggleExpandSubSection(subSection: SubSection) {
    this.activeId = subSection.id;
  }

  trackById(_: number, field: SectionSelectionTreeField | SubSection): string {
    return field.id;
  }

  navigateToElementOnEnable(
    elementName: string,
    subSection?: SubSection,
    mainSection?: Section<IconName>,
    ev?: MouseEvent,
  ) {
    if (ev) {
      ev.stopImmediatePropagation();
      ev.preventDefault();
    }

    if (subSection && mainSection) {
      this.toggleExpandSubSection(subSection);
      return;
    }

    if (
      subSection.name !== elementName &&
      !subSection.fields.find((i) => i.fieldName === elementName)?.isEnabled
    ) {
      return;
    }

    this.store.dispatch(
      StateLibScrollToSectionPageActions.scrollToSection({
        payload: {
          firstId: subSection.id,
          secondId: subSection.isField
            ? ''
            : subSection.fields.find((f) => f.fieldName === elementName)?.id,
          thirdId: subSection.isField
            ? subSection.fields.find((f) => f.fieldName === elementName)?.id
            : '',
        },
      }),
    );
  }

  onFieldToggleEnabled(
    field: SectionSelectionTreeField,
    subSection: SubSection,
  ) {
    field.isEnabled = !field.isEnabled;

    const isNewFieldStateDisabled = !field.isEnabled;
    const areAllSubgroupsDisabled = subSection?.fields?.every(
      (subGroup) => !subGroup.isEnabled,
    );

    this.store.dispatch(
      StateLibBusinessCaseRealEstatePageActions.enableOrDisableGroup({
        businessCaseId: this.businessCaseId,
        body: {
          disabled: isNewFieldStateDisabled,
          groupKey: field.key,
        },
        fieldId: field.id,
        parentGroupId: subSection.id,
        areAllSubgroupsDisabled,
      }),
    );
  }

  onFieldAdd(
    ev: MouseEvent,
    field: SectionSelectionTreeField,
    groupId: string,
  ) {
    ev.stopImmediatePropagation();
    ev.preventDefault();

    this.addField.emit({ field, groupId });
  }

  clearActive(): void {
    this.activeId = '';
  }

  navigateToGroup(firstId: string, secondId: string): void {
    this.store.dispatch(
      StateLibScrollToSectionPageActions.scrollToSection({
        payload: {
          firstId,
          secondId,
          thirdId: '',
        },
      }),
    );
  }
}
