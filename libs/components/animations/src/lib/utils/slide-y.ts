import { animate, animation, keyframes, style } from '@angular/animations';
import { DEFAULT_TIMING } from './defaults';

export function slideY(a: string | number, b: string | number) {
  return animation(
    animate(
      '{{ timing }}ms {{ delay }}ms',
      keyframes([
        style({
          transform: 'translate3d(0, {{ a }}, 0)',
          offset: 0,
        }),
        style({
          transform: 'translate3d(0, {{ b }}, 0)',
          offset: 1,
        }),
      ]),
    ),
    { params: { timing: DEFAULT_TIMING, delay: 0, a, b } },
  );
}
