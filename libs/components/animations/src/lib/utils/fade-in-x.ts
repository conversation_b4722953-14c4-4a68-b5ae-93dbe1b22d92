import { animate, animation, keyframes, style } from '@angular/animations';
import { DEFAULT_TIMING } from './defaults';

export function fadeInX(a: string | number, b: string | number) {
  return animation(
    animate(
      '{{ timing }}ms {{ delay }}ms',
      keyframes([
        style({
          opacity: 0,
          transform: 'translate3d({{ a }}, 0, 0)',
          offset: 0,
        }),
        style({
          opacity: 1,
          transform: 'translate3d({{ b }}, 0, 0)',
          offset: 1,
        }),
      ]),
    ),
    { params: { timing: DEFAULT_TIMING, delay: 0, a, b } },
  );
}
