import { Directive, EventEmitter, Input, Output } from '@angular/core';
import { isEqual } from 'lodash-es';
import { DashboardFilters } from '../models/dashboard-filters';

@Directive()
export class DashboardBase {
  @Input() filters: DashboardFilters;
  @Output() toggleFilters = new EventEmitter();

  defaultFilters: Record<string, unknown>;

  get hasFiltersApplied() {
    return this.filters && !isEqual(this.filters, this.defaultFilters);
  }
}
