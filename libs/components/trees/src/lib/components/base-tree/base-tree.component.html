@if (dataSource) {
  <cdk-tree
    [dataSource]="dataSource"
    class="base-tree-container"
    [treeControl]="treeControl"
  >
    <!-- This is the tree node template for leaf nodes -->
    <cdk-nested-tree-node
      *cdkTreeNodeDef="let node"
      class="base-tree-node leaf"
    >
      <div class="label leaf">
        <ng-container
          [ngTemplateOutlet]="treeLeafNodeTemplate"
          [ngTemplateOutletContext]="{ $implicit: node }"
        ></ng-container>
      </div>
    </cdk-nested-tree-node>
    <!-- This is the tree node template for expandable nodes -->
    <cdk-nested-tree-node
      *cdkTreeNodeDef="let node; when: hasChild"
      class="base-tree-node group"
      [class.expanded]="treeControl.isExpanded(node)"
    >
      <div class="label" cdkTreeNodeToggle>
        <ui-icon
          name="arrow_right"
          class="tree-node-icon"
          size="large-xl"
          [class.rotate]="treeControl.isExpanded(node)"
        >
        </ui-icon>
        <ng-container
          [ngTemplateOutlet]="treeNodeTemplate"
          [ngTemplateOutletContext]="{ $implicit: node }"
        ></ng-container>
      </div>
      <div
        [class.base-tree-invisible]="!treeControl.isExpanded(node)"
        class="nested-nodes-wrapper"
      >
        <ng-container cdkTreeNodeOutlet></ng-container>
      </div>
    </cdk-nested-tree-node>
  </cdk-tree>
}
