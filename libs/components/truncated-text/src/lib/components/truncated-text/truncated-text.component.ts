import {
  AfterContentChecked,
  AfterContentInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  ElementRef,
  HostBinding,
  Input,
  OnInit,
  ViewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, debounceTime, fromEvent, merge, tap } from 'rxjs';

@Component({
  selector: 'ui-truncated-text',
  templateUrl: './truncated-text.component.html',
  styleUrls: ['./truncated-text.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TruncatedTextComponent
  implements OnInit, AfterContentChecked, AfterContentInit
{
  @ViewChild('truncatedContentWrapper') truncatedContentRef: ElementRef;

  @Input() widthCalculationExpression: string;

  @HostBinding('style.max-width') get hostMaxWidth() {
    if (!this.widthCalculationExpression) {
      return '100%';
    }

    return this.widthCalculationExpression;
  }

  isTruncated = false;
  textForToolTip: string;
  ngContentInnerHTML: string;

  private readonly truncateTrigger$$ = new BehaviorSubject(true);

  constructor(
    private destroyRef: DestroyRef,
    private cdr: ChangeDetectorRef,
  ) {}

  ngOnInit() {
    merge(this.truncateTrigger$$, fromEvent(window, 'resize'))
      .pipe(
        debounceTime(400),
        takeUntilDestroyed(this.destroyRef),
        tap(() => {
          this.triggerChangesAfterTruncation();
        }),
      )
      .subscribe();
  }

  ngAfterContentInit() {
    this.truncateTrigger$$.next(true);
  }

  ngAfterContentChecked(): void {
    const innerText = this.truncatedContentRef?.nativeElement?.innerText;
    if (innerText !== this.textForToolTip) {
      this.ngContentInnerHTML = innerText;
      this.triggerChangesAfterTruncation();
    }
  }

  triggerChangesAfterTruncation() {
    this.isTruncated = this.isEllipsisActive(this.truncatedContentRef);
    this.textForToolTip = this.truncatedContentRef?.nativeElement?.innerText;
    this.cdr.detectChanges();
  }

  private isEllipsisActive(e: ElementRef) {
    return e?.nativeElement?.offsetWidth < e?.nativeElement?.scrollWidth;
  }
}
