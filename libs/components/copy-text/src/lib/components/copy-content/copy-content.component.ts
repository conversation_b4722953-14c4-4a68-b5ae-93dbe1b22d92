import { Clipboard } from '@angular/cdk/clipboard';
import {
  ChangeDetectionStrategy,
  Component,
  Input,
  ViewChild,
} from '@angular/core';
import { TooltipComponent } from '@fincloud/components/tooltip';
import { Toast } from '@fincloud/core/toast';
import { setTimeoutUnpatched } from '@fincloud/core/utils';
import { IconName } from '@fincloud/types/models';
import { FinToastService } from '@fincloud/ui/toast';
import { BehaviorSubject, Observable } from 'rxjs';

@Component({
  selector: 'ui-copy-content',
  templateUrl: './copy-content.component.html',
  styleUrls: ['./copy-content.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CopyContentComponent<T> {
  @Input() content: T;
  @Input() closeDelay = 3_000;
  @Input() pendingAttempts = 3;
  //JSON.stringify second parameter
  @Input() replacer: <T, R>(key: string, value: T) => R | Array<string> = null;
  //JSON.stringify third parameter
  @Input() spacer: string | number = null;
  @ViewChild('tooltip') private toolTipComponent: TooltipComponent;

  copyContentIconName: IconName = 'content_copy';
  copyContentDoneIconName: IconName = 'check';

  private copyContentIconName$$ = new BehaviorSubject<IconName>(
    this.copyContentIconName,
  );
  copyContentIconName$: Observable<IconName> = this.copyContentIconName$$;

  constructor(
    private clipboard: Clipboard,
    private finToastService: FinToastService,
  ) {}

  onClick(iconName: IconName): void {
    if (iconName === this.copyContentDoneIconName) {
      return;
    }

    this.copyContent();
    this.toggleTooltip();
  }

  private toggleTooltip(): void {
    this.toolTipComponent.toggleTooltip();
    this.copyContentIconName$$.next(this.copyContentDoneIconName);

    setTimeoutUnpatched(() => {
      this.toolTipComponent.toggleTooltip();
      this.copyContentIconName$$.next(this.copyContentIconName);
    }, this.closeDelay);
  }

  private copyContent(): void {
    const content =
      typeof this.content === 'string'
        ? this.content
        : this.tryStringify(this.content);
    const pending = this.clipboard.beginCopy(content);
    let remainingAttempts = this.pendingAttempts;
    const attempt = () => {
      const result = pending.copy();
      if (!result && --remainingAttempts > 0) {
        setTimeoutUnpatched(attempt, 0);
      } else {
        pending.destroy();
      }
    };
    attempt();
  }

  private tryStringify(content: T): string {
    let stringifiedContent = '';
    try {
      stringifiedContent = JSON.stringify(content, this.replacer, this.spacer);
    } catch {
      this.finToastService.show(Toast.error());
    }

    return stringifiedContent;
  }
}
