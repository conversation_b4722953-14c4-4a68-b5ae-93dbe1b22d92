import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Inject,
  Input,
  LOCALE_ID,
  OnInit,
  Output,
} from '@angular/core';
import { WindowRef } from '@fincloud/core/services';
import { Locale } from '@fincloud/types/enums';
import { DocumentPreviewState } from '@fincloud/types/models';
import {
  LinkTarget,
  PageViewModeType,
  PdfLoadedEvent,
  VerbosityLevel,
  pdfDefaultOptions,
} from 'ngx-extended-pdf-viewer';

@Component({
  selector: 'ui-pdf-viewer',
  templateUrl: './pdf-viewer.component.html',
  styleUrls: ['./pdf-viewer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PdfViewerComponent implements OnInit {
  @Input()
  src: string | ArrayBuffer | Blob | Uint8Array | URL;

  @Input()
  fileName: string;

  @Input()
  logLevel: VerbosityLevel = VerbosityLevel.ERRORS;

  @Input()
  pageViewMode: PageViewModeType = 'multiple';

  @Input()
  initialZoom: string | number | undefined = 'auto';

  @Input()
  showToolbar = true;

  @Input()
  showSecondaryToolbarButton = false;

  @Input()
  showSidebarButton = true;

  @Input()
  showFindButton = true;

  @Input()
  showPagingButtons = true;

  @Input()
  showZoomButtons = true;

  @Input()
  showOpenFileButton = false;

  @Input()
  showPrintButton = true;

  @Input()
  showDownloadButton = false;

  @Input()
  contextMenuAllowed = false;

  @Input()
  replaceBrowserPrint = true;

  @Input()
  showHandToolButton = false;

  @Input()
  showScrollingButton = true;

  @Input()
  showPresentationModeButton = false;

  @Input()
  showRotateButton = true;

  @Input()
  showSpreadButton = true;

  @Input()
  showPropertiesButton = true;

  @Input()
  showDrawEditor = false;

  @Input()
  showTextEditor = false;

  @Input()
  showStampEditor = false;

  @Input()
  set page(page) {
    if (page) {
      this.pageNumber = page;
    }
  }

  get page() {
    return this.pageNumber;
  }

  @Output() pdfLoaded = new EventEmitter<PdfLoadedEvent>();

  private pageNumber: Partial<DocumentPreviewState> = { pageNumber: 0 };

  constructor(
    @Inject(LOCALE_ID) public locale: Locale,
    private windowRef: WindowRef,
  ) {}

  ngOnInit(): void {
    if (this.pageViewMode === 'book') {
      this.initialZoom = 'page-fit';
    }

    pdfDefaultOptions.externalLinkTarget = LinkTarget.BLANK;
    this.suppressNgxConsoleFilterLogs();
  }
  onPdfLoaded(pdfLoaded: PdfLoadedEvent) {
    this.pdfLoaded.emit(pdfLoaded);
  }

  private suppressNgxConsoleFilterLogs() {
    (
      this.windowRef.nativeWindow as unknown as {
        ngxConsoleFilter: () => boolean;
      }
    ).ngxConsoleFilter = () => false;
  }
}
