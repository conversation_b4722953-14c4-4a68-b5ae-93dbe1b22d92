import { ColumnSortedData } from '@fincloud/core/business-case';

import {
  CellRange,
  ColDef,
  Column,
  ColumnA<PERSON>,
  ColumnPinnedType,
  EditableCallbackParams,
  FillOperationParams,
  GridApi,
  GridOptions,
  IRowNode,
} from '@ag-grid-community/core';
import { cloneDeep, isEmpty, isEqual } from 'lodash-es';
// import { DataGridColumnHeaderComponent } from '../components/data-grid-column-header/data-grid-column-header.component';
import { IdentityService } from '@fincloud/core/services';
import { ObjectHelper } from '@fincloud/utils';
import { DataGridColumnHeaderComponent } from '../components/data-grid-column-header/data-grid-column-header.component';
import { DataGridViewMode } from '../enums/data-grid-view-mode';
import { buildDefault } from './build-default';
import { EMPTY_CELL_DATA } from './empty-cell-data';
import { generateColumnNameFromNumber } from './generate-column-name-from-number';
import { ROW_NUMBER_COLUMN } from './row-number-column';

// import { getEmptyColumn } from './get-empty-column';
// import { DataGridCoreService } from '../services/data-grid-core.service';

export class DataGridConfig implements GridOptions {
  version = '1';
  rowHeight = 36;
  maxVisibleRows = 10;
  revisionMaxVisibleRows = 5;
  headerHeight = 24;
  undoRedoCellEditing = true;
  undoRedoCellEditingLimit = 100;
  theme = 'ag-theme-alpine';
  columnDefs: ColDef[];
  internalOnlyColumnDefs: ColDef[] = [
    {
      headerName: '',
      width: 28,
      maxWidth: 28,
      field: ROW_NUMBER_COLUMN,
      editable: false,
      headerClass: 'row-number-column-header',
      cellClass: 'row-number-column',
      suppressFillHandle: true,
      valueGetter: (params) => {
        return params.node.rowIndex + 1;
      },
      pinned: 'left',
      lockPosition: 'left',

      onCellClicked: (e) => {
        this.setRowSelected(e.rowIndex, e.columnApi, e.api);
      },
    },
  ];
  enableFillHandle = true;

  enterNavigatesVertically = true;
  enterNavigatesVerticallyAfterEdit = true;

  rowDragManaged = true;

  rowData: Record<string, unknown>[] = [];
  localeText: { [key: string]: string };

  get workingColumnDefs() {
    return this.columnDefs.filter(
      (c) => !this.internalOnlyColumnDefs.some((i) => i.field === c.field),
    );
  }

  /* Row data containing only actual data, excluding id and other unnecessary stuff */
  get workingRowData() {
    const workingRowData = this.rowData.map((r) => {
      const reduced = Object.keys(r)
        .filter((k) => this.workingColumnDefs.some((c) => c.field === k))
        .reduce(
          (previous, k) => {
            return {
              ...previous,
              [k]: r[k],
            };
          },
          <Record<string, unknown>>{},
        );
      return reduced;
    });

    return workingRowData;
  }

  persistedColDefProperties = [
    'field',
    'headerName',
    'sort',
    'pinned',
    'width',
    'maxWidth',
    'cellDataType',
    'valueGetter',
    'valueSetter',
    'valueParser',
    'valueFormatter',
    'cellRenderer',
    'headerClass',
    'type',
  ];

  filterData: { [key: string]: Record<string, unknown> } = {};

  enableRangeSelection = true;

  defaultColDef: ColDef = {
    flex: 1,
    editable: false,
    minWidth: 200,
    useValueFormatterForExport: true,
    headerComponent: DataGridColumnHeaderComponent,
  };

  pinnedTopRow: Record<string, string>;
  pinnedBottomRow: Record<string, string>;

  get revisionTableHeight() {
    const visibleRowCount = Math.min(
      this.revisionMaxVisibleRows,
      this.rowData?.length,
    );
    const verticalScrollbarFix = 1;
    const rowsWithHeader = visibleRowCount + 1;
    return `${rowsWithHeader * this.rowHeight + verticalScrollbarFix}px`;
  }

  get tableHeight() {
    const visibleRowCount = Math.min(this.maxVisibleRows, this.rowData?.length);
    const verticalScrollbarFix = 1;
    const rowsWithHeader = visibleRowCount + 1;
    return `${rowsWithHeader * this.rowHeight + verticalScrollbarFix}px`;
  }

  static buildFromInformationValue(fieldInformationValue: DataGridConfig) {
    if (!fieldInformationValue) {
      fieldInformationValue = buildDefault({
        columnSize: 2,
        label: '',
        rowSize: 2,
        enableRowNumberColumn: true,
      });
    }

    // Check if any old custom column header names have been set and move them to a row
    if (fieldInformationValue) {
      fieldInformationValue = cloneDeep(fieldInformationValue);

      let hasOldHeaderNames = false;
      fieldInformationValue.columnDefs.forEach((cd, index) => {
        const generatedColumnName = generateColumnNameFromNumber(index);

        if (!isEmpty(cd.headerName) && cd.headerName !== generatedColumnName) {
          hasOldHeaderNames = true;
        }
      });

      if (hasOldHeaderNames) {
        const row = fieldInformationValue.columnDefs.reduce(
          (acc, cur) => {
            const newValue = cloneDeep(EMPTY_CELL_DATA);
            newValue.value = cur.headerName;

            acc[cur.field] = newValue;

            return acc;
          },
          {} as Record<string, unknown>,
        );

        row.id = IdentityService.generateId();

        fieldInformationValue.rowData.unshift(row);
        fieldInformationValue.columnDefs.map((cd) => (cd.headerName = ''));
      }
    }

    const config = ObjectHelper.mergeDeep(
      new DataGridConfig(),
      fieldInformationValue,
    );

    return config;
  }

  // static getEmptyColumn(colIndex: number = null): ColDef {
  //   return {
  //     field: IdentityService.generateId(),
  //     flex: 1,
  //     headerName: colIndex
  //       ? DataGridCoreService.generateColumnNameFromNumber(colIndex)
  //       : '',
  //     pinned: null,
  //     sort: null,
  //   };
  // }

  setViewMode(dataGridViewMode: DataGridViewMode) {
    switch (dataGridViewMode) {
      case DataGridViewMode.READ_ONLY:
        this.workingColumnDefs.forEach((c) => {
          this.setColumnAsReadOnly(c);
        });
        break;
      case DataGridViewMode.SESSION_ONLY_EDIT:
        this.workingColumnDefs.forEach((c) => {
          this.setColumnAsSessionOnlyEdit(c);
        });

        break;
      case DataGridViewMode.PERSISTENT_EDIT:
        this.workingColumnDefs.forEach((c) => {
          this.setColumnAsPersistentEdit(c);
        });
        break;
    }

    return this;
  }

  setColumnAsReadOnly(colDef: ColDef) {
    colDef.sortable = false;
    colDef.filter = false;
    colDef.editable = false;
    colDef.resizable = false;
    colDef.autoHeaderHeight = true;
    colDef.menuTabs = [];
    colDef.suppressMenu = true;
    colDef.suppressMovable = true;
    colDef.headerComponentParams = { viewMode: DataGridViewMode.READ_ONLY };
    colDef.flex = 1;
    colDef.type = null;
  }

  setColumnAsSessionOnlyEdit(colDef: ColDef) {
    colDef.sortable = true;
    colDef.filter = true;
    colDef.filterParams = { buttons: ['reset', 'apply'], closeOnApply: true };
    colDef.editable = false;
    colDef.resizable = true;
    colDef.autoHeaderHeight = true;
    colDef.menuTabs = ['filterMenuTab'];
    colDef.suppressMenu = false;
    colDef.headerComponentParams = {
      viewMode: DataGridViewMode.SESSION_ONLY_EDIT,
    };
    colDef.flex = 1;
    colDef.type = null;
  }

  setColumnAsPersistentEdit(colDef: ColDef) {
    colDef.sortable = true;
    colDef.filter = true;
    colDef.filterParams = { buttons: ['reset', 'apply'], closeOnApply: true };
    colDef.editable = (params: EditableCallbackParams) => {
      const cellData = params.data[params.colDef.field];
      const cellDataType = cellData?.formatting?.dataType?.toLowerCase();
      return cellDataType !== 'checkbox';
    };
    colDef.resizable = true;
    colDef.autoHeaderHeight = true;
    colDef.menuTabs = ['filterMenuTab'];
    colDef.suppressMenu = false;
    colDef.headerComponentParams = {
      viewMode: DataGridViewMode.PERSISTENT_EDIT,
    };
    colDef.flex = 1;
    colDef.type = null;
  }

  fillOperation = (params: FillOperationParams) => {
    return params.rowNode.data[params.column.getColId()].value;
  };
  /**
   * Build a DTO object to save in DB, keep only what makes sense to be persisted
   */
  toDto() {
    const dto = {
      version: this.version,
      columnDefs: cloneDeep(this.workingColumnDefs).map((colDef) => {
        for (const key in colDef) {
          if (!this.persistedColDefProperties.includes(key)) {
            delete colDef[key as keyof ColDef];
          }
        }
        return colDef;
      }),
      rowData: this.rowData,
      filterData: this.filterData,
      pinnedBottomRow: this.pinnedBottomRow,
      pinnedTopRow: this.pinnedTopRow,
    };
    return dto;
  }

  pinColumn(column: Column, pinned: ColumnPinnedType) {
    const colDef = this.columnDefs.find((def) => def.field === column.getId());
    colDef.pinned = pinned;
    colDef.lockPosition = pinned ?? false;
  }

  sortColumn(data: ColumnSortedData) {
    this.columnDefs = this.columnDefs.map((col) => {
      if (col.field === data.colDef.field) {
        return { ...col, sort: data.sort };
      }

      return { ...col, sort: null };
    });
  }

  storeFilterData(data: { [key: string]: Record<string, unknown> }) {
    this.filterData = data;
  }

  isFilterDataNew(data: { [key: string]: Record<string, unknown> }): boolean {
    return !!this.filterData && !isEqual(data, this.filterData);
  }

  deleteColumn(column: Column) {
    this.columnDefs = this.columnDefs.filter(
      (col) => col.field !== column.getId(),
    );
    this.rowData.forEach((row) => delete row[column.getId()]);
  }

  deleteRow(row: IRowNode) {
    this.rowData.splice(row.rowIndex, 1);
  }

  dragRow(row: Record<string, string>, newIndex: number) {
    this.rowData = this.rowData.filter((r) => r.id !== row.id);
    this.rowData.splice(newIndex, 0, row);
  }

  pinRow(rowData: Record<string, string>, position: 'top' | 'bottom') {
    position === 'top'
      ? (this.pinnedTopRow = rowData)
      : (this.pinnedBottomRow = rowData);
  }

  unpinRow(pinnedRowPosition: 'top' | 'bottom') {
    pinnedRowPosition === 'top'
      ? (this.pinnedTopRow = null)
      : (this.pinnedBottomRow = null);
  }

  moveColumn(colDef: ColDef, newIndex: number) {
    this.columnDefs = this.columnDefs.filter(
      (col) => col.field !== colDef?.field,
    );
    this.columnDefs.splice(newIndex, 0, colDef);
    this.columnDefs = this.columnDefs.filter((col) => col !== undefined);
  }

  changePinnedColumnHeader(colDef: ColDef, pinned: ColumnPinnedType) {
    const pinnedColumn = this.columnDefs.find(
      (col) => col.field === colDef.field,
    );
    pinnedColumn.headerClass = pinned ? 'pinned-column-header' : null;
  }

  setRowSelected(rowIndex: number, columnApi: ColumnApi, gridApi: GridApi) {
    const cellRanges = gridApi.getCellRanges();
    const tempRange = cellRanges.filter((tr) => {
      return !(
        tr.columns.length === 1 &&
        tr.columns[0].getColId() === ROW_NUMBER_COLUMN
      );
    });

    gridApi.clearRangeSelection();

    tempRange.forEach((tr) => {
      gridApi.addCellRange({
        columnStart: tr.columns[0],
        columnEnd: tr.columns[tr.columns.length - 1],
        rowStartIndex: tr.startRow.rowIndex,
        rowEndIndex: tr.endRow.rowIndex,
      });
    });

    const allDisplayedColumns = columnApi.getAllDisplayedColumns();

    gridApi.addCellRange({
      columnStart: allDisplayedColumns[1],
      columnEnd: allDisplayedColumns[allDisplayedColumns.length - 1],
      rowStartIndex: rowIndex,
      rowEndIndex: rowIndex,
    });
  }

  determineSelectionType(selection: CellRange[]): 'column' | 'row' | 'range' {
    if (selection[0]?.columns[0]?.getColId() === ROW_NUMBER_COLUMN) {
      return 'row';
    }

    if (
      selection.length === 1 &&
      selection[0].startRow.rowIndex === selection[0].endRow.rowIndex &&
      selection[0].columns.length === this.workingColumnDefs.length
    ) {
      return 'row';
    }

    if (
      selection.length === 1 &&
      selection[0].columns.length === 1 &&
      selection[0].startRow.rowIndex === 0 &&
      selection[0].endRow.rowIndex === this.rowData.length - 1
    ) {
      return 'column';
    }

    return 'range';
  }
}
