import { IdentityService } from '@fincloud/core/services';
import { cloneDeep } from 'lodash-es';
import { TableCreateConfig } from '../models/table-create-config';
import { DataGridConfig } from './data-grid';
import { EMPTY_CELL_DATA } from './empty-cell-data';
import { getEmptyColumn } from './get-empty-column';

export const buildDefault = (tableCreateConfig: TableCreateConfig) => {
  const config: DataGridConfig = new DataGridConfig();

  config.columnDefs = Array.from({ length: tableCreateConfig.columnSize }, () =>
    getEmptyColumn(),
  );

  const rowData: Record<string, unknown>[] = [];
  for (let row = 0; row < tableCreateConfig.rowSize; row++) {
    const cellsValue = config.columnDefs.reduce(
      (acc, cur) => {
        acc[cur.field] = cloneDeep(EMPTY_CELL_DATA);

        return acc;
      },
      {} as Record<string, unknown>,
    );

    cellsValue.id = IdentityService.generateId();
    rowData.push(cellsValue);
  }

  config.rowData = rowData;

  return config;
};
