import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ValueGetterParams,
  ValueSetterParams,
} from '@ag-grid-community/core';
import { Injectable } from '@angular/core';

import { ColumnData } from '@fincloud/core/business-case';
import { cloneDeep, isEmpty, isString } from 'lodash-es';

import { DataGridCompositeCellComponent } from '../components/data-grid-composite-cell/data-grid-composite-cell.component';
import { DataGridConfig } from '../utils/data-grid';
import { EMPTY_CELL_DATA } from '../utils/empty-cell-data';
import { generateColumnNameFromNumber } from '../utils/generate-column-name-from-number';
import { getEmptyColumn } from '../utils/get-empty-column';

@Injectable({
  providedIn: 'root',
})
export class DataGridColumnService {
  setColDefHeaderNames(config: DataGridConfig) {
    config.workingColumnDefs.forEach((c, i) => {
      c.headerName = generateColumnNameFromNumber(i);
      c.autoHeight = true;
    });
  }

  setColDefParsers(config: DataGridConfig) {
    config.workingColumnDefs.forEach((c) => {
      c.editable = true;

      c.cellRenderer = DataGridCompositeCellComponent;
      c.valueGetter = (params) => {
        this.checkAndFixOldFormatData(params);

        return params.data[params.colDef.field]?.value;
      };

      c.valueSetter = (params) => {
        this.checkAndFixOldFormatData(params);

        params.data[params.colDef.field]['value'] = params?.newValue;
        return true;
      };
    });
  }

  setColumnSelected(gridApi: GridApi, ctrlSelected: boolean, data: ColumnData) {
    if (!ctrlSelected) {
      gridApi.clearRangeSelection();
    }

    const displayedRowCount = gridApi.getDisplayedRowCount();
    const firstRowIndex = 0;
    const lastRowIndex = displayedRowCount - 1;

    gridApi?.addCellRange({
      columnStart: data.colDef.field,
      columnEnd: data.colDef.field,
      rowStartIndex: firstRowIndex,
      rowEndIndex: lastRowIndex,
    });

    gridApi.setFocusedCell(firstRowIndex, data.colDef.field);
  }

  addEmptyColumn(config: DataGridConfig, afterColumn: Column) {
    const idx = config.columnDefs.findIndex(
      (def) => def.field === afterColumn.getId(),
    );
    const emptyColumn = getEmptyColumn(idx);
    config.columnDefs.splice(idx + 1, 0, emptyColumn);
    config.rowData.forEach(
      (row) => (row[emptyColumn.field] = cloneDeep(EMPTY_CELL_DATA)),
    );

    this.setColDefHeaderNames(config);
    this.setColDefParsers(config);
  }

  checkAndFixOldFormatData(params: ValueGetterParams | ValueSetterParams) {
    const field = params?.data[params.colDef.field];
    if (isString(field)) {
      const updatedField = cloneDeep(EMPTY_CELL_DATA);
      if (!isEmpty(field)) {
        updatedField.value = field;
      }

      params.data[params.colDef.field] = updatedField;
    }
  }
}
