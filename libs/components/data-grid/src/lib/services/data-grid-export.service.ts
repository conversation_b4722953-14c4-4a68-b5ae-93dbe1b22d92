import { Injectable } from '@angular/core';
import { DataGridConfig } from '../utils/data-grid';

import {
  CsvExportParams,
  ExcelExportParams,
  GridApi,
} from '@ag-grid-community/core';
import { ExportType } from '../enums/export-type';
import { ExportOptions } from '../models/export-options';

@Injectable({
  providedIn: 'root',
})
export class DataGridExportService {
  export(
    exportOptions: ExportOptions,
    config: DataGridConfig,
    gridApi: GridApi,
    exportName: string,
  ) {
    if (exportOptions.type === ExportType.CSV) {
      return this.exportCsv(config, gridApi, exportName);
    }

    if (exportOptions.type === ExportType.EXCEL) {
      return this.exportExcel(config, gridApi, exportName);
    }
  }

  private getExportConfig(config: DataGridConfig) {
    const exportColumns = config.columnDefs.map((c) => c.field);
    exportColumns.shift();

    const c: ExcelExportParams & CsvExportParams = {
      columnKeys: exportColumns,
      skipColumnHeaders: true,
      processCellCallback: (params) => {
        return params.value;
      },
    };

    return c;
  }

  private exportCsv(
    config: DataGridConfig,
    gridApi: GridApi,
    exportName: string,
  ) {
    const exportConfig = this.getExportConfig(config);
    exportConfig.fileName = `${exportName}.csv`;

    gridApi.exportDataAsCsv(exportConfig);
  }

  private exportExcel(
    config: DataGridConfig,
    gridApi: GridApi,
    exportName: string,
  ) {
    const exportConfig = this.getExportConfig(config);
    exportConfig.fileName = `${exportName}.xlsx`;

    gridApi.exportDataAsExcel(exportConfig);
  }
}
