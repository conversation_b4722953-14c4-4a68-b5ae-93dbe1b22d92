import {
  CellRange as AgGrid<PERSON><PERSON><PERSON>ange,
  Column,
  GridApi,
} from '@ag-grid-community/core';
import { Injectable } from '@angular/core';
import { ObjectHelper } from '@fincloud/utils';
import { has, isEmpty, isEqual, max, mean, min, sum } from 'lodash-es';
import { DataGridViewMode } from '../enums/data-grid-view-mode';
import { CellData } from '../models/cell-data';
import { CellFormatting } from '../models/cell-formatting';
import { CellRange } from '../models/cell-range';
import { ICellRendererParamsWithFormatting } from '../models/icell-renderer-params-with-formatting';
import { SummaryMetricFunctions } from '../models/summary-metric-functions';
import { DataGridConfig } from '../utils/data-grid';
import { DEFAULT_CELL_FORMATTING } from '../utils/default-cell-formatting';
import { DataGridColumnService } from './data-grid-column.service';
import { FormulaEngineService } from './formula-engine.service';

@Injectable({
  providedIn: 'root',
})
export class DataGridCoreService {
  constructor(
    private dataGridColumnHelperService: DataGridColumnService,
    private formulaEngine: FormulaEngineService,
  ) {}

  refreshViewMode(
    dataGridViewMode: DataGridViewMode,
    config: DataGridConfig,
    gridApi: GridApi,
  ) {
    config?.setViewMode(dataGridViewMode);

    if (gridApi) {
      gridApi?.setFilterModel(config?.filterData);
      gridApi?.setColumnDefs(config?.columnDefs);
      gridApi?.setRowData(config?.rowData);
      this.setPinnedRowData(config, gridApi);
    }
  }

  setPinnedRowData(config: DataGridConfig, gridApi: GridApi) {
    if (config?.pinnedTopRow) {
      gridApi?.setPinnedTopRowData([config.pinnedTopRow]);
    }

    if (config?.pinnedBottomRow) {
      gridApi?.setPinnedBottomRowData([config.pinnedBottomRow]);
    }
  }

  enrichConfigWithInternals(config: DataGridConfig) {
    this.addInternalOnlyColDefs(config);
    this.dataGridColumnHelperService.setColDefHeaderNames(config);
    this.dataGridColumnHelperService.setColDefParsers(config);
  }

  addInternalOnlyColDefs(config: DataGridConfig) {
    config.internalOnlyColumnDefs.forEach((i) => {
      if (!config.columnDefs.find((c) => c.field === i.field)) {
        config.columnDefs.unshift(i);
      }
    });
  }

  getSummaryMetricCalculations(
    gridApi: GridApi,
    cellRanges: AgGridCellRange[],
  ) {
    const cellData: Array<string> = [];
    cellRanges.forEach((cellRange) => {
      const startRowIndex = Math.min(
        cellRange.startRow.rowIndex,
        cellRange.endRow.rowIndex,
      );
      const endRowIndex = Math.max(
        cellRange.startRow.rowIndex,
        cellRange.endRow.rowIndex,
      );

      for (let i = startRowIndex; i <= endRowIndex; i++) {
        const rowNode = gridApi.getRowNode(i.toString());

        cellRange.columns.forEach((c: Column) => {
          const data = rowNode.data as Record<string, unknown>;

          let cellValue = (data[c.getColId()] as CellData)?.value?.toString();

          if (cellValue?.startsWith('=')) {
            cellValue = this.formulaEngine
              .parseExpression(cellValue)
              ?.result?.toString();
          }
          cellData.push(cellValue);
        });
      }
    });

    const numberValues = cellData
      .filter((c) => ObjectHelper.isNumeric(c))
      .map((c) => parseFloat(c));

    return <SummaryMetricFunctions>{
      average: mean(numberValues),
      min: min(numberValues),
      max: max(numberValues),
      count: cellData.filter((n) => !!n).length,
      sum: sum(numberValues),
    };
  }

  setFormattingOnSelection(
    gridApi: GridApi,
    formatting: CellFormatting,
    cellRanges: CellRange[],
    updatedCallback: () => void,
  ) {
    if (isEmpty(formatting)) {
      return;
    }

    let hasFormattingChanged = false;

    cellRanges.forEach((cellRange) => {
      const startRowIndex = Math.min(
        cellRange.startRowIndex,
        cellRange.endRowIndex,
      );
      const endRowIndex = Math.max(
        cellRange.startRowIndex,
        cellRange.endRowIndex,
      );

      for (let i = startRowIndex; i <= endRowIndex; i++) {
        const rowNode = gridApi.getRowNode(i.toString());

        cellRange.columns.forEach((c) => {
          const data = rowNode?.data as Record<
            string,
            ICellRendererParamsWithFormatting
          >;

          if (data) {
            const dataField = data[c?.field];

            if (
              dataField &&
              !isEqual(dataField?.formatting, formatting) &&
              !ObjectHelper.compareObjects(dataField?.formatting, formatting)
            ) {
              c.field;
              if (
                dataField.formatting.dataType === 'checkbox' &&
                formatting.dataType !== 'checkbox'
              ) {
                rowNode.setDataValue(c.field, null);
              }

              if (
                dataField.formatting.dataType !== 'checkbox' &&
                formatting.dataType === 'checkbox'
              ) {
                rowNode.setDataValue(c.field, false);
              }

              dataField.formatting = {
                ...dataField?.formatting,
                ...formatting,
              };

              hasFormattingChanged = true;
            }
          }
        });
      }
    });

    if (hasFormattingChanged) {
      gridApi.refreshCells({ force: true });
      updatedCallback();
    }
  }

  clearCellRangeValue(gridApi: GridApi) {
    const selectedCellRanges = gridApi.getCellRanges();
    selectedCellRanges.forEach((cellRange) => {
      const colIds = cellRange.columns.map((col) => col.getColId());
      const startRowIndex = Math.min(
        cellRange.startRow?.rowIndex,
        cellRange.endRow?.rowIndex,
      );
      const endRowIndex = Math.max(
        cellRange.startRow.rowIndex,
        cellRange.endRow.rowIndex,
      );

      for (let i = startRowIndex; i <= endRowIndex; i++) {
        const rowNode = gridApi.getDisplayedRowAtIndex(i);
        colIds.forEach((colId) => rowNode.setDataValue(colId, null));
      }
    });
  }

  getToolbarFormattingOnSelection(
    gridApi: GridApi,
    cellRanges: AgGridCellRange[],
  ) {
    const cellFormatting = { ...DEFAULT_CELL_FORMATTING };

    cellRanges.forEach((cellRange) => {
      const startRowIndex = Math.min(
        cellRange.startRow.rowIndex,
        cellRange.endRow.rowIndex,
      );
      const endRowIndex = Math.max(
        cellRange.startRow.rowIndex,
        cellRange.endRow.rowIndex,
      );

      for (let i = startRowIndex; i <= endRowIndex; i++) {
        const rowNode = gridApi.getRowNode(i.toString());

        cellRange.columns.forEach((c) => {
          const data = rowNode.data as Record<string, unknown>;
          const cellData = data[c.getColId()] as CellData;

          if (!cellFormatting.color && cellData?.formatting?.color) {
            cellFormatting.color = cellData?.formatting?.color;
          }

          if (
            !cellFormatting.backgroundColor &&
            cellData?.formatting?.backgroundColor
          ) {
            cellFormatting.backgroundColor =
              cellData?.formatting?.backgroundColor;
          }

          if (!cellFormatting.dataType && cellData?.formatting?.dataType) {
            cellFormatting.dataType = cellData?.formatting?.dataType;
          }

          if (
            !cellFormatting.horizontalAlignment &&
            cellData?.formatting?.horizontalAlignment
          ) {
            cellFormatting.horizontalAlignment =
              cellData?.formatting?.horizontalAlignment;
          }

          if (
            !cellFormatting.verticalAlignment &&
            cellData?.formatting?.verticalAlignment
          ) {
            cellFormatting.verticalAlignment =
              cellData?.formatting?.verticalAlignment;
          }
        });

        for (let j = startRowIndex; j <= endRowIndex; j++) {
          cellRange.columns.forEach((c) => {
            const innerRowNode = gridApi.getRowNode(j.toString());
            const data = innerRowNode.data as Record<string, unknown>;
            const cellData = data[c.getColId()] as CellData;

            Object.keys(cellFormatting).forEach((k) => {
              if (
                !has(cellData?.formatting, k) ||
                cellFormatting[k] !== cellData?.formatting[k]
              ) {
                delete cellFormatting[k];
              }
            });
          });
        }
      }
    });

    return cellFormatting;
  }
}
