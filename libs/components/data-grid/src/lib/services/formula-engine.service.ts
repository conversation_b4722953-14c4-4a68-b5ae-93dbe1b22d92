import { ColDef } from '@ag-grid-community/core';
import { DestroyRef, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Parser } from 'hot-formula-parser';
import { BehaviorSubject, Observable, Subject, filter, tap } from 'rxjs';
import { CellCoordinate } from '../models/cell-coordinate';
import { CellData } from '../models/cell-data';
import { CellRange } from '../models/cell-range';

@Injectable({
  providedIn: 'root',
})
export class FormulaEngineService {
  dataStream$: Subject<Record<string, unknown>[]> = new Subject();

  private formulaParser;
  private matrixSpreadsheet: Array<Array<unknown>>;
  private selectedFunction$$ = new BehaviorSubject<string>('');

  selectedFunction$: Observable<string> = this.selectedFunction$$
    .asObservable()
    .pipe(filter(Boolean));

  constructor(private destroyRef: DestroyRef) {
    this.formulaParser = new Parser();

    this.setupFormulaParser();

    this.listenDataStream()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe();
  }

  listenDataStream() {
    return this.dataStream$.pipe(
      tap((v) => {
        this.matrixSpreadsheet = v.map((v) => {
          return Object.keys(v).map((key) => (v[key] as CellData)?.value);
        });
      }),
    );
  }

  parseExpression(expression: string) {
    if (expression?.startsWith('=')) {
      expression = expression.substring(1);
    }

    const parsed = this.formulaParser.parse(expression);

    return parsed;
  }

  emitSelectedFunction(func: string) {
    this.selectedFunction$$.next(func);
  }

  extractCellRangeFromFormulaExpression(
    s: string,
    colDefs: ColDef[],
  ): CellRange[] {
    // Extract row and column indices from the matched references
    return this.getCellReferences(s?.toString())?.map((ref) => {
      const rangeParts = ref.split(':');

      let startColumn = this.columnToIndex(ref.match(/[A-Z]+/)[0]);
      let endColumn = 1;

      let startRow = parseInt(ref.match(/\d+/)[0]);
      let endRow = startRow;

      if (rangeParts.length === 2) {
        const start = rangeParts[0];
        const end = rangeParts[1];
        startColumn = this.columnToIndex(start.match(/[A-Z]+/)[0]);
        endColumn =
          this.columnToIndex(end.match(/[A-Z]+/)[0]) - startColumn - 2;
        startRow = parseInt(start.match(/\d+/)[0]);
        endRow = parseInt(end.match(/\d+/)[0]);
      }

      const startRowIndex = startRow - 1;
      const endRowIndex = endRow - 1;
      const startColumnIndex = startColumn - 1;

      return <CellRange>{
        startRowIndex,
        endRowIndex,
        columns: [...colDefs].splice(startColumnIndex, endColumn),
      };
    });
  }

  private getCellReferences = (s: string) =>
    s?.match(/[A-Z]+\d+(:[A-Z]+\d+)?/g);

  private columnToIndex(column: string) {
    let index = 0;
    for (let i = 0; i < column.length; i++) {
      index = index * 26 + (column.charCodeAt(i) - 64);
    }
    return index;
  }

  private setupFormulaParser() {
    this.formulaParser.on('callCellValue', (cellCoord, done) => {
      const fragment = this.buildFormulaExpressionFragment(
        cellCoord,
        cellCoord,
      );

      if (fragment) {
        done(fragment);
      }
    });

    this.formulaParser.on(
      'callRangeValue',
      (startCellCoord: CellCoordinate, endCellCoord: CellCoordinate, done) => {
        const fragment = this.buildFormulaExpressionFragment(
          startCellCoord,
          endCellCoord,
        );

        if (fragment) {
          done(fragment);
        }
      },
    );
  }

  private buildFormulaExpressionFragment(
    startCellCoord: CellCoordinate,
    endCellCoord: CellCoordinate,
  ) {
    const fragment = [];

    for (
      let row = startCellCoord.row.index;
      row <= endCellCoord.row.index;
      row++
    ) {
      const rowData = this.matrixSpreadsheet[row];
      const colFragment = [];

      for (
        let col = startCellCoord.column.index;
        col <= endCellCoord.column.index;
        col++
      ) {
        let value = rowData[col];

        if (
          value &&
          typeof value === 'string' &&
          !Number.isNaN(parseFloat(value as string))
        ) {
          value = parseFloat(value as string);
        }

        colFragment.push(value);
      }
      fragment.push(colFragment);
    }

    return fragment;
  }
}
