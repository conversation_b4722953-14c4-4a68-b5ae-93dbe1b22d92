import { GetContextMenuItemsParams, GridApi } from '@ag-grid-community/core';
import { Injectable } from '@angular/core';
import { cloneDeep } from 'lodash-es';
import { CellData } from '../models/cell-data';
import { DataGridConfig } from '../utils/data-grid';
import { EMPTY_CELL_DATA } from '../utils/empty-cell-data';

@Injectable({
  providedIn: 'root',
})
export class DataGridRowService {
  pinRow(
    gridApi: GridApi,
    config: DataGridConfig,
    params: GetContextMenuItemsParams,
    position: 'top' | 'bottom',
  ) {
    if (position === 'top') {
      gridApi.setPinnedTopRowData([params.node.data]);
      config.pinRow(params.node.data, 'top');
    } else {
      gridApi.setPinnedBottomRowData([params.node.data]);
      config.pinRow(params.node.data, 'bottom');
    }
  }

  unpinRow(
    gridApi: GridApi,
    config: DataGridConfig,
    params: GetContextMenuItemsParams,
  ) {
    const pinnedRowPosition = params.node.rowPinned;
    if (pinnedRowPosition) {
      pinnedRowPosition === 'top'
        ? gridApi.setPinnedTopRowData([])
        : gridApi.setPinnedBottomRowData([]);

      config.unpinRow(pinnedRowPosition);
    } else {
      if (config.pinnedTopRow?.id === params.node.data.id) {
        gridApi.setPinnedTopRowData([]);
        config.unpinRow('top');
      }

      if (config.pinnedBottomRow?.id === params.node.data.id) {
        gridApi.setPinnedBottomRowData([]);
        config.unpinRow('bottom');
      }
    }
  }

  addNewRow(config: DataGridConfig, params: GetContextMenuItemsParams) {
    const row = params.node;
    const emptyRow: Record<string, CellData> = config.columnDefs.reduce(
      (acc, cur) => {
        acc[cur.field] = cloneDeep(EMPTY_CELL_DATA);

        return acc;
      },
      {} as Record<string, CellData>,
    );

    config.rowData.splice(row.rowIndex + 1, 0, emptyRow);
  }
}
