import { GridApi } from '@ag-grid-community/core';
import { EventEmitter } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { DataGridViewMode } from '../enums/data-grid-view-mode';
import { DataGridConfig } from '../utils/data-grid';
import { CellFormatting } from './cell-formatting';
import { DataGridToolbarConfig } from './data-grid-toolbar-config';
import { ExportOptions } from './export-options';

export interface UiDataGrid {
  config?: DataGridConfig;
  workingConfig?: DataGridConfig;
  dataGridViewMode?: DataGridViewMode;
  tableExportName?: string;
  toolbarConfig?: DataGridToolbarConfig;
  gridApi?: GridApi<unknown>;
  configUpdated?: EventEmitter<DataGridConfig>;
  selectionFormatting$?: BehaviorSubject<CellFormatting>;
  export?: (exportOptions: ExportOptions) => void;
  reloadGrid?: (config: DataGridConfig) => void;
}
