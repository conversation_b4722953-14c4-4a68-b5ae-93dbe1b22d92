import {
  Component,
  DestroyRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { CellCoordinate } from '../../models/cell-coordinate';
import { FormulaEngineService } from '../../services/formula-engine.service';

@Component({
  selector: 'ui-data-grid-formula-bar',
  templateUrl: './data-grid-formula-bar.component.html',
  styleUrls: ['./data-grid-formula-bar.component.scss'],
})
export class DataGridFormulaBarComponent implements OnInit {
  @Input()
  inputValue: string;

  @Input()
  showValue = true;

  @Input()
  focusedCellCoordinates: string;

  @Output()
  inputValueChanged = new EventEmitter<string>();

  matrixSpreadsheet: Array<Array<unknown>>;
  formulaCellRange: CellCoordinate[];

  constructor(
    private destroyRef: DestroyRef,
    private formulaEngine: FormulaEngineService,
  ) {}

  ngOnInit(): void {
    this.formulaEngine.selectedFunction$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((func) => {
        if (!this.inputValue) {
          this.inputValue = '=' + func;
        } else if (this.inputValue.startsWith('=')) {
          this.inputValue = `${this.inputValue}+${func}`;
        } else {
          this.inputValue = `=${this.inputValue}+${func}`;
        }

        this.inputValueChanged.emit(this.inputValue);
      });
  }

  onInputValueChange(newValue: string) {
    this.inputValueChanged.emit(newValue);
  }

  insertFunction() {
    if (this.inputValue?.startsWith('=')) {
      return;
    }

    this.inputValue = this.inputValue ? '=' + this.inputValue : '=';
    this.inputValueChanged.emit(this.inputValue);
  }
}
