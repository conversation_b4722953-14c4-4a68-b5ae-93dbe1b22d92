<div
  class="custom-header-container"
  [class.selected]="
    (dataGridHeaderService.columnSelectedData$ | async)?.colDef?.field ===
    params?.column?.getColId()
  "
  (click)="selectColumn()"
  [class.edit-mode]="isInEditMode"
>
  <div class="header-name-container">
    @if (params.column.getColDef().headerName; as header) {
      <div class="custom-header-label">
        {{ header }}
      </div>
    }
  </div>
  @if (!isInEditMode) {
    <div class="buttons-container">
      <div
        (click)="onSortRequested(getNextSortState(), $event)"
        class="sorting-buttons"
      >
        @if (params.enableSorting && (currentSort === 'desc' || !currentSort)) {
          <div class="custom-sort-down-label">
            <ui-icon
              name="downward-arrow"
              size="small"
              color="subtle"
            ></ui-icon>
          </div>
        }
        @if (params.enableSorting && (currentSort === 'asc' || !currentSort)) {
          <div class="custom-sort-up-label">
            <ui-icon name="upward-arrow" size="small" color="subtle"></ui-icon>
          </div>
        }
      </div>
      @if (params.enableMenu && !isInEditMode) {
        <div
          #menuButton
          class="custom-header-menu-button"
          (click)="onMenuClicked()"
        >
          <ui-icon name="filter_alt" size="medium" color="subtle"></ui-icon>
          @if (filterIndicator) {
            <ui-circle-status
              [statusConfig]="{ color: 'purple', size: 'small' }"
            ></ui-circle-status>
          }
        </div>
      }
    </div>
  }
</div>
