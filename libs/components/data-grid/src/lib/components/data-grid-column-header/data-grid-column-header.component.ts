import { DataGridHeaderService } from '@fincloud/core/business-case';

import { IHeaderAngularComp } from '@ag-grid-community/angular';
import { IHeaderParams } from '@ag-grid-community/core';
import { Component, ElementRef, HostListener, ViewChild } from '@angular/core';
import { DataGridViewMode } from '../../enums/data-grid-view-mode';
import { CustomHeaderParams } from '../../models/custom-header-params';

@Component({
  selector: 'ui-data-grid-column-header',
  templateUrl: './data-grid-column-header.component.html',
  styleUrls: ['./data-grid-column-header.component.scss'],
})
export class DataGridColumnHeaderComponent implements IHeaderAngularComp {
  isInEditMode = false;
  headerName: string;
  params: IHeaderParams & CustomHeaderParams;

  DataGridViewMode = DataGridViewMode;

  currentSort: 'asc' | 'desc' | null;
  filterIndicator = false;

  @ViewChild('menuButton', { read: ElementRef }) public menuButton: ElementRef;

  @HostListener('contextmenu', ['$event'])
  onRightClick(event: Event) {
    event.preventDefault();

    if (this.params.viewMode !== DataGridViewMode.PERSISTENT_EDIT) {
      return;
    }

    this.selectColumn();

    // This functionality is not publicly exposed through ag-grid's api and may change in the future. For now there is no other way to trigger the context menu from the column header.
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (this.params.api as unknown as any).contextMenuFactory.showMenu(
      null,
      this.params.column,
      null,
      event,
    );
  }

  constructor(protected dataGridHeaderService: DataGridHeaderService) {}

  agInit(params: IHeaderParams & CustomHeaderParams): void {
    this.headerName = params.column.getColDef().headerName;
    this.params = params;

    params.column.removeEventListener(
      'sortChanged',
      this.onSortChanged.bind(this),
    );
    params.column.removeEventListener(
      'filterChanged',
      this.onFilterApplied.bind(this),
    );
    params.column.addEventListener(
      'sortChanged',
      this.onSortChanged.bind(this),
    );
    params.column.addEventListener(
      'filterChanged',
      this.onFilterApplied.bind(this),
    );

    this.onSortChanged();
  }

  selectColumn() {
    if (!this.isInEditMode) {
      this.dataGridHeaderService.emitColumnSelected({
        colDef: this.params.column.getColDef(),
      });
    }
  }

  onMenuClicked() {
    this.params.showColumnMenu(this.menuButton.nativeElement);
  }

  onSortChanged() {
    if (this.params.column.isSortAscending()) {
      this.currentSort = 'asc';
    } else if (this.params.column.isSortDescending()) {
      this.currentSort = 'desc';
    } else {
      this.currentSort = null;
    }
  }

  onSortRequested(order: 'asc' | 'desc' | null, _event: MouseEvent) {
    _event.preventDefault();

    this.dataGridHeaderService.emitColumnSortedData({
      colDef: this.params.column.getColDef(),
      sort: order,
    });
  }

  refresh(_params: IHeaderParams): boolean {
    // Required by the interface. Not used for now
    return false;
  }

  showInput(e: Event) {
    e.preventDefault();

    if (this.params.viewMode === DataGridViewMode.PERSISTENT_EDIT) {
      this.isInEditMode = true;
    }
  }

  getNextSortState(): 'asc' | 'desc' | null {
    switch (this.currentSort) {
      case 'asc':
        return 'desc';
      case 'desc':
        return null;
      case null:
        return 'asc';
    }
  }

  onFilterApplied() {
    const filterModel = this.params.api.getFilterModel();
    this.filterIndicator = Object.keys(filterModel).includes(
      this.params.column.getId(),
    );
    this.dataGridHeaderService.emitColumnFilteredData(filterModel);
  }
}
