import { Component, Input } from '@angular/core';
import { MetricFunctions } from '../../models/metric-functions';
import { SummaryMetricFunctions } from '../../models/summary-metric-functions';
import { STATUS_BAR_FUNCTION_OPTIONS } from '../../utils/status-bar-function-options';

@Component({
  selector: 'ui-data-grid-status-bar',
  templateUrl: './data-grid-status-bar.component.html',
  styleUrls: ['./data-grid-status-bar.component.scss'],
})
export class DataGridStatusBarComponent {
  @Input()
  summaryMetricFunctions: SummaryMetricFunctions;

  selectedOption = [...STATUS_BAR_FUNCTION_OPTIONS.map((f) => f.value)];

  functionSelectorOptions = STATUS_BAR_FUNCTION_OPTIONS;

  isSelected(type: string) {
    return this.selectedOption.includes(type);
  }

  getSummaryMetricValue(type: MetricFunctions) {
    if (this.summaryMetricFunctions && this.summaryMetricFunctions[type]) {
      return this.summaryMetricFunctions[type];
    }

    return null;
  }
}
