import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Color } from '@fincloud/components/color-picker';
import { ExportType } from '../../enums/export-type';
import { CellDataType } from '../../models/cell-data-type';
import { CellFormatting } from '../../models/cell-formatting';
import { CellHorizontalAlignment } from '../../models/cell-horizontal-alignment';
import { CellVerticalAlignment } from '../../models/cell-vertical-alignment';
import { UiDataGrid } from '../../models/ui-data-grid';
import { SUPPORTED_CELL_COLORS } from '../../utils/supported-cell-colors';

@Component({
  selector: 'ui-data-grid-toolbar',
  templateUrl: './data-grid-toolbar.component.html',
  styleUrls: ['./data-grid-toolbar.component.scss'],
})
export class DataGridToolbarComponent {
  @Input()
  dataGridModel: UiDataGrid;

  colors: Color[][] = SUPPORTED_CELL_COLORS;

  @Output()
  openInFullscreen = new EventEmitter();

  get activeColor() {
    return this.dataGridModel.selectionFormatting$.getValue().color;
  }

  get activeBackgroundColor() {
    return this.dataGridModel.selectionFormatting$.getValue().backgroundColor;
  }

  get enableFullScreenMode() {
    return this.dataGridModel.toolbarConfig.enableFullScreen;
  }

  get enableActionsMenu() {
    return this.dataGridModel.toolbarConfig.enableActionsMenu;
  }

  applyUndo() {
    this.dataGridModel.gridApi.undoCellEditing();
  }

  applyRedo() {
    this.dataGridModel.gridApi.redoCellEditing();
  }

  applyCut() {
    this.dataGridModel.gridApi.cutToClipboard();
  }

  toggleBold() {
    this.setFormattingOnSelection({
      bold: !this.dataGridModel.selectionFormatting$.getValue()?.bold,
    });
  }

  toggleItalic() {
    this.setFormattingOnSelection({
      italic: !this.dataGridModel.selectionFormatting$.getValue()?.italic,
    });
  }

  toggleUnderline() {
    this.setFormattingOnSelection({
      underline: !this.dataGridModel.selectionFormatting$.getValue()?.underline,
    });
  }

  toggleWrapText() {
    this.setFormattingOnSelection({
      wrapText: !this.dataGridModel.selectionFormatting$.getValue()?.wrapText,
    });
  }

  setDataType(dataType: CellDataType) {
    const updatedDataType =
      this.dataGridModel.selectionFormatting$.getValue().dataType === dataType
        ? null
        : dataType;

    this.setFormattingOnSelection({
      dataType: updatedDataType,
    });
  }

  exportCSV() {
    this.dataGridModel.export({ type: ExportType.CSV });
  }

  exportExcel() {
    this.dataGridModel.export({ type: ExportType.EXCEL });
  }

  alignHorizontally(alignment: CellHorizontalAlignment) {
    this.setFormattingOnSelection({
      horizontalAlignment: alignment,
    });
  }

  alignVertically(alignment: CellVerticalAlignment) {
    this.setFormattingOnSelection({
      verticalAlignment: alignment,
    });
  }

  setColor(color: string) {
    this.setFormattingOnSelection({
      color: color,
    });
  }

  setBackgroundColor(backgroundColor: string) {
    this.setFormattingOnSelection({
      backgroundColor: backgroundColor,
    });
  }

  onFullScreen() {
    this.openInFullscreen.emit();
  }

  private setFormattingOnSelection(newFormatting: CellFormatting) {
    const formatting = {
      ...this.dataGridModel.selectionFormatting$.getValue(),
      ...newFormatting,
    };

    this.dataGridModel.selectionFormatting$.next(formatting);
  }
}
