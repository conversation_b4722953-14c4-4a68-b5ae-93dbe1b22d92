import { Component, ViewChild } from '@angular/core';
import { ActionsMenuComponent } from '@fincloud/components/navigation';
import { cloneDeep } from 'lodash-es';
import { ExcelFormulaGroupName } from '../../models/excel-formula-group-name';
import { FormulaGroup } from '../../models/formula-group';
import { FormulaEngineService } from '../../services/formula-engine.service';
import { FORMULA_GROUPS } from '../../utils/formula-groups';

@Component({
  selector: 'ui-formula-dropdown-selector',
  templateUrl: './formula-dropdown-selector.component.html',
  styleUrls: ['./formula-dropdown-selector.component.scss'],
})
export class FormulaDropdownSelectorComponent {
  @ViewChild(ActionsMenuComponent) actionsMenu: ActionsMenuComponent;

  formulaGroups: FormulaGroup = FORMULA_GROUPS;

  formulasToShow: FormulaGroup = cloneDeep(this.formulaGroups);

  constructor(private formulaEngine: FormulaEngineService) {}

  onFunctionSelect(func: string) {
    this.actionsMenu.closeMenu();
    this.formulaEngine.emitSelectedFunction(func + '()');
  }

  filterOptions(value: string) {
    Object.keys(this.formulaGroups).forEach((k) => {
      const fomulaGroup = k as ExcelFormulaGroupName;
      const formulas = cloneDeep(this.formulaGroups);
      this.formulasToShow[fomulaGroup] = formulas[fomulaGroup].filter(
        (option) => option.toLowerCase().includes(value?.toLowerCase()),
      );
    });
  }
}
