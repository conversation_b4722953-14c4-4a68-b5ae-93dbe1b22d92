<ui-actions-menu
  [optionsTemplate]="functionOptions"
  [hideArrow]="true"
  iconColor="subtle"
  [showMenuBottom]="true"
  iconSize="medium-large"
  [hideDotsTrigger]="true"
  [autoClose]="false"
  class="group-actions"
>
  <ui-icon
    name="functions"
    size="medium"
    color="primary"
    class="icon-group-first"
  ></ui-icon>
  <ui-icon
    name="arrow_down"
    size="medium"
    color="primary"
    class="icon-group-second"
  ></ui-icon>
  <ng-template #functionOptions>
    <div class="formula-container">
      <ui-search-filter
        #searchFilterComponent
        (search)="filterOptions($event)"
        [hasBorder]="true"
        color="gray"
        class="search-filter"
        size="small"
        placeholder="Suche..."
      ></ui-search-filter>

      <div class="formula-list">
        <ng-scrollbar>
          <div
            class="function-category-label"
            i18n="@@dataGrid.toolbar.functions.category.commonlyUsed"
          >
            H<PERSON><PERSON><PERSON> verwendet
          </div>

          <ui-actions-menu-item
            class="action-group-menu-item"
            (clicked)="onFunctionSelect('SUM')"
            label="SUM"
          >
          </ui-actions-menu-item>
          <ui-actions-menu-item
            class="action-group-menu-item"
            (clicked)="onFunctionSelect('AVERAGE')"
            label="AVERAGE"
          >
          </ui-actions-menu-item>
          <ui-actions-menu-item
            class="action-group-menu-item"
            (clicked)="onFunctionSelect('COUNT')"
            label="COUNT"
          >
          </ui-actions-menu-item>
          <ui-actions-menu-item
            class="action-group-menu-item"
            (clicked)="onFunctionSelect('MIN')"
            label="MIN"
          >
          </ui-actions-menu-item>
          <ui-actions-menu-item
            class="action-group-menu-item"
            (clicked)="onFunctionSelect('MAX')"
            label="MAX"
          >
          </ui-actions-menu-item>

          <ui-horizontal-divider class="divider" color="gray">
          </ui-horizontal-divider>

          <div
            class="function-category-label"
            i18n="@@dataGrid.toolbar.functions.category.financial"
          >
            Finanzen
          </div>
          @for (item of formulasToShow.financial; track item) {
            <ui-actions-menu-item
              class="action-group-menu-item"
              (clicked)="onFunctionSelect(item)"
              [label]="item"
            >
            </ui-actions-menu-item>
          }

          <div
            class="function-category-label"
            i18n="@@dataGrid.toolbar.functions.category.date"
          >
            Datum
          </div>
          @for (item of formulasToShow.dateAndTime; track item) {
            <ui-actions-menu-item
              class="action-group-menu-item"
              (clicked)="onFunctionSelect(item)"
              [label]="item"
            >
            </ui-actions-menu-item>
          }

          <div
            class="function-category-label"
            i18n="@@dataGrid.toolbar.functions.category.math"
          >
            Mathematik
          </div>

          @for (item of formulasToShow.mathAndTrigonometry; track item) {
            <ui-actions-menu-item
              class="action-group-menu-item"
              (clicked)="onFunctionSelect(item)"
              [label]="item"
            >
            </ui-actions-menu-item>
          }

          <div
            class="function-category-label"
            i18n="@@dataGrid.toolbar.functions.category.statistics"
          >
            Statistik
          </div>
          @for (item of formulasToShow.statistical; track item) {
            <ui-actions-menu-item
              class="action-group-menu-item"
              (clicked)="onFunctionSelect(item)"
              [label]="item"
            >
            </ui-actions-menu-item>
          }

          <div
            class="function-category-label"
            i18n="@@dataGrid.toolbar.functions.category.lookupAndReference"
          >
            Nachschlagen
          </div>
          @for (item of formulasToShow.lookupAndReference; track item) {
            <ui-actions-menu-item
              class="action-group-menu-item"
              (clicked)="onFunctionSelect(item)"
              [label]="item"
            >
            </ui-actions-menu-item>
          }

          <div
            class="function-category-label"
            i18n="@@dataGrid.toolbar.functions.category.text"
          >
            Text
          </div>
          @for (item of formulasToShow.text; track item) {
            <ui-actions-menu-item
              class="action-group-menu-item"
              (clicked)="onFunctionSelect(item)"
              [label]="item"
            >
            </ui-actions-menu-item>
          }

          <div
            class="function-category-label"
            i18n="@@dataGrid.toolbar.functions.category.logical"
          >
            Logik
          </div>
          @for (item of formulasToShow.logical; track item) {
            <ui-actions-menu-item
              class="action-group-menu-item"
              (clicked)="onFunctionSelect(item)"
              [label]="item"
            >
            </ui-actions-menu-item>
          }

          <div
            class="function-category-label"
            i18n="@@dataGrid.toolbar.functions.category.information"
          >
            Informationen
          </div>
          @for (item of formulasToShow.information; track item) {
            <ui-actions-menu-item
              class="action-group-menu-item"
              (clicked)="onFunctionSelect(item)"
              [label]="item"
            >
            </ui-actions-menu-item>
          }

          <div
            class="function-category-label"
            i18n="@@dataGrid.toolbar.functions.category.engineering"
          >
            Technik
          </div>
          @for (item of formulasToShow.engineering; track item) {
            <ui-actions-menu-item
              class="action-group-menu-item"
              (clicked)="onFunctionSelect(item)"
              [label]="item"
            >
            </ui-actions-menu-item>
          }
        </ng-scrollbar>
      </div>
    </div>
  </ng-template>
</ui-actions-menu>
