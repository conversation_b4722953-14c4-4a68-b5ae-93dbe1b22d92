import { DataGridHeaderService } from '@fincloud/core/business-case';

import {
  CellRange as AgGridCellRange,
  CellFocusedEvent,
  CellKeyDownEvent,
  CellValueChangedEvent,
  Column,
  ColumnMovedEvent,
  ColumnPinnedEvent,
  FullWidthCellKeyDownEvent,
  GetContextMenuItemsParams,
  GetMainMenuItemsParams,
  GridApi,
  GridReadyEvent,
  MenuItemDef,
  ProcessCellForExportParams,
  RangeSelectionChangedEvent,
  RowDragEvent,
} from '@ag-grid-community/core';
import { DOCUMENT } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  EventEmitter,
  HostListener,
  Inject,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  TemplateRef,
} from '@angular/core';
import { ConfigurationService } from '@fincloud/core/config';
import { LocaleService } from '@fincloud/core/services';
import { cloneDeep } from 'lodash-es';
import {
  BehaviorSubject,
  Subject,
  combineLatest,
  filter,
  skip,
  tap,
} from 'rxjs';
import { DataGridViewMode } from '../../enums/data-grid-view-mode';
import { CellData } from '../../models/cell-data';
import { CellFormatting } from '../../models/cell-formatting';
import { CellRange } from '../../models/cell-range';
import { DataGridToolbarConfig } from '../../models/data-grid-toolbar-config';
import { ExportOptions } from '../../models/export-options';
import { UiDataGrid } from '../../models/ui-data-grid';
import { DataGridColumnService } from '../../services/data-grid-column.service';
import { DataGridCoreService } from '../../services/data-grid-core.service';
import { DataGridExportService } from '../../services/data-grid-export.service';
import { DataGridRowService } from '../../services/data-grid-row.service';
import { FormulaEngineService } from '../../services/formula-engine.service';
import { DataGridConfig } from '../../utils/data-grid';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AG_GRID_LOCALE_DE } from '../../locales/locale.de';
import { AG_GRID_LOCALE_EN } from '../../locales/locale.en';
import { SummaryMetricFunctions } from '../../models/summary-metric-functions';
import { DEFAULT_GRID_TOOLBAR_CONFIG } from '../../utils/default-grid-toolbar-config';
import { EMPTY_CELL_DATA } from '../../utils/empty-cell-data';
import { ROW_NUMBER_COLUMN } from '../../utils/row-number-column';

@Component({
  selector: 'ui-data-grid',
  templateUrl: './data-grid.component.html',
  styleUrls: ['./data-grid.component.scss'],
  providers: [DataGridHeaderService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DataGridComponent implements UiDataGrid, OnChanges, OnInit {
  @Input() config: DataGridConfig;
  @Input() dataGridViewMode = DataGridViewMode.READ_ONLY;
  @Input() tableExportName: string;
  @Input() toolbarConfig: DataGridToolbarConfig = DEFAULT_GRID_TOOLBAR_CONFIG;
  @Input() actionsMenuOptionsTemplate: TemplateRef<unknown>;
  @Input() label: string;
  @Input() highlightLabel: boolean;

  @Output() configUpdated = new EventEmitter<DataGridConfig>();
  @Output() saveChanged = new EventEmitter<DataGridConfig>();
  @Output() openInFullscreen = new EventEmitter();

  gridApi: GridApi<unknown>;

  selectionFormatting$ = new BehaviorSubject<CellFormatting>({});
  summaryMetricFunctions$ = new Subject<SummaryMetricFunctions>();

  protected popupParent: HTMLElement | null;
  protected shouldEmitChange: boolean;
  protected _workingConfig: DataGridConfig;
  protected focusedCellCoordinates: string;
  protected focusedCellValue: string;

  private locale: 'de' | 'en';
  private ctrlSelected = false;

  get workingConfig() {
    return this._workingConfig;
  }

  get colDefs() {
    return this.workingConfig.columnDefs;
  }

  // defaultColDef: ColDef = {
  //   flex: 1,
  //   editable: false,
  //   minWidth: 200,
  //   useValueFormatterForExport: true,
  //   headerComponent: DataGridColumnHeaderComponent,
  // };

  constructor(
    private destroyRef: DestroyRef,
    private dataGridHeaderService: DataGridHeaderService,
    @Inject(DOCUMENT) private document: Document,
    private configService: ConfigurationService,
    private formulaEngine: FormulaEngineService,
    private dataGridColumnHelperService: DataGridColumnService,
    private dataGridRowHelperService: DataGridRowService,
    private dataGridCoreService: DataGridCoreService,
    private dataGridExportService: DataGridExportService,
    private localeService: LocaleService,
  ) {}

  ngOnInit() {
    this.initializeGrid();
  }

  @HostListener('document:keydown', ['$event'])
  handleKeyboardDownEvent(event: KeyboardEvent) {
    if (event.metaKey) {
      this.ctrlSelected = true;
    }
  }

  @HostListener('document:keyup', ['$event'])
  handleKeyboardUpEvent(event: KeyboardEvent) {
    if (event.metaKey !== true) {
      this.ctrlSelected = false;
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes?.dataGridViewMode?.currentValue !==
      changes?.dataGridViewMode?.previousValue
    ) {
      this.onChangedViewMode();
    }
  }

  initializeGrid() {
    this.popupParent = this.document.querySelector('body');
    this.locale = this.localeService.getUserPreferredLocale();

    combineLatest([
      this.listenColumnSortedData(),
      this.listenColumnFilteredData(),
      this.listenColumnSelected(),
      this.listenSelectionFormatting(),
    ])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe();

    this.reloadGrid(this.config);
  }

  listenColumnSortedData() {
    return this.dataGridHeaderService.columnSortedData$.pipe(
      takeUntilDestroyed(this.destroyRef),
      tap((data) => {
        this.workingConfig.sortColumn(data);
        this.triggerConfigUpdate();
      }),
    );
  }

  listenColumnFilteredData() {
    return this.dataGridHeaderService.columnFilteredData$.pipe(
      tap((data) => {
        if (this.workingConfig.isFilterDataNew(data)) {
          this.workingConfig.storeFilterData(data);
          this.triggerConfigUpdate();
        }
      }),
    );
  }

  listenColumnSelected() {
    return this.dataGridHeaderService.columnSelectedData$.pipe(
      filter((v) => v !== null),
      tap((data) =>
        this.dataGridColumnHelperService.setColumnSelected(
          this.gridApi,
          this.ctrlSelected,
          data,
        ),
      ),
    );
  }

  listenSelectionFormatting() {
    return this.selectionFormatting$.pipe(
      skip(1),
      tap((formatting) => {
        this.dataGridCoreService.setFormattingOnSelection(
          this.gridApi,
          formatting,
          this.gridApi.getCellRanges().map((c) => {
            return {
              startRowIndex: c.startRow.rowIndex,
              endRowIndex: c.endRow.rowIndex,
              columns: c.columns.map((col) =>
                this.workingConfig.columnDefs.find(
                  (cd) => cd.field === col.getColId(),
                ),
              ),
            };
          }),
          this.triggerConfigUpdate,
        );
      }),
    );
  }

  reloadGrid(config: DataGridConfig = this.config) {
    if (config) {
      this._workingConfig = cloneDeep(config);
      this.dataGridCoreService.enrichConfigWithInternals(this.workingConfig);
      this.workingConfig.localeText =
        this.locale === 'de' ? AG_GRID_LOCALE_DE : AG_GRID_LOCALE_EN;

      this.refreshViewMode();
      this.reloadFormulaEngineData();
    }
  }

  onGridReady(params: GridReadyEvent<unknown>) {
    this.gridApi = params.api;

    this.refreshViewMode();
  }

  onCellValueChanged(params: CellValueChangedEvent) {
    this.triggerConfigUpdate();

    this.setFocusedCellValue(
      this.gridApi?.getValue(params.column, params.node),
    );
    this.handleValueChangedCut(params);
    this.reloadFormulaEngineData();
  }

  onCellKeyDown(params: CellKeyDownEvent | FullWidthCellKeyDownEvent) {
    this.setFocusedCellValue((params.event.target as HTMLInputElement).value);
  }

  onRowDragEnd(event: RowDragEvent) {
    this.workingConfig.dragRow(event.node.data, event.overIndex);
    this.shouldEmitChange = event.overIndex >= 0 ? true : false;
  }

  onColumnMoved(event: ColumnMovedEvent) {
    this.workingConfig.moveColumn(event.column?.getColDef(), event.toIndex);
    this.shouldEmitChange = event.toIndex >= 0 ? true : false;
  }

  onColumnPinned(event: ColumnPinnedEvent) {
    this.workingConfig.changePinnedColumnHeader(
      event.column.getColDef(),
      event.pinned,
    );
    this.gridApi?.setColumnDefs(this.workingConfig.columnDefs);
  }

  onDragStopped() {
    if (this.shouldEmitChange) {
      this.triggerConfigUpdate();
      this.shouldEmitChange = false;
    }
  }

  onRangeSelectionChanged(event: RangeSelectionChangedEvent) {
    if (event.finished) {
      const cellRanges = this.gridApi.getCellRanges();

      this.setSummaryMetricFunctions(cellRanges);
      this.updateFocusedCellOnSelection(cellRanges);
      this.updateToolbarFormattingOnSelection(cellRanges);
    }
  }

  onProcessCellForClipboard(params: ProcessCellForExportParams) {
    return JSON.stringify(params.node.data[params.column.getColId()]);
  }

  onProcessCellFromClipboard(params: ProcessCellForExportParams) {
    let v = params.value;
    if (params.value) {
      v = JSON.parse(params.value);
    }

    params.node.data[params.column.getColId()] = cloneDeep(v);
    params['value'] = v?.value;

    params.api.refreshCells({ force: true });

    return params.value;
  }

  onChangedViewMode() {
    if (
      this.dataGridViewMode === DataGridViewMode.PERSISTENT_EDIT &&
      !this.gridApi?.getCellRanges().length
    ) {
      this.gridApi?.addCellRange({
        columnStart: this.colDefs[1].field,
        columnEnd: this.colDefs[1].field,
        rowStartIndex: 0,
        rowEndIndex: 0,
      });
      this.focusedCellCoordinates = 'A 1';
      this.setFocusedCellValue(
        this.gridApi?.getValue(
          this.colDefs[1].field,
          this.gridApi.getDisplayedRowAtIndex(0),
        ),
      );
    }

    this.refreshViewMode();
  }

  onFormulaBarValueChanged(value: string) {
    const cellRanges = this.gridApi?.getCellRanges();
    const rowNode = this.gridApi?.getDisplayedRowAtIndex(
      cellRanges[cellRanges.length - 1]?.startRow?.rowIndex,
    );
    rowNode.setDataValue(cellRanges[cellRanges.length - 1]?.startColumn, value);

    this.highlightSelection(value);
  }

  onCellFocus(event: CellFocusedEvent<unknown>) {
    this.highlightFocusedCell(event);

    if (!event.forceBrowserFocus) {
      this.resetColumnHeaderSelectedData();
    }
  }

  getContextMenuItems(
    params: GetContextMenuItemsParams,
  ): (string | MenuItemDef)[] {
    if (params.column.getColId() === ROW_NUMBER_COLUMN) {
      this._workingConfig.setRowSelected(
        params.node.rowIndex,
        params.columnApi,
        params.api,
      );
    }

    const currentSelection = this.gridApi.getCellRanges();
    const currentSelectionType =
      this.workingConfig.determineSelectionType(currentSelection);
    const contextMenu = this.buildContextMenu(currentSelectionType, params);

    return contextMenu;
  }

  export(exportOptions: ExportOptions) {
    this.dataGridExportService.export(
      exportOptions,
      this.workingConfig,
      this.gridApi,
      this.tableExportName,
    );
  }

  private handleValueChangedCut(params: CellValueChangedEvent) {
    if (params.source === 'clipboardService' && params.value === null) {
      params.node.data[params.column.getColId()] = cloneDeep(EMPTY_CELL_DATA);

      this.gridApi.refreshCells({
        force: true,
      });
    }
  }

  private refreshViewMode() {
    this.dataGridCoreService.refreshViewMode(
      this.dataGridViewMode,
      this.workingConfig,
      this.gridApi,
    );
  }

  private addNewColumn(params: GetContextMenuItemsParams) {
    this?.dataGridColumnHelperService.addEmptyColumn(
      this.workingConfig,
      params.column,
    );
    this.applyNewState();
  }

  private setFocusedCellValue(value: string) {
    this.focusedCellValue = value;
  }

  private setSummaryMetricFunctions(cellRanges: AgGridCellRange[]) {
    const summaryMetrics =
      this.dataGridCoreService.getSummaryMetricCalculations(
        this.gridApi,
        cellRanges,
      );

    this.summaryMetricFunctions$.next(summaryMetrics);
  }

  private updateFocusedCellOnSelection(cellRanges: AgGridCellRange[]) {
    this.focusedCellCoordinates = `${
      cellRanges[cellRanges.length - 1]?.startColumn.getColDef().headerName
    } ${cellRanges[cellRanges.length - 1]?.startRow.rowIndex + 1}`;

    this.setFocusedCellValue(
      this.gridApi?.getValue(
        cellRanges[cellRanges.length - 1]?.startColumn,
        this.gridApi.getDisplayedRowAtIndex(
          cellRanges[cellRanges.length - 1]?.startRow.rowIndex,
        ),
      ),
    );
  }

  private updateToolbarFormattingOnSelection(cellRanges: AgGridCellRange[]) {
    const toolbarFormatting =
      this.dataGridCoreService.getToolbarFormattingOnSelection(
        this.gridApi,
        cellRanges,
      );

    this.selectionFormatting$.next(toolbarFormatting);
  }

  private highlightFocusedCell(event: CellFocusedEvent<unknown>) {
    const cellValue = (
      this.workingConfig.workingRowData[event.rowIndex][
        (event.column as Column).getColId()
      ] as CellData
    )?.value;

    this.highlightSelection(cellValue);
  }

  private resetColumnHeaderSelectedData() {
    this.dataGridHeaderService.emitColumnSelected(null);
  }

  private highlightSelection(expression: string) {
    this.clearAllCellsHighlight();

    const cellIndeces =
      this.formulaEngine.extractCellRangeFromFormulaExpression(
        expression,
        this.workingConfig.workingColumnDefs,
      );

    cellIndeces?.forEach(() => {
      const formatting: CellFormatting = {
        ...this.selectionFormatting$.getValue(),
        ...{
          highlight: !this.selectionFormatting$.getValue().highlight,
        },
      };

      this.dataGridCoreService.setFormattingOnSelection(
        this.gridApi,
        formatting,
        cellIndeces,
        this.triggerConfigUpdate,
      );
    });
  }

  private clearAllCellsHighlight() {
    const clearHighlightFormatting: CellFormatting = {
      ...{
        highlight: false,
      },
    };

    const allCellRange = [
      <CellRange>{
        startRowIndex: 0,
        endRowIndex: this.workingConfig.rowData.length,
        columns: [...this.workingConfig.workingColumnDefs],
      },
    ];

    this.dataGridCoreService.setFormattingOnSelection(
      this.gridApi,
      clearHighlightFormatting,
      allCellRange,
      this.triggerConfigUpdate,
    );
  }

  private reloadFormulaEngineData() {
    this.formulaEngine.dataStream$.next(this.workingConfig.workingRowData);
  }

  private addNewRow(params: GetContextMenuItemsParams) {
    this.dataGridRowHelperService.addNewRow(this.workingConfig, params);

    this.applyNewState();
  }

  private deleteColumn(params: GetContextMenuItemsParams) {
    this.workingConfig.deleteColumn(params.column);
    this.applyNewState();
  }

  private pinColumn(params: GetMainMenuItemsParams, pinned: 'left' | 'right') {
    this.workingConfig.pinColumn(params.column, pinned);
    this.applyNewState();
  }

  private deleteRow(params: GetContextMenuItemsParams) {
    this.workingConfig.deleteRow(params.node);
    this.applyNewState();
  }

  private pinRow(
    params: GetContextMenuItemsParams,
    position: 'top' | 'bottom',
  ) {
    this.dataGridRowHelperService.pinRow(
      this.gridApi,
      this.workingConfig,
      params,
      position,
    );

    this.applyNewState();
  }

  private unpinRow(params: GetContextMenuItemsParams) {
    this.dataGridRowHelperService.unpinRow(
      this.gridApi,
      this.workingConfig,
      params,
    );

    this.applyNewState();
  }

  private clearCellRangeValue() {
    this.dataGridCoreService.clearCellRangeValue(this.gridApi);

    this.applyNewState();
  }

  private triggerConfigUpdate = () => {
    this.configUpdated.emit(this.workingConfig);
  };

  private applyNewState() {
    this.triggerConfigUpdate();
    this.refreshViewMode();
  }

  private buildContextMenu(
    currentSelection: 'range' | 'row' | 'column',
    params: GetContextMenuItemsParams,
  ): (string | MenuItemDef)[] {
    const commonMenuItems = [
      'cut',
      'copy',
      {
        name: $localize`:@@dataGrid.contextMenu.paste:Einfügen`,
        icon: '<span class="material-symbols-outlined">content_paste</span>',
        disabled: true,
        tooltip: $localize`:@@dataGrid.contextMenu.paste.tooltip:Verwenden Sie die Tastenkombination Strg + V zum Einfügen`,
        shortcut: $localize`:@@dataGrid.contextMenu.paste.shortcut:Strg + V`,
      },
    ];

    if (currentSelection === 'row') {
      const rowMenuItems = [
        ...commonMenuItems,
        'separator',
        {
          icon: '<span class="material-symbols-outlined">push_pin</span>',
          name: $localize`:@@dataGrid.contextMenu.pinRow:Zeile anheften`,
          subMenu: [
            {
              name: $localize`:@@dataGrid.contextMenu.pinOnTop:Pin oben`,
              action: () => this.pinRow(params, 'top'),
              checked:
                this.workingConfig.pinnedTopRow?.id === params.node.data.id,
            },
            {
              name: $localize`:@@dataGrid.contextMenu.pinOnBottom:Pin unten`,
              action: () => this.pinRow(params, 'bottom'),
              checked:
                this.workingConfig.pinnedBottomRow?.id === params.node.data.id,
            },
            {
              name: $localize`:@@dataGrid.withoutPin:kein Pin`,
              action: () => this.unpinRow(params),
            },
          ],
        },
        'separator',
        {
          icon: '<span class="material-symbols-outlined">add</span>',
          name: $localize`:@@dataGrid.addNewRow:Neue Zeile hinzufügen`,
          action: () => this.addNewRow(params),
        },
        {
          icon: '<span class="material-symbols-outlined">delete</span>',
          name: $localize`:@@dataGrid.deleteRow:Zeile löschen`,
          action: () => this.deleteRow(params),
        },
      ];

      return rowMenuItems;
    } else if (currentSelection === 'column') {
      const columnMenuItems = [
        ...commonMenuItems,
        'separator',
        {
          icon: '<span class="material-symbols-outlined">view_column_2</span>',
          name: $localize`:@@dataGrid.symbolOutlined:Spaltengröße anpassen`,
          action: () => this.gridApi.sizeColumnsToFit(),
        },
        {
          icon: '<span class="material-symbols-outlined">push_pin</span>',
          name: $localize`:@@dataGrid.pushPin:Spalte anheften`,
          subMenu: [
            {
              name: $localize`:@@dataGrid.pinRight:Pin rechts`,
              action: () => this.pinColumn(params, 'right'),
              checked: params.column.getColDef().pinned === 'right',
            },
            {
              name: $localize`:@@dataGrid.pinLeft:Pin links`,
              action: () => this.pinColumn(params, 'left'),
              checked: params.column.getColDef().pinned === 'left',
            },
            {
              name: $localize`:@@dataGrid.withoutPin:kein Pin`,
              action: () => this.pinColumn(params, null),
            },
          ],
        },
        'separator',
        {
          icon: '<span class="material-symbols-outlined">add</span>',
          name: $localize`:@@datagrid.addNewColumn:Neue Spalte hinzufügen`,
          action: () => this.addNewColumn(params),
        },
        {
          icon: '<span class="material-symbols-outlined">delete</span>',
          name: $localize`:@@dataGrid.deleteColumn:Spalte löschen`,
          action: () => this.deleteColumn(params),
        },
      ];

      return columnMenuItems;
    } else {
      const cellRangeMenuItems = [
        ...commonMenuItems,
        {
          name: $localize`:@@dataGrid.contextMenu.delete:Löschen`,
          icon: '<span class="material-symbols-outlined">delete</span>',
          shortcut: $localize`:@@dataGrid.contextMenu.delete.shortcut:Entf`,
          action: () => this.clearCellRangeValue(),
        },
      ];

      return cellRangeMenuItems;
    }
  }
}
