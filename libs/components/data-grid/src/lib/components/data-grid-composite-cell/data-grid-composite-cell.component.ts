import { ICellRendererAngularComp } from '@ag-grid-community/angular';
import { ICellRendererParams } from '@ag-grid-community/core';
import { Component } from '@angular/core';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { ObjectHelper } from '@fincloud/utils';
import { CellData } from '../../models/cell-data';
import { CellValueRepresentation } from '../../models/cell-value-representation';
import { FormulaErrorMessage } from '../../models/formula-error-message';
import { FormulaEngineService } from '../../services/formula-engine.service';
import { FORMULA_ERRORS } from '../../utils/formula-errors';

@Component({
  selector: 'ui-data-grid-composite-cell',
  templateUrl: './data-grid-composite-cell.component.html',
  styleUrls: ['./data-grid-composite-cell.component.scss'],
})
export class DataGridCompositeCellComponent
  implements ICellRendererAngularComp
{
  cellData: CellData;
  cellParams: ICellRendererParams;
  dateFormatAndTime: string;

  get formatting() {
    return this.cellData?.formatting;
  }

  get backgroundColor() {
    return this.cellData?.formatting?.backgroundColor;
  }

  get genericValue() {
    if (!this.cellDataType) {
      return true;
    }

    if (
      !!this.cellDataType &&
      this.cellDataType !== 'currency' &&
      this.cellDataType !== 'decimal' &&
      this.cellDataType !== 'number' &&
      this.cellDataType !== 'percent' &&
      this.cellDataType !== 'date' &&
      this.cellDataType !== 'checkbox'
    ) {
      return true;
    }

    // this case the value is not a number hence shouldn't be represented in any of the number-like types
    if (
      !this.isValueNumeric &&
      (this.cellDataType === 'currency' ||
        this.cellDataType === 'decimal' ||
        this.cellDataType === 'number' ||
        this.cellDataType === 'percent')
    ) {
      return true;
    }

    return false;
  }

  get isValueNumeric() {
    return ObjectHelper.isNumeric(this.cellValue?.result);
  }

  get cellValue() {
    let v: CellValueRepresentation = {
      result: this.cellData?.value?.toString(),
      error: null,
    };

    if (v?.result?.toString().startsWith('=')) {
      const parsed = this.formulaEngine.parseExpression(this.cellData?.value);
      v = {
        result: parsed?.result?.toString(),
        error: parsed?.error,
      };
    }

    return v;
  }

  get cellDataType() {
    return this.cellData?.formatting?.dataType?.toLowerCase();
  }

  get errorTooltip(): FormulaErrorMessage {
    return FORMULA_ERRORS[this.cellValue?.error];
  }

  constructor(
    private formulaEngine: FormulaEngineService,
    private regionalSettings: RegionalSettingsService,
  ) {
    this.dateFormatAndTime = `${this.regionalSettings.dateFormat}`;
  }

  agInit(params: ICellRendererParams): void {
    this.cellParams = params;
    this.cellData = params.data[params.colDef.field];
  }

  setValue(value: unknown) {
    this.cellParams.setValue(value);
  }

  refresh(params: ICellRendererParams<any, unknown>): boolean {
    this.cellData = params.data[params.colDef.field];
    return true;
  }
}
